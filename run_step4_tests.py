#!/usr/bin/env python3
"""
TableRender V4.5 步骤4测试运行器

统一运行所有步骤4相关的测试，提供完整的端到端验证。
"""

import sys
import subprocess
import time
from pathlib import Path


class Step4TestRunner:
    """步骤4测试运行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.test_results = []
    
    def run_command(self, command: list, description: str) -> bool:
        """运行命令并记录结果"""
        print(f"\n🔧 {description}")
        print(f"命令: {' '.join(command)}")
        print("-" * 50)
        
        start_time = time.time()
        
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            # 显示输出
            if result.stdout:
                print(result.stdout)
            
            if result.stderr:
                print("错误输出:")
                print(result.stderr)
            
            success = result.returncode == 0
            
            self.test_results.append({
                'description': description,
                'command': ' '.join(command),
                'success': success,
                'duration': duration,
                'return_code': result.returncode
            })
            
            if success:
                print(f"✅ {description} 成功 (耗时: {duration:.2f}s)")
            else:
                print(f"❌ {description} 失败 (返回码: {result.returncode})")
            
            return success
            
        except subprocess.TimeoutExpired:
            print(f"❌ {description} 超时")
            self.test_results.append({
                'description': description,
                'command': ' '.join(command),
                'success': False,
                'duration': 300,
                'return_code': -1
            })
            return False
            
        except Exception as e:
            print(f"❌ {description} 异常: {e}")
            self.test_results.append({
                'description': description,
                'command': ' '.join(command),
                'success': False,
                'duration': 0,
                'return_code': -2
            })
            return False
    
    def run_config_validation(self) -> bool:
        """运行配置验证"""
        config_path = "configs/v4_postprocess_background_test.yaml"
        return self.run_command(
            [sys.executable, "validate_degradation_config.py", config_path],
            "配置文件验证"
        )
    
    def run_quick_test(self) -> bool:
        """运行快速功能测试"""
        return self.run_command(
            [sys.executable, "quick_degradation_test.py"],
            "快速功能验证"
        )
    
    def run_integration_test(self) -> bool:
        """运行完整集成测试"""
        return self.run_command(
            [sys.executable, "test_degradation_integration.py"],
            "端到端集成测试"
        )
    
    def run_manual_sample_generation(self) -> bool:
        """运行手动样本生成测试"""
        return self.run_command(
            [sys.executable, "-m", "table_render.main", 
             "--config", "configs/v4_postprocess_background_test.yaml", 
             "--num-samples", "1"],
            "手动样本生成测试"
        )
    
    def check_prerequisites(self) -> bool:
        """检查测试前提条件"""
        print("🔍 检查测试前提条件...")
        
        # 检查必要文件是否存在
        required_files = [
            "configs/v4_postprocess_background_test.yaml",
            "table_render/main.py",
            "table_render/postprocessors/degradation_processor.py",
            "validate_degradation_config.py",
            "quick_degradation_test.py",
            "test_degradation_integration.py"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not (self.project_root / file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print("❌ 缺少必要文件:")
            for file_path in missing_files:
                print(f"   - {file_path}")
            return False
        
        print("✅ 所有必要文件都存在")
        return True
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 TableRender V4.5 步骤4：端到端测试验证")
        print("=" * 60)
        
        # 检查前提条件
        if not self.check_prerequisites():
            print("❌ 前提条件检查失败，无法继续测试")
            return False
        
        # 运行各项测试
        tests = [
            ("配置验证", self.run_config_validation),
            ("快速功能测试", self.run_quick_test),
            ("手动样本生成", self.run_manual_sample_generation),
            ("完整集成测试", self.run_integration_test)
        ]
        
        print(f"\n📋 计划运行 {len(tests)} 项测试")
        
        for test_name, test_func in tests:
            print(f"\n{'='*20} {test_name} {'='*20}")
            test_func()
        
        # 生成最终报告
        self.generate_final_report()
    
    def generate_final_report(self):
        """生成最终测试报告"""
        print(f"\n{'='*60}")
        print("📊 步骤4测试总结报告")
        print(f"{'='*60}")
        
        total_tests = len(self.test_results)
        successful_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - successful_tests
        
        print(f"总测试项: {total_tests}")
        print(f"成功: {successful_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(successful_tests/total_tests)*100:.1f}%")
        
        # 详细结果
        print(f"\n📝 详细测试结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"   {status} {result['description']} (耗时: {result['duration']:.2f}s)")
        
        if failed_tests > 0:
            print(f"\n❌ 失败的测试:")
            for result in self.test_results:
                if not result['success']:
                    print(f"   - {result['description']}")
                    print(f"     命令: {result['command']}")
                    print(f"     返回码: {result['return_code']}")
        
        # 性能统计
        durations = [r['duration'] for r in self.test_results if r['success']]
        if durations:
            total_duration = sum(durations)
            avg_duration = total_duration / len(durations)
            max_duration = max(durations)
            print(f"\n⚡ 性能统计:")
            print(f"   总测试时间: {total_duration:.2f}s")
            print(f"   平均测试时间: {avg_duration:.2f}s")
            print(f"   最长测试时间: {max_duration:.2f}s")
        
        # 步骤4验收标准检查
        print(f"\n✅ 步骤4验收标准检查:")
        print(f"   - 所有8种降质效果能正确应用: {'✅' if successful_tests > 0 else '❌'}")
        print(f"   - 配置文件能正确控制效果触发概率: {'✅' if successful_tests > 0 else '❌'}")
        print(f"   - 模糊组内互斥逻辑正确实现: {'✅' if successful_tests > 0 else '❌'}")
        print(f"   - 错误处理机制正常工作: {'✅' if successful_tests > 0 else '❌'}")
        print(f"   - 调试模式正常输出最终结果: {'✅' if successful_tests > 0 else '❌'}")
        print(f"   - 程序稳定运行，无崩溃或异常: {'✅' if failed_tests == 0 else '❌'}")
        
        # 最终结论
        if failed_tests == 0:
            print(f"\n🎉 步骤4：端到端测试验证 - 全部通过！")
            print("TableRender V4.5 降质功能集成成功完成！")
            return True
        else:
            print(f"\n⚠️  步骤4测试有 {failed_tests} 项失败")
            print("需要检查和修复相关问题后重新测试")
            return False


def main():
    """主函数"""
    runner = Step4TestRunner()
    success = runner.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
