#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
降质效果固定参数研究工具

采用固定参数值（而非范围）来研究doc_degradation模块中不同参数对图像降质效果的影响，
消除随机性，确保结果可复现。
专注于表格图像的降质效果研究。
"""

import os
import cv2
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Any, Tuple, Optional
import argparse
import json
from pathlib import Path
import copy

# 导入降质模块
from third_parties.doc_degradation import StrategyScheduler
from third_parties.doc_degradation.core.degradation_ocr_pipe import DegradationOCRPipe
from third_parties.doc_degradation.core.strategy import DegradationType
from third_parties.doc_degradation.configs.config import DegradationConfig


class DegradationFixedParameterStudy:
    """降质固定参数研究类"""
    
    def __init__(self, output_dir: str = "parameter_study_results"):
        """
        初始化参数研究工具
        
        Args:
            output_dir: 输出目录路径
        """
        self.scheduler = StrategyScheduler()
        self.degradation_pipe = DegradationOCRPipe(self.scheduler)
        self.output_dir = output_dir
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 默认配置
        self.default_config = DegradationConfig()
    
    def _apply_degradation_with_fixed_param(self, 
                                           image: np.ndarray, 
                                           degradation_type: DegradationType,
                                           fixed_params: Dict[str, Any],
                                           choose_flag: Dict[str, Any] = None) -> np.ndarray:
        """
        应用单个降质效果，使用固定参数而非范围
        
        Args:
            image: 输入图像
            degradation_type: 降质类型
            fixed_params: 固定参数字典，将范围值转换为固定值
            choose_flag: 特殊选择标志
            
        Returns:
            降质后的图像
        """
        # 将所有范围参数转换为固定值（相同的起始和结束值）
        fixed_config = {}
        for param_name, param_value in fixed_params.items():
            # 检查是否为范围参数（通常以_range结尾）
            if isinstance(param_value, (list, tuple)) and param_name.endswith('_range'):
                # 将范围参数转换为具有相同起始和结束值的元组
                if isinstance(param_value, (int, float)):
                    fixed_config[param_name] = (param_value, param_value)
                else:
                    # 如果已经是元组，则保留原样
                    fixed_config[param_name] = param_value
            else:
                # 非范围参数直接复制
                fixed_config[param_name] = param_value
        
        return self.degradation_pipe.process(
            image=image,
            custom_strategies=[(degradation_type, fixed_config)],
            probabilities=[1.0],  # 100%应用效果
            choose_flag=choose_flag,
            seed=42  # 固定种子进一步确保可重复性
        )
    
    def study_single_parameter(self, 
                              image_path: str,
                              degradation_type: DegradationType,
                              parameter_name: str,
                              parameter_values: List[Any],
                              fixed_params: Dict[str, Any] = None) -> str:
        """
        研究单个参数变化对降质效果的影响，使用固定值而非范围
        
        Args:
            image_path: 输入图像路径
            degradation_type: 降质类型
            parameter_name: 参数名称
            parameter_values: 参数值列表（每个值都是固定值，非范围）
            fixed_params: 其他固定参数
            
        Returns:
            结果保存路径
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # BGR转RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 获取降质类型名称
        degradation_name = degradation_type.name
        
        # 创建输出目录
        study_dir = os.path.join(self.output_dir, f"{degradation_name}_{parameter_name}_study")
        os.makedirs(study_dir, exist_ok=True)
        
        # 创建结果图像
        fig, axes = plt.subplots(1, len(parameter_values) + 1, figsize=(4 * (len(parameter_values) + 1), 4))
        
        # 显示原始图像
        axes[0].imshow(image)
        axes[0].set_title("原始图像")
        axes[0].axis("off")
        
        results = []
        
        # 对每个参数值应用降质效果
        for i, param_value in enumerate(parameter_values):
            # 准备固定参数配置
            params = copy.deepcopy(fixed_params) if fixed_params else {}
            
            # 如果参数名以_range结尾，将值设为(值,值)形式
            if parameter_name.endswith('_range'):
                params[parameter_name] = (param_value, param_value)
            else:
                params[parameter_name] = param_value
            
            # 应用降质效果
            degraded_image = self._apply_degradation_with_fixed_param(
                image.copy(), 
                degradation_type,
                params
            )
            
            # 保存单张结果
            result_path = os.path.join(study_dir, f"{parameter_name}_{param_value}.jpg")
            cv2.imwrite(result_path, cv2.cvtColor(degraded_image, cv2.COLOR_RGB2BGR))
            
            # 添加到展示图
            axes[i+1].imshow(degraded_image)
            
            # 构造显示标题
            display_value = param_value
            if isinstance(param_value, tuple) and len(param_value) == 2 and param_value[0] == param_value[1]:
                display_value = param_value[0]  # 如果是(x,x)形式的元组，只显示x
                
            axes[i+1].set_title(f"{parameter_name} = {display_value}")
            axes[i+1].axis("off")
            
            # 记录结果
            results.append({
                "parameter_value": str(param_value),
                "result_path": result_path
            })
        
        # 保存对比图
        comparison_path = os.path.join(study_dir, f"{parameter_name}_comparison.jpg")
        plt.tight_layout()
        plt.savefig(comparison_path, dpi=300)
        
        # 保存研究信息
        info = {
            "degradation_type": degradation_name,
            "parameter_name": parameter_name,
            "parameter_values": [str(v) for v in parameter_values],
            "fixed_parameters": fixed_params,
            "comparison_image": comparison_path,
            "individual_results": results
        }
        
        with open(os.path.join(study_dir, "study_info.json"), "w", encoding="utf-8") as f:
            json.dump(info, f, indent=2, ensure_ascii=False)
        
        return study_dir
    
    def study_parameter_pair(self, 
                           image_path: str, 
                           degradation_type: DegradationType,
                           param1_name: str,
                           param1_values: List[Any],
                           param2_name: str,
                           param2_values: List[Any],
                           fixed_params: Dict[str, Any] = None) -> str:
        """
        创建参数对研究，使用固定值替代范围值
        
        Args:
            image_path: 输入图像路径
            degradation_type: 降质类型
            param1_name: 第一个参数名称
            param1_values: 第一个参数值列表（固定值）
            param2_name: 第二个参数名称
            param2_values: 第二个参数值列表（固定值）
            fixed_params: 其他固定参数
            
        Returns:
            结果保存路径
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # BGR转RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 获取降质类型名称
        degradation_name = degradation_type.name
        
        # 创建输出目录
        study_dir = os.path.join(self.output_dir, f"{degradation_name}_pair_study")
        os.makedirs(study_dir, exist_ok=True)
        
        # 创建网格图像
        rows = len(param1_values) + 1  # +1 为标题行
        cols = len(param2_values) + 1  # +1 为标题列
        
        fig, axes = plt.subplots(rows, cols, figsize=(4 * cols, 4 * rows))
        
        # 设置标题行和列
        axes[0, 0].axis('off')  # 左上角单元格为空
        
        # 确保行列标题显示固定值而非范围
        def get_display_value(param_name, value):
            if isinstance(value, tuple) and len(value) == 2 and value[0] == value[1]:
                return value[0]  # 如果是(x,x)形式的元组，只显示x
            return value
        
        for i, val1 in enumerate(param1_values):
            display_val1 = get_display_value(param1_name, val1)
            axes[i+1, 0].text(0.5, 0.5, f"{param1_name}={display_val1}", 
                            ha='center', va='center', fontsize=12, fontweight='bold')
            axes[i+1, 0].axis('off')
        
        for j, val2 in enumerate(param2_values):
            display_val2 = get_display_value(param2_name, val2)
            axes[0, j+1].text(0.5, 0.5, f"{param2_name}={display_val2}", 
                            ha='center', va='center', fontsize=12, fontweight='bold')
            axes[0, j+1].axis('off')
        
        # 填充网格
        results = []
        for i, val1 in enumerate(param1_values):
            for j, val2 in enumerate(param2_values):
                # 准备固定参数配置
                params = copy.deepcopy(fixed_params) if fixed_params else {}
                
                # 对于范围参数，将值设为(值,值)形式
                if param1_name.endswith('_range'):
                    params[param1_name] = (val1, val1) if not isinstance(val1, tuple) else val1
                else:
                    params[param1_name] = val1
                    
                if param2_name.endswith('_range'):
                    params[param2_name] = (val2, val2) if not isinstance(val2, tuple) else val2
                else:
                    params[param2_name] = val2
                
                # 应用降质效果
                degraded_image = self._apply_degradation_with_fixed_param(
                    image.copy(), 
                    degradation_type,
                    params
                )
                
                # 保存单张结果
                display_val1 = get_display_value(param1_name, val1)
                display_val2 = get_display_value(param2_name, val2)
                result_path = os.path.join(study_dir, f"{param1_name}_{display_val1}_{param2_name}_{display_val2}.jpg")
                cv2.imwrite(result_path, cv2.cvtColor(degraded_image, cv2.COLOR_RGB2BGR))
                
                # 添加到网格图
                axes[i+1, j+1].imshow(degraded_image)
                axes[i+1, j+1].axis("off")
                
                # 记录结果
                results.append({
                    f"{param1_name}": str(display_val1),
                    f"{param2_name}": str(display_val2),
                    "result_path": result_path
                })
        
        # 保存网格图
        grid_path = os.path.join(study_dir, f"{param1_name}_{param2_name}_grid.jpg")
        plt.tight_layout()
        plt.savefig(grid_path, dpi=300)
        
        # 保存研究信息
        info = {
            "degradation_type": degradation_name,
            "parameter1_name": param1_name,
            "parameter1_values": [str(get_display_value(param1_name, v)) for v in param1_values],
            "parameter2_name": param2_name,
            "parameter2_values": [str(get_display_value(param2_name, v)) for v in param2_values],
            "fixed_parameters": fixed_params,
            "grid_image": grid_path,
            "individual_results": results
        }
        
        with open(os.path.join(study_dir, "grid_study_info.json"), "w", encoding="utf-8") as f:
            json.dump(info, f, indent=2, ensure_ascii=False)
        
        return study_dir
    
    def study_degradation_types(self,
                              image_path: str,
                              save_dir: str = "degradation_types"):
        """
        研究所有降质类型的基本效果
        
        Args:
            image_path: 输入图像路径
            save_dir: 保存目录名
            
        Returns:
            结果保存路径
        """
        # 读取图像
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图像: {image_path}")
        
        # BGR转RGB
        image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # 创建输出目录
        study_dir = os.path.join(self.output_dir, save_dir)
        os.makedirs(study_dir, exist_ok=True)
        
        # 获取所有降质类型
        degradation_types = [t for t in DegradationType]
        
        # 创建结果图像
        rows = (len(degradation_types) + 2) // 3  # 每行3个图像
        cols = min(3, len(degradation_types) + 1)
        
        fig, axes = plt.subplots(rows, cols, figsize=(5 * cols, 5 * rows))
        axes = axes.flatten()
        
        # 显示原始图像
        axes[0].imshow(image)
        axes[0].set_title("原始图像", fontsize=14)
        axes[0].axis("off")
        
        results = []
        
        # 对每种降质类型应用效果
        for i, deg_type in enumerate(degradation_types):
            # 应用降质效果，使用固定参数
            degraded_image = self._apply_degradation_with_fixed_param(
                image.copy(), 
                deg_type,
                {}  # 使用默认参数，但内部会将范围转为固定值
            )
            
            # 保存单张结果
            result_path = os.path.join(study_dir, f"{deg_type.name}.jpg")
            cv2.imwrite(result_path, cv2.cvtColor(degraded_image, cv2.COLOR_RGB2BGR))
            
            # 添加到展示图
            axes[i+1].imshow(degraded_image)
            axes[i+1].set_title(deg_type.name, fontsize=14)
            axes[i+1].axis("off")
            
            # 记录结果
            results.append({
                "degradation_type": deg_type.name,
                "result_path": result_path
            })
        
        # 隐藏多余的子图
        for i in range(len(degradation_types) + 1, len(axes)):
            axes[i].axis("off")
        
        # 保存对比图
        comparison_path = os.path.join(study_dir, "all_degradation_types.jpg")
        plt.tight_layout()
        plt.savefig(comparison_path, dpi=300)
        
        # 保存研究信息
        info = {
            "study_type": "degradation_types",
            "comparison_image": comparison_path,
            "individual_results": results
        }
        
        with open(os.path.join(study_dir, "degradation_types_info.json"), "w", encoding="utf-8") as f:
            json.dump(info, f, indent=2, ensure_ascii=False)
        
        return study_dir


# 创建示例研究函数
def run_example_studies(image_path, output_dir="fixed_parameter_results"):
    """
    运行示例参数研究
    
    Args:
        image_path: 输入图像路径
        output_dir: 输出目录
    """
    study_tool = DegradationFixedParameterStudy(output_dir)
    
    # 1. 研究所有降质类型的基本效果
    print("研究所有降质类型的基本效果...")
    study_tool.study_degradation_types(image_path)
    
    # 2. 研究模糊参数
    print("\n研究高斯模糊sigma参数...")
    study_tool.study_single_parameter(
        image_path=image_path,
        degradation_type=DegradationType.BLUR,
        parameter_name="gaussian_blur_sigma_range",
        parameter_values=[1, 2, 3, 4, 5, 6],
        fixed_params={"gaussian_blur_kernel_range": (5, 5)}  # 固定核大小为5
    )
    
    # 3. 研究高斯模糊核大小参数
    print("\n研究高斯模糊核大小参数...")
    study_tool.study_single_parameter(
        image_path=image_path,
        degradation_type=DegradationType.BLUR,
        parameter_name="gaussian_blur_kernel_range",
        parameter_values=[(3, 3), (5, 5), (7, 7), (9, 9), (11, 11)],
        fixed_params={"gaussian_blur_sigma_range": (3, 3)}  # 固定sigma为3
    )
    
    # 4. 研究JPEG压缩质量参数
    print("\n研究JPEG压缩质量参数...")
    study_tool.study_single_parameter(
        image_path=image_path,
        degradation_type=DegradationType.JPEG,
        parameter_name="jpeg_quality_range",
        parameter_values=[(5, 5), (10, 10), (20, 20), (30, 30), (50, 50), (80, 80)]
    )
    
    # 5. 研究噪声强度参数
    print("\n研究高斯噪声参数...")
    study_tool.study_single_parameter(
        image_path=image_path,
        degradation_type=DegradationType.NOISE,
        parameter_name="gaussian_noise_std_range",
        parameter_values=[1, 2, 5, 10, 15, 20]
    )
    
    # 6. 研究文字断裂参数 - 对表格线和文字影响明显
    print("\n研究文字断裂参数对...")
    study_tool.study_parameter_pair(
        image_path=image_path,
        degradation_type=DegradationType.LINE_BROKEN,
        param1_name="line_broken_alpha_range",
        param1_values=[(1.8, 1.8), (2.0, 2.0), (2.2, 2.2), (2.4, 2.4)],
        param2_name="line_broken_beta_range",
        param2_values=[(1.0, 1.0), (1.2, 1.2), (1.4, 1.4)]
    )
    
    # 7. 研究不均匀光照参数
    print("\n研究不均匀光照参数...")
    study_tool.study_single_parameter(
        image_path=image_path,
        degradation_type=DegradationType.UNEVEN_LIGHTING,
        parameter_name="uneven_lighting_range",
        parameter_values=[(0.1, 0.1), (0.2, 0.2), (0.3, 0.3), (0.4, 0.4), (0.5, 0.5)]
    )
    
    print(f"\n所有示例研究已完成! 结果保存在 {output_dir} 目录")


def main():
    parser = argparse.ArgumentParser(description="文档图像降质固定参数研究工具")
    
    subparsers = parser.add_subparsers(dest="command", help="命令")
    
    # 单参数研究命令
    param_study = subparsers.add_parser("param_study", help="单个参数研究")
    param_study.add_argument("--image", type=str, required=True, help="输入图像路径")
    param_study.add_argument("--degradation", type=str, required=True, 
                          help="降质类型，例如: BLUR, NOISE, JPEG等")
    param_study.add_argument("--parameter", type=str, required=True, help="参数名称")
    param_study.add_argument("--values", type=str, required=True, 
                          help="参数值列表，JSON格式")
    param_study.add_argument("--fixed_params", type=str, default="{}", 
                          help="固定参数，JSON格式")
    param_study.add_argument("--output", type=str, default="fixed_parameter_results", 
                          help="输出目录")
    
    # 参数对研究命令
    pair_study = subparsers.add_parser("pair_study", help="参数对研究")
    pair_study.add_argument("--image", type=str, required=True, help="输入图像路径")
    pair_study.add_argument("--degradation", type=str, required=True, 
                         help="降质类型，例如: BLUR, NOISE, JPEG等")
    pair_study.add_argument("--param1", type=str, required=True, help="第一个参数名称")
    pair_study.add_argument("--values1", type=str, required=True, 
                         help="第一个参数值列表，JSON格式")
    pair_study.add_argument("--param2", type=str, required=True, help="第二个参数名称")
    pair_study.add_argument("--values2", type=str, required=True, 
                         help="第二个参数值列表，JSON格式")
    pair_study.add_argument("--fixed_params", type=str, default="{}", 
                         help="固定参数，JSON格式")
    pair_study.add_argument("--output", type=str, default="fixed_parameter_results", 
                         help="输出目录")
    
    # 所有类型研究命令
    all_types = subparsers.add_parser("all_types", help="研究所有降质类型")
    all_types.add_argument("--image", type=str, required=True, help="输入图像路径")
    all_types.add_argument("--output", type=str, default="fixed_parameter_results", 
                        help="输出目录")
    
    # 示例研究命令
    examples = subparsers.add_parser("examples", help="运行示例参数研究")
    examples.add_argument("--image", type=str, required=True, help="输入图像路径")
    examples.add_argument("--output", type=str, default="fixed_parameter_results", 
                       help="输出目录")
    
    args = parser.parse_args()
    
    if args.command == "param_study":
        # 创建参数研究工具
        study_tool = DegradationFixedParameterStudy(args.output)
        
        # 解析参数
        degradation_type = getattr(DegradationType, args.degradation)
        parameter_values = json.loads(args.values)
        fixed_params = json.loads(args.fixed_params)
        
        # 执行研究
        result_dir = study_tool.study_single_parameter(
            image_path=args.image,
            degradation_type=degradation_type,
            parameter_name=args.parameter,
            parameter_values=parameter_values,
            fixed_params=fixed_params
        )
        
        print(f"参数研究完成，结果保存在: {result_dir}")
        
    elif args.command == "pair_study":
        # 创建参数研究工具
        study_tool = DegradationFixedParameterStudy(args.output)
        
        # 解析参数
        degradation_type = getattr(DegradationType, args.degradation)
        param1_values = json.loads(args.values1)
        param2_values = json.loads(args.values2)
        fixed_params = json.loads(args.fixed_params)
        
        # 执行研究
        result_dir = study_tool.study_parameter_pair(
            image_path=args.image,
            degradation_type=degradation_type,
            param1_name=args.param1,
            param1_values=param1_values,
            param2_name=args.param2,
            param2_values=param2_values,
            fixed_params=fixed_params
        )
        
        print(f"参数对研究完成，结果保存在: {result_dir}")
    
    elif args.command == "all_types":
        # 创建参数研究工具
        study_tool = DegradationFixedParameterStudy(args.output)
        
        # 执行研究
        result_dir = study_tool.study_degradation_types(
            image_path=args.image
        )
        
        print(f"所有降质类型研究完成，结果保存在: {result_dir}")
        
    elif args.command == "examples":
        # 运行示例研究
        run_example_studies(args.image, args.output)
        
    else:
        parser.print_help()


if __name__ == "__main__":
    main()
