# TableRender: 产品需求文档 (PRD)

*本文档概述了TableRender（一个高度可控的表格图像合成工具）的产品需求。它是一份动态文档，旨在供人类开发者和AI智能体阅读和理解。*

---

## 1. 产品概述

### 1.1. 项目目标

开发一个强大的端到端表格图像合成工具，能够生成海量且多样化的表格图像数据集，并附带精确的结构化标注。该工具旨在为表格检测、结构识别和信息提取模型的训练与评估提供基础组件。

### 1.2. 解决的核心问题

现有的表格识别模型普遍缺乏大规模、高质量且多样化的训练数据。手动创建此类数据集成本高昂且耗时。本项目旨在通过一种程序化的方式生成既真实又高度可定制的合成表格数据，以解决这一难题，弥合合成数据的简单性与现实世界的复杂性之间的鸿沟。

### 1.3. 目标用户

- 从事文档智能、OCR和表格理解的AI研究人员与工程师。
- 需要合成数据进行模型训练和验证的数据科学家。

### 1.4. 核心设计原则

- **高度可控性 (High Controllability)**: 用户必须能通过一个统一的配置对象，对表格生成的各个方面（包括结构、内容和样式）进行细粒度控制。
- **丰富多样性 (Rich Diversity)**: 工具必须能够生成模仿真实世界文档的、种类繁多的表格样式和结构。
- **数据保真度 (Data Fidelity)**: 生成的图像和标注必须是高质量的。渲染过程应稳定可靠，标注必须达到像素级准确且结构正确。
- **可复现性 (Reproducibility)**: 每个生成的样本都必须能从其配置元数据中100%复现，以确保实验的一致性和可追溯性。
- **可扩展性 (Extensibility)**: 系统设计应着眼于未来的扩展，允许轻松地添加新的渲染引擎、内容源或样式选项。

---

## 2. 系统架构与渲染策略

### 2.1. 总体架构

系统将采用**架构B（多核心渲染）**作为其长期目标，以确保对各种输入格式（如HTML、LaTeX）的最高保真度。然而，开发将分阶段进行。

- **第一阶段：HTML/CSS渲染引擎。** 初期开发将完全专注于创建一个基于现代Web技术的、稳定可靠的渲染流水线。该引擎将负责处理从程序化定义和CSV数据生成表格，并使用无头浏览器将其渲染成图像。
- **第二阶段：LaTeX渲染引擎。** (未来范围) 将为渲染基于LaTeX的表格开发一个独立的流水线。
- **第三阶段：可扩展核心。** (未来范围) 将重构核心系统以支持可插拔的渲染引擎，从而轻松集成新格式。

### 2.2. 第一阶段渲染流水线

HTML/CSS渲染流水线是初始产品版本的核心。它遵循以下逻辑步骤：

1.  **配置解析**：系统接收用户定义的配置对象，该对象指导所有后续步骤。
2.  **内容准备**：根据配置，从程序化生成（格式化数据、源文件中的随机文本）或指定的CSV文件中获取内容。
3.  **逻辑结构生成**：基于配置中的概率规则，确定表格的逻辑结构（行、列、合并单元格、表头）。
4.  **内部数据模型构建**：创建一个干净的、与格式无关的表格内部表示。该模型将表格的逻辑与其表现形式分离。
5.  **HTML/CSS生成**：将内部数据模型转换为HTML和CSS代码。为每个单元格（`<td>`/`<th>`）注入唯一ID，以便进行精确标注。
6.  **无头浏览器渲染**：将生成的HTML/CSS加载到无头浏览器（如通过Selenium/Playwright控制的Chrome）中，以渲染最终的表格图像。
7.  **标注生成**：系统使用注入的单元格ID查询渲染后的页面，以获取精确的边界框（`bbox`）坐标。此信息与内部数据模型相结合，生成最终的JSON标注，包括逻辑位置（`lloc`）和边框样式。
8.  **图像增强（可选）**：可以将“干净”的渲染图像送入增强模块，以应用逼真的失真效果（如模糊、噪声、透视变换）。
9.  **输出打包**：将最终的图像及其对应的JSON标注保存到结构化的输出目录中。

---

## 3. 功能需求

### 3.1. 输入：配置对象

系统的主要输入将是一个单一、全面的配置对象（例如，Python字典或JSON文件）。该对象是生成任务的蓝图。

- **格式**：配置将采用键值对结构。
- **控制**：它将管理生成的所有方面，包括内容源、表格结构、样式和输出设置。
- **API**：核心功能将直接接受此配置对象。第一阶段不实现流式/链式API，也不提供预设模板。

### 3.2. 输出：标准化与结构化

工具将为每个生成的批次生成一组文件，并以清晰、易于使用的方式进行组织。

- **文件类型**：对于每个样本，工具会生成：
    - 一个图像文件（如 `.png`）。
    - 一个JSON标注文件。
    - 一个用于可复现性的元数据文件。
- **目录结构**：
    - 主输出目录由用户指定。
    - 在内部，样本将被分组到子目录中，每个子目录最多包含500个样本（例如 `000001-000500/`, `000501-010000/` 等）。
    - 在每个子目录中，将有三个平行的文件夹：
        - `images/`：包含所有 `.png` 文件。
        - `annotations/`：包含所有标注 `.json` 文件。
        - `metadata/`：包含所有元数据 `.json` 文件，其结构与另外两个文件夹镜像对应。
- **标注格式**：标注JSON将严格遵守 `sample_json.json` 格式，详细说明每个 `cell` 的 `bbox`、`lloc`、`border` 和 `content`。
- **元数据格式**：元数据JSON将存储用于生成相应样本的完整配置对象，包括随机种子，以确保完全的可复现性。

### 3.3. 内容生成

工具必须支持多种可配置的单元格内容来源。

- **程序化生成**：
    - **格式化数据**：必须支持生成具有真实格式的常见数据类型（如日期、货币、百分比）。
    - **从文件读取文本**：必须支持从用户提供的 `.txt` 文件中随机抽样行或词来填充单元格。
- **CSV数据**：
    - 必须支持使用指定 `.csv` 文件中的数据填充表格结构。
    - **不匹配处理**：当CSV的维度与目标表格结构不匹配时，其行为将由配置中的概率决定（例如，截断多余的列、将单元格留白或用其他内容填充）。

### 3.4. 结构生成

表格的逻辑结构是高度可配置的，并由概率规则驱动。

- **维度**：行数和列数可以指定为固定数字或一个随机范围。
- **单元格合并**：
    - 工具必须同时支持行合并（`rowspan`）和列合并（`colspan`）。
    - 合并单元格的出现将由配置中的概率设置控制（例如 `merge_probability: 0.1`）。
    - 合并的范围（例如，跨越的最大行/列数）也应是可配置的。
- **表头**：系统将支持创建多级表头（复杂表头）。
- **区域无关性**：在第一阶段，系统不会区分 `<thead>`、`<tbody>` 和 `<tfoot>`。结构完全由内部模型生成的 `<th>` 和 `<td>` 标签的排列来定义。使用 `<th>` 还是 `<td>` 可以作为结构配置的一部分。

### 3.5. 样式生成

样式是生成逼真表格的关键组成部分，并将以分层方式应用（全局 -> 列 -> 行 -> 单元格）。

- **字体**：
    - 工具将从用户指定的目录中加载字体文件（`.ttf`, `.otf`）。
    - 字体族、大小、字重（如粗体）和字形（如斜体）将是可配置的。
- **颜色**：单元格的背景色和文本颜色都将是可配置的。
- **对齐**：单元格内文本的水平（左、中、右）和垂直（上、中、下）对齐都将是可配置的。
- **边框**：边框样式将简化为对单元格四个边的布尔状态（开/关）。
- **内边距**：可以指定单元格的内边距。
- **斑马纹**：将提供一个特定的布尔选项，用于为行应用交替的背景色。
- **行/列尺寸**：行高和列宽可以被随机拉伸或压缩，以创建不依赖于内容大小的非均匀布局。

### 3.6. 边界情况与容错性

系统必须稳定可靠，并能优雅地处理边缘情况而不会崩溃。

- **内容溢出**：如果内容超出了单元格的尺寸，其行为将由配置决定：
    - **选项A**：截断文本。
    - **选项B**：文本换行并自动扩展行高。
    - A和B之间的选择可以是一个随机结果。
- **配置冲突**：如果冲突的样式规则应用于同一元素（例如，一个单元格同时位于一个高亮行和一个高亮列中），将随机选择一个规则优先。系统将记录一个警告但不会停止执行。

---

## 4. 非功能性需求

### 4.1. 性能

系统应为批量生成进行优化。虽然单个图像的生成时间不是主要指标，但生成数千个样本的总体吞吐量应是合理的。特别是标注生成步骤，必须高效并避免DOM查询瓶颈。

### 4.2. 可扩展性

架构应该是模块化的。像内容生成器、结构生成器和样式映射器这样的关键组件应被设计为独立的模块，以方便未来的增强。即使在第一阶段不构建完整的插件系统，也应在初始实现时考虑这些模块的接口。

### 4.3. 易用性

该工具将通过一个清晰的配置对象进行操作。虽然配置可能很复杂，但其结构应该是逻辑清晰且有良好文档记录的。

### 4.4. 可靠性

该工具必须能够在无人值守的情况下运行大型生成任务而不会崩溃。对配置冲突和边界情况的错误处理对此要求至关重要。

---

## 5. 范围

### 5.1. 第一阶段范围内

- 端到端的HTML/CSS渲染流水线。
- 第3节中列出的所有功能需求，包括结构、内容和样式的概率性生成。
- 生成“干净”的数字原生图像，以及可选的用于增强的后处理。
- 高度结构化、可复现的输出，图像、标注和元数据分离。

### 5.2. 第一阶段范围外

- **LaTeX渲染引擎**：这是一个计划中的未来功能。
- **高级GUI/API**：不会开发图形用户界面或流式/链式API。
- **高级预设/模板**：系统不会有内置的模板系统。
- **数据库集成**：从数据库获取内容不在范围内。
- **实时生成**：该工具是为离线批量处理而设计的，不适用于实时应用。
