{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0013f863", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from typing import List\n", "import json\n", "from pdflatex import PDFLaTeX\n", "from pdf2image import convert_from_bytes\n", "import pyvips\n", "import cv2 as cv\n", "import re\n", "from subprocess import run, PIPE\n", "import glob\n", "import itertools\n", "import time\n", "import cProfile\n", "from clibrary import library\n", "import multiprocessing\n", "np.random.seed(1)"]}, {"cell_type": "markdown", "id": "e7f87bbc", "metadata": {}, "source": ["### Template Generator\n", "Generate table with certain properties (borderless,boredered and so on). Results in a string representing the tabular in latex. The required input is a pandas dataframe.\n", "- borderless (no borders)\n", "- bordered header (only header bordered)\n", "- bordered header and bottom (only header and end bordered)\n", "- bordered internal columns (only internal column bordered)\n", "- bordered columns (columns bordered)\n", "- bordered (grid table )"]}, {"cell_type": "code", "execution_count": 2, "id": "4776a8db", "metadata": {}, "outputs": [], "source": ["class TemplateGenerator:\n", "    ''' remove top rule, mid rule , bottom rule'''\n", "    def clean_template(self,template:str)->str:\n", "        line = template.replace(\"\\\\toprule\\n\",\"\")\n", "        line = line.replace(\"\\\\midrule\\n\",\"\")\n", "        line = line.replace(\"\\\\bottomrule\\n\",\"\")\n", "        return line\n", "    \n", "    ''' format the column names '''\n", "    def format_columns(self,df)->List[str]:\n", "        cols = list(df.columns)\n", "        formated_cols = [''.join(['[',col,']']) for col in cols]\n", "        return formated_cols\n", "    \n", "    ''' format column names to bold '''\n", "    def format_template(self,template:str)->str:\n", "        line = template.replace(\"[\",\"\\\\textbf{\")\n", "        line = line.replace(\"]\",\"}\")\n", "        return line\n", "        \n", "    ''' no borders'''\n", "    def borderless(self,df)->str:\n", "        columns = self.format_columns(df)\n", "        column_format = \"\".join([\"c\" for i in range(len(df.columns))])\n", "        template = df.to_latex(index=False,column_format=column_format,header=columns)\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        return template\n", "    \n", "    ''' no border except in header'''\n", "    def bordered_header(self,df)->str:\n", "        columns = self.format_columns(df)\n", "        column_format = \"\".join([\"c\" for i in range(len(df.columns))])\n", "        template = df.to_latex(index=False,column_format=column_format,header=columns)\n", "        template = template.replace(\"\\\\toprule\",\"\\hline\")\n", "        template = template.replace(\"\\\\midrule\",\"\\hline\")\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        return template\n", "    \n", "    ''' no border except in header and bottom'''\n", "    def bordered_header_bottom(self,df)->str:\n", "        columns = self.format_columns(df)\n", "        column_format = \"\".join([\"c\" for i in range(len(df.columns))])\n", "        template = df.to_latex(index=False,column_format=column_format,header=columns)\n", "        template = template.replace(\"\\\\toprule\",\"\\hline\")\n", "        template = template.replace(\"\\\\midrule\",\"\\hline\")\n", "        template = template.replace(\"\\\\bottomrule\",\"\\hline\")\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        return template\n", "    \n", "    ''' only internal columns bordered '''\n", "    def bordered_internal_columns(self,df)->str:\n", "        columns = self.format_columns(df)\n", "        column_format = \"\".join([\"|c\" if i!=0 else \"c\" for i in range(len(df.columns))])\n", "        template = df.to_latex(index=False,column_format=column_format,header=columns)\n", "        template = template.replace(\"\\\\midrule\",\"\\hline\")\n", "        template = template.replace(\"\\\\bottomrule\",\"\\hline\")\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        return template\n", "    \n", "    ''' only columns bordered '''\n", "    def bordered_columns(self,df)->str:\n", "        columns = self.format_columns(df)\n", "        column_format = \"\".join([\"c|\" for i in range(len(df.columns))])\n", "        column_format = \"\".join([\"|\",column_format])\n", "        template = df.to_latex(index=False,column_format=column_format,header=columns)\n", "        template = template.replace(\"\\\\toprule\",\"\\hline\")\n", "        template = template.replace(\"\\\\midrule\",\"\\hline\")\n", "        template = template.replace(\"\\\\bottomrule\",\"\\hline\")\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        return template\n", "    \n", "    ''' partialy bordered'''\n", "    def partialy_bordered(self,df)->str:\n", "        template = self.bordered(df)\n", "        lines = template.split(\"\\n\")\n", "        idx = []\n", "        for i in range(len(lines)):\n", "            line = lines[i]\n", "            found = line.find(\"hline\")\n", "            if found!=-1:\n", "                idx.append(i)\n", "        mid = len(idx)//2\n", "        replaced = []\n", "        for i in idx[2:mid]:\n", "            temp = lines[i]\n", "            temp = temp.replace('\\\\hline','')\n", "            lines[i] = temp\n", "        template = \"\\n\".join(lines)\n", "        return template\n", "    \n", "    ''' all borders '''\n", "    def bordered(self,df)->str:\n", "        columns = self.format_columns(df)\n", "        column_format = \"\".join([\"c|\" for i in range(len(df.columns))])\n", "        column_format = \"\".join([\"|\",column_format])\n", "        template = df.to_latex(index=False,column_format=column_format,header=columns)\n", "        template = template.replace(\"\\\\toprule\",\"\\hline\")\n", "        template = self.clean_template(template)\n", "        template = template.replace(\"\\\\\\\\\",\"\\\\\\\\ \\hline\")\n", "        template = self.format_template(template)\n", "        return template     "]}, {"cell_type": "markdown", "id": "c44256fa", "metadata": {}, "source": ["### Complex Template Generator\n", "Generate a table with a table in one of its columns. Results string representing the tabular in latex. The required input is 2 pandas dataframes (inner and outer). Extends the Template Generator.\n", "- Embedded (bordered outer table in which one of the columns contains a tables generate by template generator )"]}, {"cell_type": "code", "execution_count": 3, "id": "5ca720c8", "metadata": {}, "outputs": [], "source": ["class ComplexTemplateGenerator(TemplateGenerator):\n", "    ''' all borders for outer tables '''\n", "    def bordered(self,df_c)->str:\n", "        df = df_c.copy()\n", "        r_index = np.random.randint(0,df.shape[0])\n", "        c_index = np.random.randint(0,df.shape[1])\n", "        index = c_index\n", "        df.iat[r_index,c_index] = '*'\n", "        columns = self.format_columns(df)\n", "        column_format = [\"c\" for i in range(len(df.columns))]\n", "        column_format[index] = '@{}c@{}'\n", "        column_format = \"|\".join(column_format)\n", "        column_format = \"\".join([\"|\",column_format,\"|\"])\n", "        template = df.to_latex(index=False,column_format=column_format,header=columns)\n", "        template = template.replace(\"\\\\toprule\",\"\\hline\")\n", "        template = self.clean_template(template)\n", "        template = template.replace(\"\\\\\\\\\",\"\\\\\\\\ \\hline\")\n", "        template = self.format_template(template)\n", "        return template\n", "    \n", "    ''' format a complex(embedded) template '''\n", "    def format_complex_template(self,outer_template:str,inner_template:str):\n", "        lines = outer_template.split(\"\\n\")\n", "        for i in range(len(lines)):\n", "            line = lines[i]\n", "            if line.find(\"*\")!=-1:\n", "                lines[i] = lines[i].replace(\"*\",inner_template)\n", "            \n", "        template = \"\\n\".join(lines)\n", "        return template\n", "    \n", "    ''' format inner template'''\n", "    def format_inner_template(self,template):\n", "        border = '\\\\fcolorbox{white}{white!30}{\\n'\n", "        template = \"\".join([border,template,'}'])\n", "        return template\n", "    \n", "    def embedded(self,df_outer,df_inner,f)->str:\n", "        df_outer = df_outer.astype(str)\n", "        df_inner = df_inner.astype(str)\n", "        outer_str = self.bordered(df_outer)\n", "        inner_str = f(df_inner)\n", "        inner_str = self.format_inner_template(inner_str)\n", "        template = self.format_complex_template(outer_str,inner_str)\n", "        return template"]}, {"cell_type": "markdown", "id": "08b34804", "metadata": {}, "source": ["### Table Generator\n", "\n", "Wraps a generated tabular around a table and adds a caption. Also draws borders for use later in making table mask. The expected input is a latex format string coming from the template or complex template generator."]}, {"cell_type": "code", "execution_count": 4, "id": "fe84e8db", "metadata": {}, "outputs": [], "source": ["class TableGenerator:\n", "    def __init__(self):\n", "        self.top = '\\\\begin{table}[h]\\n\\centering\\n\\caption{*}\\n\\\\vspace{1mm}\\n'\n", "        self.bottom = '\\\\end{table}'\n", "        \n", "    def outlined_table(self,template:str,caption:str=\"Table\",mode=\"simple\")->str:\n", "        if mode==\"simple\":\n", "            top = self.top.replace(\"*\",caption)\n", "            separation = '\\\\setlength{\\\\fboxsep}{1pt}\\n'\n", "            border = \"\".join([separation,'\\\\fcolorbox{red}{white}{\\n'])\n", "            bottom = self.bottom\n", "            table = \"\".join([top,border,template[:-1],'}\\n',bottom])\n", "            return table\n", "                             \n", "        elif mode==\"complex\":\n", "            top = self.top.replace(\"*\",caption)\n", "            border = '\\\\fcolorbox{red}{white}{\\n'\n", "            bottom = self.bottom\n", "            table = \"\".join([top,border,template[:-1],'}\\n',bottom])\n", "            table = table.replace(\"fcolorbox{white}\",\"fcolorbox{red}\")\n", "            return table\n", "    \n", "    def outlined_tables(self,templates:List[str],mode=\"simple\")->List[str]:\n", "        tables = []\n", "        for template in templates:\n", "            table = self.outlined_table(template,mode=mode)\n", "            tables.append(table)\n", "        return tables\n", "        \n", "    def table(self,template:str,caption:str=\"Table\")->str:\n", "        top = self.top.replace(\"*\",caption)\n", "        separation = '\\\\setlength{\\\\fboxsep}{1pt}\\n'\n", "        border = \"\".join([separation,'\\\\fcolorbox{white}{white}{\\n'])\n", "        bottom = self.bottom\n", "        table = \"\".join([top,border,template[:-1],'}\\n',bottom])\n", "        return table\n", "    \n", "    def tables(self,templates:List[str])->List[str]:\n", "        tables = []\n", "        for template in templates:\n", "            table = self.table(template)\n", "            tables.append(table)\n", "        return tables\n", "        "]}, {"cell_type": "markdown", "id": "b3e9c2db", "metadata": {}, "source": ["### Table Writer\n", "Uses a default latex template and writes tables to it (Includes writing random text), which can later be converted to pdf."]}, {"cell_type": "code", "execution_count": 5, "id": "4fc011a8", "metadata": {}, "outputs": [], "source": ["class TableWriter:    \n", "    def __init__(self):\n", "        self.begin = '\\\\begin{document}\\n'\n", "        self.end = '\\\\end{document}'\n", "        \n", "        with open('templates/latex/template.tex','r') as f:\n", "            template = \"\".join(f.readlines())\n", "            \n", "        with open('templates/latex/config.json','r') as f:\n", "            paragraphs = json.load(f)\n", "            \n", "        self.template = template\n", "        self.paragraphs = []\n", "        for key in paragraphs:\n", "            text = \"\\n\".join(paragraphs[key])\n", "            self.paragraphs.append(text)\n", "            \n", "    ''' just write a single table '''\n", "    def write_single(self,table:str)->str:\n", "        text = self.paragraphs[0]\n", "        data = \"\".join([self.begin,'\\n',text,'\\n',table,\"\\n\",self.end])\n", "        template = \"\".join([self.template,'\\n',data])\n", "        return template\n", "    \n", "    ''' write multiple tables '''\n", "    def write(self,tables:List[str])->str:\n", "        data = '\\n'\n", "        texts = self.paragraphs\n", "        for i in range(len(tables)):\n", "            table = tables[i]\n", "            if i<len(texts):\n", "                text = texts[i]\n", "            else:\n", "                j = np.random.randint(0,len(texts)-1)\n", "                text = texts[j]\n", "            data = \"\".join([data,text,'\\n\\n',table,'\\n\\n'])\n", "        \n", "        data = \"\".join([data,texts[-1],'\\n\\n'])\n", "        data = \"\".join([self.begin,data,\"\\n\",self.end])\n", "        template = \"\".join([self.template,data])\n", "        return template    "]}, {"cell_type": "markdown", "id": "8f5821a1", "metadata": {}, "source": ["### Pdf Generator\n", "Generate a pdf in bytes from a given latex string. "]}, {"cell_type": "code", "execution_count": 6, "id": "956bd2ed", "metadata": {}, "outputs": [], "source": ["class PdfGenerator:\n", "    ''' returns pdf bytes '''\n", "    def pdf(self,latex_str:str)->bytes:\n", "        pdfl = PDFLaTeX.from_binarystring(bytes(latex_str,'utf-8'),\"job\")\n", "        pdf, log, completed_process = pdfl.create_pdf(keep_pdf_file=False)\n", "        return pdf\n", "    \n", "    ''' return list of pdf bytes'''\n", "    def pdfs(self,latex_strs:List[str])->List[bytes]:\n", "        pdfs = []\n", "        for latex_str in latex_strs:\n", "            pdf = self.pdf(latex_str)\n", "            pdfs.append(pdf)\n", "        return pdfs"]}, {"cell_type": "markdown", "id": "0d4f3def", "metadata": {}, "source": ["### Pdf 2 Imgs\n", "Convert pdf to images, so we can convert to images and generate masks."]}, {"cell_type": "code", "execution_count": 7, "id": "e0a05728", "metadata": {}, "outputs": [], "source": ["class PdfToImg:\n", "    ''' pdf to img '''\n", "    def pdf_to_img(self,pdf_bytes:bytes):\n", "        img = convert_from_bytes(pdf_bytes,dpi=200)[0]\n", "        return np.asarray(img,dtype=np.uint8)\n", "    \n", "    ''' pdfs to imgs '''\n", "    def pdfs_to_imgs(self,pdfs:List[bytes]):\n", "        pdf_imgs = []\n", "        for pdf in pdfs:\n", "            img_pdf = self.pdf_to_img(pdf)\n", "            pdf_imgs.append(img_pdf)\n", "        return pdf_imgs"]}, {"cell_type": "markdown", "id": "6d00bfe4", "metadata": {}, "source": ["### Transformer\n", "Transform a given image , to try and mimic real world data of scanned images. The following transforms applicable\n", "- Gaussian Blur $k$ (kernel size), $(k,k)$ \n", "- Scale $(sx,sy)$ \n", "- Rotate $\\theta$"]}, {"cell_type": "code", "execution_count": 8, "id": "ae8bac15", "metadata": {}, "outputs": [], "source": ["class Transformer:\n", "    ''' blur (! later must investigate scan effect)'''\n", "    def blur(self,img,kernel):\n", "        sigma_x,sigma_y = 2,2\n", "        blurred_img = cv.<PERSON><PERSON><PERSON>(img,kernel,sigma_x,sigma_y)\n", "        return blurred_img\n", "    \n", "    ''' rotate '''\n", "    def rotate(self,img,theta:float,border=(255,255,255)):\n", "        height, width = img.shape[:2]\n", "        center = (width/2, height/2)\n", "        rotate_matrix = cv.getRotationMatrix2D(center=center, angle=theta, scale=1)\n", "        rotated_img = cv.warpAffine(src=img, M=rotate_matrix, dsize=(width, height),borderValue=border)\n", "        return rotated_img\n", "    \n", "    ''' dirtify data by applying sequence of transformations'''\n", "    def dirtify(self,img,k:int,s_x:int,s_y:int,theta:float,mask:bool):\n", "        m,n = img.shape[:2]\n", "        m,n = int(s_y*m),int(s_x*n)\n", "        dim = (n,m)\n", "        if not mask:\n", "            x = self.blur(img,kernel=(k,k))\n", "            y = cv.resize(x, dim, interpolation = cv.INTER_AREA)\n", "            z = self.rotate(y,theta=theta)\n", "        else:\n", "            x = img\n", "            y = cv.resize(x, dim, interpolation = cv.INTER_AREA)\n", "            z = self.rotate(y,theta=theta,border=(0,0,0))\n", "        return z"]}, {"cell_type": "markdown", "id": "a4610266", "metadata": {}, "source": ["### Mask Generator\n", "From the given pdf image generate table mask (label). This is done as follows.\n", "- Compute absolute difference between raw pdf img and outlined img\n", "- Apply adaptive thresholding to resulting image to obtain binary image\n", "- Detect external contours in binary image and fill bounding box regions of contours"]}, {"cell_type": "code", "execution_count": 9, "id": "be93493e", "metadata": {}, "outputs": [], "source": ["class PreProcessor:\n", "    ''' grayscale the image '''\n", "    def grayscale(self,img):\n", "        grayscaled = cv.cvtColor(img, cv.COLOR_RGB2GRAY)\n", "        return grayscaled\n", "    \n", "    ''' thresholding the image to a binary image '''\n", "    def threshold(self,img,mode='adaptive'):\n", "        if mode == 'adaptive':\n", "            thresh = cv.adaptiveThreshold(img, 255, 1, 1, 11, 2)\n", "            return thresh\n", "        elif mode=='otsu':\n", "            _,thresh = cv.threshold(img,128,255,cv.THRESH_BINARY |cv.THRESH_OTSU)\n", "            return thresh\n", "\n", "    ''' apply preprocessing steps ''' \n", "    def preprocess(self,img):\n", "        grayscaled = self.grayscale(img)\n", "        thresholded = self.threshold(grayscaled)\n", "        return thresholded\n", "    \n", "class MaskGenerator:\n", "    def __init__(self):\n", "        self.preprocessor = PreProcessor()\n", "       \n", "    ''' fill region with specified contours '''\n", "    def fill(self,shape,contours):\n", "        filled_binary_mask = np.zeros(shape,dtype=np.uint8)\n", "        bounding_rectangles = []\n", "        for contour in contours:\n", "            rect = cv.boundingRect(contour)\n", "            x,y,w,h = rect\n", "            filled_binary_mask[y:y+h,x:x+w] = 255\n", "        return filled_binary_mask\n", "            \n", "    ''' fill the mask '''\n", "    def fill_mask(self,mask):\n", "        binary_mask = self.preprocessor.preprocess(mask)\n", "        contours, _ = cv.findContours(binary_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)\n", "        binary_mask = self.fill(binary_mask.shape,contours)\n", "        return binary_mask\n", "    \n", "    ''' generate table mask taking difference of 2 imgs '''\n", "    def mask(self,raw_img,outlined_img):\n", "        x = raw_img\n", "        y = outlined_img\n", "        mask = abs(x-y)\n", "        filled_mask = self.fill_mask(mask)\n", "        return filled_mask\n", "    \n", "    def masks(self,raw_imgs,outlined_imgs):\n", "        masks = []\n", "        for i in range(len(raw_imgs)):\n", "            x = raw_imgs[i]\n", "            y = outlined_imgs[i]\n", "            mask = self.mask(x,y)\n", "            masks.append(mask)\n", "        return masks"]}, {"cell_type": "markdown", "id": "252bed73", "metadata": {}, "source": ["### Table Structure Generator\n", "Generate xml like string representing table structure. This is done by making system call to latexml."]}, {"cell_type": "code", "execution_count": 10, "id": "2ee7b72b", "metadata": {}, "outputs": [], "source": ["class StructureGenerator:\n", "    def __init__(self):\n", "        self.expression = '<table|<row|<cell|</cell>|</row>|</table>'\n", "        self.regex = re.compile(self.expression)\n", "\n", "    ''' markup representing table '''\n", "    def structure(self,table:str)->str:\n", "        with open('tmp/tmp.tex','w') as f:\n", "            f.write(table)\n", "        p = run(['tralics','-output_dir','tmp','tmp/tmp.tex'],stdout=PIPE)\n", "        with open('tmp/tmp.xml','r') as f:\n", "            xml = \"\".join(f.readlines())\n", "        lines = re.findall(self.regex, xml)\n", "        lines = lines[1:-1]\n", "        lines = [lines[i]+'>' if lines[i].find(\">\")==-1 else lines[i] for i in range(len(lines))]\n", "        structure = \"\\n\".join(lines)\n", "        return structure\n", "    \n", "    ''' generate table structures '''\n", "    def structures(self,tables:List[str])->List[str]:\n", "        structures = []\n", "        for table in tables:\n", "            structure = self.structure(table)\n", "            structures.append(structure)\n", "        return structures"]}, {"cell_type": "markdown", "id": "d114446a", "metadata": {}, "source": ["### Metadata Generator\n", "Metadata to be generated includes the following\n", "- Number of tables - This is counted by counting the number of connected components in the mask (table regions should be disjoint regions of white pixels)\n", "- Bounding boxes (of tables) - Generated by detecting external contours and returning nounding box\n", "- XML strings of structure - latex string representation as arg yo structure generator"]}, {"cell_type": "code", "execution_count": 11, "id": "0e0c34ce", "metadata": {}, "outputs": [], "source": ["class MetadataGenerator(StructureGenerator):    \n", "    ''' number of tables from table mask '''\n", "    def number_of_tables(self,table_mask)->int:\n", "        num,_ = cv.connectedComponents(table_mask)\n", "        return num-1\n", "    \n", "    ''' sort the contours top to bottom '''\n", "    def sort(self,contours):\n", "        y_values = []\n", "        for contour in contours:\n", "            x,y,w,h = cv.boundingRect(contour)\n", "            y_values.append(y)\n", "        y_values = np.array(y_values)\n", "        idx = np.argsort(y_values)\n", "        sorted_contours = [contours[i] for i in idx]\n", "        return sorted_contours\n", "    \n", "    ''' bounding boxes '''\n", "    def bounding_boxes(self,table_mask):\n", "        contours , _ = cv.findContours(table_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)\n", "        sorted_contours = self.sort(contours)\n", "        boxes = []\n", "        for contour in sorted_contours:\n", "            box = cv.boundingRect(contour)\n", "            boxes.append(box)\n", "        return boxes\n", "    \n", "    ''' generate metadata '''\n", "    def metadata(self,table_mask,tables:List[str]):\n", "        num_tables = self.number_of_tables(table_mask)\n", "        bboxes = self.bounding_boxes(table_mask)\n", "        bboxes_data = [bbox for bbox in bboxes]\n", "        structures = self.structures(tables)\n", "        result = {'no':num_tables,'bounding_boxes':bboxes_data,'structures':structures}\n", "        return result"]}, {"cell_type": "markdown", "id": "2e6f1e5d", "metadata": {}, "source": ["### Data Source\n", "A pool of dataframes in which table data is pulled from. When data is needed for a table following happens\n", "a call to sample with number of datafframes needed and mode (important for limits). The sample randomly selects dataframes and samples each randomly. Then shuffles dataframes order."]}, {"cell_type": "code", "execution_count": 1, "id": "522aac9b", "metadata": {}, "outputs": [], "source": ["class DataSource:\n", "    def __init__(self,path):\n", "        self.data = self.load(path)\n", "        self.N = len(self.data)\n", "        self.MIN_ROWS = 2\n", "        self.MIN_OUTER_ROWS = 4\n", "        self.MAX_ROWS = 30\n", "        self.MIN_COLS = 2\n", "        self.MAX_COLS = 7\n", "        self.MAX_COLS_INNER = 3\n", "        self.MAX_COLS_OUTER = 3\n", "        self.STATE = 42\n", "        \n", "    ''' prepare dataframe '''\n", "    def prepare(self,df):\n", "        df.columns = self.format_columns(df.columns)\n", "        nan_value = float(\"NaN\")\n", "        df.replace(\"\", \"-\", inplace=True)\n", "        df.replace(\"NaN\",\"-\", inplace=True)\n", "        df.dropna(inplace=True)\n", "        df = df.iloc[:300,:]\n", "        df = df.astype(str)\n", "        return df\n", "    \n", "    ''' load dataframes '''\n", "    def load(self,path):\n", "        fnames = glob.glob(path)\n", "        data = []\n", "        for fname in fnames:\n", "            df = pd.read_csv(fname,encoding=\"utf-8\")\n", "            df = self.prepare(df)\n", "            for c in df.columns:\n", "                df[c] = df[c].apply(self.reduce)\n", "            data.append(df)\n", "        return data\n", "    \n", "    ''' reduce rows '''\n", "    def reduce(self,x:str)->str:\n", "        if type(x) is not str:\n", "            return x\n", "        if len(x)>12:\n", "            return x[:12]\n", "        return x\n", "    \n", "    ''' format column headers '''\n", "    def format_columns(self,columns):\n", "        new_columns = []\n", "        for c in columns:\n", "            c = c.replace(\"_\",\" \")\n", "            c = c.title()\n", "            if len(c)>12:\n", "                c = c.split(' ')[0][:12]\n", "            new_columns.append(c)\n", "        return new_columns\n", "    \n", "    ''' shuffle '''\n", "    def shuffle(self):\n", "        p = np.random.permutation(self.N)\n", "        self.data = [self.data[p[i]] for i in range(len(p))]\n", "    \n", "    ''' select a dataframe '''\n", "    def select(self,i,rows,cols):\n", "        df = self.data[i].copy()\n", "        df = df.sample(frac=1,random_state=self.STATE)\n", "        df = df.iloc[:rows,np.random.permutation(cols)]\n", "        df = df.iloc[:,:cols]\n", "        return df\n", "        \n", "    ''' sample for simple tables'''\n", "    def sample(self,n,mode=0):\n", "        sample = []\n", "        p = np.random.permutation(n)\n", "        max_rows = self.MAX_ROWS//n\n", "        if mode == 0:\n", "            max_cols = 7\n", "        else:\n", "            max_rows = max(self.MIN_OUTER_ROWS,max_rows//2)\n", "            max_cols = 3\n", "        for i in range(n):\n", "            rows = int(np.random.uniform(self.MIN_ROWS,max_rows+1))\n", "            cols = int(np.random.uniform(self.MIN_COLS,max_cols+1))\n", "            df = self.select(p[i],rows,cols)\n", "            sample.append(df)\n", "        self.shuffle()\n", "        return sample"]}, {"cell_type": "markdown", "id": "835154f7", "metadata": {}, "source": ["### Pipeline\n", "Combines all of the above to generate dataset the steps to generate a dataset are.\n", "- Given list of types generate templates (templates func)\n", "- From templates generate resulting pdf,img,mask (datum func)\n", "- Distort datum (applies transforms to img and mask)\n", "- Annotate distort datum (uses metadata generator)"]}, {"cell_type": "code", "execution_count": 13, "id": "810586e1", "metadata": {}, "outputs": [], "source": ["class LatexGeneratorPipeline:\n", "    def __init__(self,path):\n", "        self.templator = TemplateGenerator()\n", "        self.c_templator = ComplexTemplateGenerator()\n", "        self.table_generator = TableGenerator()\n", "        self.table_writer = TableWriter()\n", "        self.pdf_generator = PdfGenerator()\n", "        self.pdf_to_img = PdfToImg()\n", "        self.mask_generator = MaskGenerator()\n", "        self.transformer = Transformer()\n", "        self.metadata_generator = MetadataGenerator()\n", "        self.data_source = DataSource(path)\n", "        self.template_funcs = [\n", "            self.templator.borderless,\n", "            self.templator.bordered_header,\n", "            self.templator.bordered_header_bottom,\n", "            self.templator.bordered_internal_columns,\n", "            self.templator.bordered_columns,\n", "            self.templator.partialy_bordered,\n", "            self.templator.bordered,\n", "            self.c_templator.embedded\n", "        ]\n", "        \n", "    ''' generate simple templates from given dfs and types'''\n", "    def templates(self,types:List[int])->List[str]:\n", "        n = len(types)\n", "        templates = []\n", "        sample = self.data_source.sample(n,0)\n", "        for i in range(n):\n", "            index = types[i]\n", "            if index!=7:\n", "                df = sample[i]\n", "                func = self.template_funcs[index]\n", "                template = func(df)\n", "                templates.append(template)\n", "            else:\n", "                if n == 1:\n", "                    df_outer,df_inner = self.data_source.sample(2,1)[:2]\n", "                else:\n", "                    df_outer,df_inner = self.data_source.sample(n,1)[:2]\n", "                df_inner = df_inner.iloc[:len(df_outer)//2,:]\n", "                func = self.template_funcs[index]\n", "                func_inner = self.template_funcs[np.random.randint(0,7)]\n", "                template = func(df_outer,df_inner,func_inner)\n", "                templates.append(template)              \n", "        return templates\n", "    \n", "    ''' generate a single datapoint {mask,pdf,tables,img} '''\n", "    def datum(self,types:List[int])->dict:\n", "        templates = self.templates(types)\n", "        # step 1 pdf and outlined pdf\n", "        tables = self.table_generator.tables(templates)\n", "        outlined_tables = self.table_generator.outlined_tables(templates)\n", "        tex = self.table_writer.write(tables)\n", "        pdf = self.pdf_generator.pdf(tex)\n", "        outlined_table = self.table_writer.write(outlined_tables)\n", "        outlined_pdf = self.pdf_generator.pdf(outlined_table)        \n", "        \n", "        # step 2 images and masks \n", "        pdfs = [pdf]\n", "        outlined_pdfs = [outlined_pdf]\n", "        imgs = self.pdf_to_img.pdfs_to_imgs(pdfs)\n", "        outlined_imgs = self.pdf_to_img.pdfs_to_imgs(outlined_pdfs)\n", "        masks = self.mask_generator.masks(imgs,outlined_imgs)\n", "        \n", "        # step 3 make results\n", "        results = {\"mask\":masks[0],\"img\":imgs[0],\"pdf\":pdfs[0],'tables':tables}\n", "        \n", "        return results\n", "    \n", "    ''' apply transformation using params to dirty data (img and mask) '''\n", "    def distort_datum(self,datum:dict,k:int=7,s_x:int=1,s_y:int=1,theta:float=0)->dict:\n", "        img = datum['img']\n", "        mask = datum['mask']\n", "        img = self.transformer.dirtify(img,k,s_x,s_y,theta,False)\n", "        mask = self.transformer.dirtify(mask,k,s_x,s_y,theta,True)\n", "        return img,mask\n", "    \n", "    ''' label a datum '''\n", "    def label(self,datum:dict):\n", "        mask = datum['mask']\n", "        tables = datum['tables']\n", "        metadata = self.metadata_generator.metadata(mask,tables)\n", "        return metadata\n", "    \n", "    ''' combinations of tables '''\n", "    def generate_combinations(self,types:List[str]):\n", "        combinations = []\n", "        counts = {i:0 for i in types}\n", "        for i in range(1,4):\n", "            c = itertools.combinations(types, i)\n", "            for j in c:\n", "                combinations.append(list(j))\n", "                for k in j:\n", "                    counts[k] = counts[k]+1\n", "        return counts,combinations\n", "    \n", "    ''' save datum along with its annotation '''\n", "    def save(self,datum:dict,annotation:dict,config):\n", "        _id = annotation['id']\n", "        img_path = config['img_path']+_id+'.png'\n", "        mask_path = config['mask_path']+_id+'.png'\n", "        annotation_path = config['annotation_path']+_id+'.json'\n", "        \n", "        # save img and mask\n", "        img = datum['img']\n", "        mask = datum['mask']\n", "        \n", "        cv.imwrite(img_path,img)\n", "        cv.imwrite(mask_path,mask)\n", "        \n", "        # save annotation\n", "        with open(annotation_path,'w') as f:\n", "            json.dump(annotation,f)\n", "        \n", "    ''' generate dataset '''    \n", "    def generate_data(self,config):\n", "        sample_size = config[\"sample_size\"]\n", "        types = config[\"types\"]\n", "        N = sample_size\n", "        counts,combinations = self.generate_combinations(types)\n", "        n = len(combinations)\n", "        stats = {i:0 for i in types}\n", "        _id = 0\n", "        for i in range(N):\n", "            idx = int(np.random.uniform(0,n))\n", "            sub_types = combinations[idx]\n", "            for c in sub_types:\n", "                stats[c] = stats[c]+1\n", "            datum = self.datum(sub_types)\n", "            theta = np.random.uniform(-2,2)\n", "            img,mask = self.distort_datum(datum,theta=theta)\n", "            datum['img'] = img\n", "            datum['mask'] = mask\n", "            label = self.label(datum)\n", "            label[\"id\"] = str(_id)\n", "            self.save(datum,label,config)\n", "            _id =_id+1\n", "        return _id,stats"]}, {"cell_type": "markdown", "id": "8625eb4c", "metadata": {}, "source": ["### <font color=\"red\">Check Bottlenecks </font>"]}, {"cell_type": "code", "execution_count": 14, "id": "4cb3403a", "metadata": {}, "outputs": [], "source": ["def test_datum(pipeline,types,output):\n", "    prof = cProfile.Profile()\n", "    prof.enable()\n", "    pipeline.datum(types)\n", "    prof.disable()\n", "    prof.dump_stats(\"\".join(['profiles/',output,'.profile']))\n", "    \n", "def test_distort(pipeline,types,output):\n", "    datum = pipeline.datum(types)\n", "    prof = cProfile.Profile()\n", "    prof.enable()\n", "    theta = np.random.uniform(-2,2)\n", "    pipeline.distort_datum(datum,theta=theta)\n", "    prof.disable()\n", "    prof.dump_stats(\"\".join(['profiles/',output,'.profile']))\n", "    \n", "def test_label(pipeline,types,output):\n", "    datum = pipeline.datum(types)\n", "    prof = cProfile.Profile()\n", "    prof.enable()\n", "    pipeline.label(datum)\n", "    prof.disable()\n", "    prof.dump_stats(\"\".join(['profiles/',output,'.profile']))\n", "    \n", "def test_pipeline(pipeline,config,output):\n", "    prof = cProfile.Profile()\n", "    prof.enable()\n", "    pipeline.generate_data(config)\n", "    prof.disable()\n", "    prof.dump_stats(\"\".join(['profiles/',output,'.profile']))"]}, {"cell_type": "code", "execution_count": 15, "id": "55b989e5", "metadata": {}, "outputs": [], "source": ["config = {\n", "\"sample_size\":10,\n", "\"types\":[0,1,2,3,4,5,6,7],\n", "\"types_map\":{\n", "    \"0\":\"Borderless\",\n", "    \"1\":\"Bordered Header\",\n", "    \"2\":\"Bordered Header Bottom\",\n", "    \"3\":\"Bordered Internal Columns\",\n", "    \"4\":\"Bordered Columns\",\n", "    \"5\":\"Partially Bordered\",\n", "    \"6\":\"Bordered\",\n", "    \"7\":\"Embedded\"\n", "    },\n", "\"img_path\":\"data/latex/imgs/\",\n", "\"mask_path\":\"data/latex/masks/\",\n", "\"annotation_path\":\"data/latex/annotations/\"\n", "}"]}, {"cell_type": "code", "execution_count": 16, "id": "53a735a1", "metadata": {}, "outputs": [], "source": ["path = 'sources/*.csv'\n", "pipeline = LatexGeneratorPipeline(path)\n", "types = config['types']\n", "_,combinations = pipeline.generate_combinations(types)\n", "sub_types = combinations[-1]"]}, {"cell_type": "code", "execution_count": 23, "id": "d7aa21fe", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["<table>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "<row>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "<cell>\n", "</cell>\n", "</row>\n", "</table>\n"]}], "source": ["s =\"<table>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n<row>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n<cell>\\n</cell>\\n</row>\\n</table>\"\n", "print(s)"]}, {"cell_type": "code", "execution_count": 17, "id": "1df0b465", "metadata": {}, "outputs": [], "source": ["test_datum(pipeline,sub_types,'latex-datum')"]}, {"cell_type": "code", "execution_count": 18, "id": "6dddb042", "metadata": {}, "outputs": [], "source": ["test_distort(pipeline,sub_types,'latex-distort')"]}, {"cell_type": "code", "execution_count": 19, "id": "9e1189e6", "metadata": {}, "outputs": [], "source": ["test_label(pipeline,sub_types,'latex-label')"]}, {"cell_type": "code", "execution_count": 20, "id": "bfce92d0", "metadata": {}, "outputs": [], "source": ["test_pipeline(pipeline,config,'latex-pipeline-serial')"]}, {"cell_type": "code", "execution_count": 21, "id": "07228861", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f33f5b456a0>"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["datum = pipeline.datum([7])\n", "label = pipeline.label(datum)\n", "plt.imshow(datum['img'])"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}