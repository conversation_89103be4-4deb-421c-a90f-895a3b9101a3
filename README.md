# TableRender

TableRender 是一个高度可控的表格图像合成工具，用于生成表格图像数据集并附带精确的结构化标注。V4.3版本新增**表格透明度融合**功能，让表格自然融入背景图像，显著提升视觉真实感。

## 📖 快速导航

- [V4 - 鲁棒性与图像增强](#-版本-4---鲁棒性与图像增强-最新) - 后处理系统，背景图合成，透视变换优化，智能边距控制，**表格透明度融合(V4.3)**
- [V3.4 - 概率化配置](#-版本-34---概率化配置与可变尺寸) - 可变尺寸，概率化配置
- [V3.3 - 精简架构](#-版本-33---精简与聚焦) - 边框算法优化，配置简化
- [安装使用](#安装) - 快速开始指南
- [项目结构](#项目结构) - 代码组织结构

## 🚀 版本 4 - 鲁棒性与图像增强 (最新)

### 🆕 V4 核心特性

#### 1. 内容溢出处理
- **智能溢出策略**: 支持截断(truncate)和换行(wrap)两种处理方式
- **CSS级别控制**: 通过CSS规则精确控制内容显示行为
- **灵活配置**: 可根据使用场景选择最适合的溢出处理策略

#### 2. 图像后处理系统
- **模糊效果**: 高斯模糊模拟相机失焦或运动模糊
- **噪声添加**: 高斯噪声模拟传感器噪声或压缩伪影
- **透视变换**: 轻微透视变换模拟拍摄角度变化
- **概率控制**: 每种效果都支持独立的概率和强度配置

#### 3. 背景图合成
- **真实环境模拟**: 将表格图像贴到真实背景图上，模拟纸张、桌面等真实环境
- **双重渲染模式**: 支持CSS渲染模式和后处理模式
- **智能尺寸处理**: 自动处理表格与背景图的尺寸匹配，优先缩放背景图
- **随机裁剪**: 在确保表格完整的前提下随机裁剪最终图像

#### 4. 智能边距控制
- **表格完整性保证**: 先完整渲染表格到背景图，确保不被截断
- **真实数据驱动**: 基于实际渲染的表格边界进行智能裁剪
- **边距精确控制**: 边距严格按配置执行，误差在1像素以内
- **概率化配置**: 支持多种边距范围及其概率分布

#### 5. 透视变换优化
- **统一OpenCV实现**: 解决了透视变换后标注坐标与图像不匹配的问题
- **坐标精确性**: 使用精确的透视变换矩阵，确保标注坐标与图像完全匹配
- **流程优化**: 透视变换在margin_control之前执行，确保坐标精确性

#### 6. 样式冲突检测
- **智能检测**: 自动检测表头/表体颜色、字体、对齐方式冲突
- **分层样式监控**: 检测分层样式覆盖冲突
- **警告日志**: 详细的冲突警告信息，帮助优化配置

#### 7. 表格透明度融合 (V4.3 🆕)
- **自然背景融合**: 表格背景透明化，与背景图像自然融合，消除生硬的边界感
- **CSS阶段透明度控制**: 在样式生成阶段应用透明度，确保CSS和后处理两种渲染模式的一致性
- **智能文字可读性**: 自动分析背景亮度，优化文字颜色并添加阴影效果，确保在透明背景下的可读性
- **统一透明度配置**: 简化的配置方式，支持全局透明度级别控制，易于使用和调试
- **完美向后兼容**: 默认关闭透明度功能，现有配置和工作流完全不受影响

#### 8. 性能优化
- **DOM查询优化**: 批量获取单元格信息，减少浏览器通信开销
- **渲染性能提升**: 优化JavaScript脚本执行效率

### 🚀 V4 快速开始

#### 安装依赖
```bash
pip install -r requirements.txt
```

#### 基本使用
```bash
# 使用V4完整配置（展示所有功能特性）
python -m table_render configs/v4_complete.yaml --num-samples 5

# 测试CSS背景图渲染模式
python -m table_render configs/v4_css_background_test.yaml --num-samples 3

# 测试OpenCV透视变换
python -m table_render configs/v4_opencv_perspective_test.yaml --num-samples 5

# 测试传统后处理背景图模式
python -m table_render configs/v4_postprocess_background_test.yaml --num-samples 3
```

#### V4 配置示例
```yaml
# 内容溢出处理
style:
  overflow_strategy: "wrap"  # 或 "truncate"

# 图像后处理
postprocessing:
  # V4.3新增：表格透明度融合 🌟
  table_blending:
    enable_transparency: true      # 启用表格透明度
    overall_transparency: 0.8      # 统一透明度级别（0.0完全透明 - 1.0完全不透明）
                                  # 推荐值：0.7-0.9，既保持融合效果又确保内容可读

  # 模糊效果
  blur:
    probability: 0.2
    radius_range: [1.0, 2.5]

  # 噪声效果
  noise:
    probability: 0.15
    intensity_range: [5, 15]

  # 透视变换 (统一使用OpenCV实现)
  perspective:
    probability: 0.8
    max_offset_ratio: 0.03

  # 背景图合成
  background:
    background_dirs: ["./assets/backgrounds/"]
    background_dir_probabilities: [1.0]
    max_scale_factor: 2.0

    # 智能边距控制
    margin_control:
      # 边距范围列表：定义不同的边距选项
      range_list:
        - [20, 40]    # 紧凑边距
        - [40, 80]    # 中等边距
        - [80, 120]   # 宽松边距

      # 对应的概率分布
      probability_list: [0.3, 0.5, 0.2]
```

### V4 实现细节

#### 1. 图像后处理系统
- **模块化设计**: 基于`BaseAugmentor`抽象类，支持可扩展的后处理效果
- **标注坐标变换**: 透视变换时自动更新标注坐标，确保标注与图像内容精确匹配
- **双重渲染模式**:
  - **CSS渲染模式**: 在HTML阶段直接集成背景图，避免坐标变换误差
  - **后处理模式**: 传统的图像后处理方式，支持复杂的图像操作

#### 2. 表格透明度融合 (V4.3新增) 🌟
- **自然背景融合**: 表格背景透明化，消除生硬的白色背景，与各种背景图像自然融合
- **CSS阶段透明度控制**: 在样式生成阶段应用透明度，确保CSS和后处理两种渲染模式的完全一致性
- **智能文字可读性优化**:
  - 自动分析背景亮度，智能调整文字颜色（深色背景→浅色文字，浅色背景→深色文字）
  - 根据对比度自动添加文字阴影或描边效果，确保在任何背景下的清晰可读
  - 支持WCAG对比度标准，确保可访问性
- **统一透明度配置**:
  - 简化的配置方式，只需设置`overall_transparency`参数（0.0-1.0）
  - 支持表头、表体、斑马条纹的统一透明度处理
  - 易于调试和微调，快速达到理想效果
- **完美向后兼容**:
  - 默认关闭透明度功能（`enable_transparency: false`）
  - 现有配置文件无需任何修改即可正常工作
  - 渐进式启用，可以逐步测试和部署

#### 3. 智能边距控制
- **"先渲染，后裁剪"原则**: 确保表格完整性，基于实际渲染结果进行精确裁剪
- **概率化配置**: 支持多种边距范围及其概率分布，生成多样化的图像
- **精确边距执行**: 基于真实表格边界，边距误差≤1像素

##### 边距控制最佳实践
- **文档扫描模拟**: 使用较小边距 (10-50像素)
- **海报/展示**: 使用较大边距 (80-200像素)
- **数据表格**: 使用中等边距 (30-80像素)
- **概率分布建议**: 主要使用场景占60-70%概率，边缘情况占10-20%概率
- **边距范围设置**: 最小边距至少10像素，最大边距不超过表格尺寸的50%

#### 3. 透视变换优化
- **统一使用OpenCV**: 使用精确的透视变换矩阵，确保坐标变换的精确性
- **流程优化**: 透视变换在margin_control之前执行，确保坐标精确性
- **坐标精确匹配**: 透视变换后的标注坐标与图像完全匹配

### V4 版本核心价值

V4版本专注于**鲁棒性与图像真实性**，解决了表格图像生成中的关键问题：

1. **标注精确性**: 透视变换后标注坐标与图像完全匹配，解决了坐标不一致的核心问题
2. **图像真实性**: 背景图合成、透视变换、模糊噪声等效果模拟真实拍摄环境
3. **智能裁剪**: 基于实际渲染结果的精确边距控制，确保表格完整性
4. **配置灵活性**: 概率化配置支持，生成多样化的表格图像数据集

#### V4 处理流程
1. HTML渲染（包含背景图）
2. OpenCV透视变换（如果启用）
3. 模糊和噪声效果（如果启用）
4. margin_control智能裁剪（如果启用）

---

## 🎨 版本 3.4 - 概率化配置与可变尺寸

### 🆕 V3.4 核心特性

#### 1. 全面概率化配置机制
- **概率化范围配置**: 支持多个数值范围及其概率分布，如 `[[3,5], [6,8]]` 配合 `[0.7, 0.3]`
- **概率化选项配置**: 支持离散选项及其概率分布，实现更丰富的随机化
- **自动概率归一化**: 无需手动确保概率总和为1，系统自动处理
- **多样性优先原则**: 配置设计确保生成的表格具有丰富的变化

#### 2. 革命性的可变行高列宽功能
- **智能尺寸配置**: 支持特定行/列的精确尺寸控制和概率性随机变化
- **真实文档模拟**: 模拟发票、报表等真实文档中的行高列宽差异
- **灵活配置类型**:
  - `specific`: 指定特定行/列的尺寸
  - `probabilistic`: 基于概率的随机尺寸变化
- **冲突解决机制**: 配置优先级和占用检测，避免重复应用

#### 3. 增强的边框概率化配置
- **复杂边框模式**: 支持多种边框模式的概率化组合
- **独立子配置**: 每种边框模式可配置独立的详细参数
- **灵活概率控制**: 精确控制不同边框样式的出现概率

#### 4. 标注文件后缀支持
- **灵活文件命名**: 支持为标注文件添加自定义后缀
- **批量处理友好**: 便于区分不同类型的标注文件
- **向后兼容**: 可选配置，不影响现有工作流

### 🚀 V3.4 快速开始

#### 安装依赖
```bash
pip install -r requirements.txt
```

#### 基本使用
```bash
# 使用V3.4默认配置（展示所有新特性）
python -m table_render configs/v3.4_default.yaml --num-samples 10

# 查看帮助
python -m table_render --help
```

#### V3.4 概率化配置示例
```yaml
# 表格结构 - 全面使用概率化配置
structure:
  # 概率化表体行数：70%概率3-5行，30%概率6-8行
  body_rows:
    range_list: [[3, 5], [6, 8]]
    probability_list: [0.7, 0.3]

  # 概率化列数：50%概率3-4列，50%概率5-6列
  cols:
    range_list: [[3, 4], [5, 6]]
    probability_list: [0.5, 0.5]

  # 概率化表头行数：80%概率单行，20%概率双行
  header_rows:
    range_list: [[1, 3], [2, 4]]
    probability_list: [0.8, 0.2]

# 样式配置 - V3.4可变尺寸功能
style:
  # 可变行高列宽配置
  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

    # 行级差异化配置
    row_configs:
      # 表头行强调 - 40%概率启用
      - name: "header_emphasis"
        probability: 0.4
        type: "specific"
        target_rows: [0]  # 第一行（表头）
        height_range: [40, 50]

      # 汇总行强调 - 30%概率启用
      - name: "summary_emphasis"
        probability: 0.3
        type: "specific"
        target_rows: [-1]  # 最后一行
        height_range: [35, 45]

      # 随机行高变化 - 25%概率启用
      - name: "random_variations"
        probability: 0.25
        type: "probabilistic"
        per_row_probability: 0.15  # 每行15%概率被选中
        height_range: [30, 40]

  # 概率化边框模式配置
  border_mode:
    mode_options:
      # 40%概率完整边框
      - probability: 0.4
        config:
          mode: "full"

      # 30%概率无边框
      - probability: 0.3
        config:
          mode: "none"

      # 30%概率半边框模式
      - probability: 0.3
        config:
          mode: "semi"
          semi_config:
            row_line_probability: 0.6
            col_line_probability: 0.5
            outer_frame: true
            header_separator: true

# 输出配置 - 支持标注文件后缀
output:
  output_dir: "./output/"
  label_suffix: "_table_annotation"  # 标注文件后缀

seed: 42
```

## 🏗️ V3.4 技术亮点

### 🔧 核心算法创新

#### 1. 智能可变尺寸算法

**设计目标**: 模拟真实文档中的行高列宽差异，提升数据集的真实感

**核心特性**
```python
# 支持两种配置类型
class SizingConfigItem:
    type: "specific" | "probabilistic"  # 配置类型
    probability: float                  # 启用概率
    target_rows: List[int]             # 特定行索引（specific类型）
    per_row_probability: float         # 每行触发概率（probabilistic类型）
    height_range: List[int]            # 高度范围[min, max]
```

**算法优势**
- **真实感提升**: 模拟发票、报表等真实文档的尺寸变化
- **灵活配置**: 支持精确指定和概率随机两种模式
- **冲突避免**: 智能的行/列占用检测，避免重复配置
- **CSS优化**: 高特异性选择器确保样式正确应用

#### 2. 概率化配置解析引擎

**问题背景**: V3.3及之前版本配置相对静态，单一配置文件生成的表格多样性有限

**V3.4解决方案**: 全面概率化配置
```python
# 概率化范围配置
class ProbabilisticRange:
    range_list: List[List[Union[int, float]]]    # 多个范围
    probability_list: List[float]                # 对应概率

# 概率化选项配置
class ProbabilisticOptions:
    option_list: List[Any]                       # 选项列表
    probability_list: List[float]                # 对应概率
```

**实现特点**
- **自动归一化**: 概率值自动归一化，无需手动计算
- **向后兼容**: 支持传统的单值配置
- **类型安全**: 基于Pydantic的严格类型验证
- **性能优化**: 高效的概率选择算法

#### 3. 增强的边框概率化系统

**V3.3问题**: 边框配置相对简单，无法实现复杂的概率化组合

**V3.4解决方案**: 多模式概率化边框
```python
class BorderModeOption:
    probability: float      # 模式概率
    config: dict           # 模式配置（支持任意复杂度）

# 配置示例
border_mode:
  mode_options:
    - probability: 0.4
      config: {mode: "full"}
    - probability: 0.3
      config: {mode: "semi", semi_config: {...}}
```

**配置优势**
- **模式多样性**: 支持任意数量的边框模式组合
- **独立配置**: 每种模式可配置独立的详细参数
- **概率精确控制**: 精确控制不同边框样式的出现频率

## 🎯 V3.4 vs V3.3 对比

| 特性 | V3.3 | V3.4 |
|------|------|------|
| **配置机制** | 静态值或简单范围 | 全面概率化配置 |
| **表格尺寸** | 固定行高列宽 | 可变行高列宽 |
| **边框配置** | 单一模式 | 多模式概率化 |
| **多样性** | 中等 | 极高 |
| **真实感** | 良好 | 优秀 |
| **配置复杂度** | 简单 | 中等（但功能强大） |

### 🔄 V3.4 完整调用链

V3.4版本的增强数据流：

```mermaid
flowchart TD
    A[配置加载] --> B[Resolver概率化解析]
    B --> C[StructureBuilder构建结构]
    C --> D[应用可变尺寸配置]
    D --> E[ContentBuilder填充内容]
    E --> F[StyleBuilder生成增强CSS]
    F --> G[可变尺寸CSS规则生成]
    G --> H[HtmlRenderer渲染]
    H --> I[AnnotationConverter转换标注]
    I --> J[FileUtils保存文件（支持后缀）]
```

**V3.4关键改进点**
- **步骤B**: 全新的概率化配置解析引擎
- **步骤D**: 可变行高列宽配置应用
- **步骤F-G**: 增强的CSS生成，支持动态尺寸
- **步骤J**: 支持标注文件后缀的文件保存

## 📋 V3.4 配置文件说明

### 可用配置文件

| 配置文件 | 版本 | 说明 |
|---------|------|------|
| `configs/v3.4_default.yaml` | V3.4 | V3.4完整配置，展示所有新特性 |

### V3.4 新增配置项详解

#### 概率化结构配置
```yaml
structure:
  body_rows:
    range_list: [[3, 5], [6, 8]]      # 多个范围选项
    probability_list: [0.7, 0.3]      # 对应概率（自动归一化）
```

#### 可变行高列宽配置
```yaml
sizing:
  row_configs:
    - name: "header_emphasis"          # 配置名称（用于日志）
      probability: 0.4                 # 启用概率
      type: "specific"                 # 配置类型
      target_rows: [0]                 # 目标行（支持负索引）
      height_range: [40, 50]           # 高度范围
```

#### 概率化边框配置
```yaml
border_mode:
  mode_options:
    - probability: 0.4                 # 模式概率
      config:                          # 模式配置
        mode: "full"
    - probability: 0.3
      config:
        mode: "semi"
        semi_config:                   # 半边框详细配置
          row_line_probability: 0.6
          col_line_probability: 0.5
```

#### 标注文件后缀
```yaml
output:
  label_suffix: "_table_annotation"    # 可选，标注文件后缀
```

### 使用示例
```bash
# V3.4 完整特性
python -m table_render configs/v3.4_default.yaml --num-samples 10
```

---

## 🎨 版本 3.3 - 精简与聚焦

### 🆕 V3.3 核心特性

#### 1. 精简配置架构
- **移除V3.1兼容性**: 彻底删除向后兼容的复杂逻辑，聚焦核心模型
- **统一边框控制**: 所有边框样式完全由顶层 `border_mode` 配置项决定
- **配置结构简化**: 只保留 `common`（公共样式）和 `inheritance`（继承配置）两个部分

#### 2. 概率化字体目录选择
- **带权重的字体目录**: 支持为不同字体目录设置选择概率
- **灵活配置格式**: 支持单个目录、目录列表、带概率的目录配置
- **智能字体管理**: 根据概率从多个字体目录中选择字体

#### 3. 完全概率化的颜色生成
- **移除静态颜色列表**: 不再需要维护冗长的颜色配置
- **概率控制**: 通过 `randomize_color_probability` 控制颜色随机化
- **智能颜色对**: 自动生成满足对比度要求的颜色组合
- **柔和色彩支持**: 可配置生成视觉友好的柔和颜色

#### 4. 革命性的边框处理算法
- **先确定边框，再合并**: 全新的边框处理方案，彻底解决合并单元格边框不一致问题
- **网格线概念**: 基于网格线状态的简单直观边框处理
- **一致性保证**: 确保同一条边界线上所有单元格的边框状态完全一致

#### 5. 合并单元格对齐优化
- **相对居中对齐**: 减少合并单元格的视觉歧义，提高标注准确性
- **智能对齐规则**: 水平left/right时垂直居中，垂直top/bottom时水平居中
- **概率控制**: 通过 `merged_cell_center_probability` 控制优化启用概率
- **冲突随机处理**: 当两个规则都适用时，随机选择应用其中一个

### 🚀 V3.3 快速开始

#### 安装依赖
```bash
pip install -r requirements.txt
```

#### 基本使用
```bash
# 使用V3.4默认配置
python -m table_render configs/v3.4_default.yaml --num-samples 10

# 查看帮助
python -m table_render --help
```

#### V3.3 精简配置示例
```yaml
structure:
  header_rows: 2
  body_rows: 4
  cols: 5
  merge_probability: 0.3
  max_row_span: 2
  max_col_span: 2

content:
  data_source:
    type: "programmatic"

style:
  # 公共样式配置
  common:
    font:
      font_dirs:
        - "./assets/fonts/"
        - "./assets/fonts/chinese/"
      font_dir_probabilities: [0.7, 0.3]  # 字体目录选择概率
      default_family: ["Arial", "Times New Roman", "Microsoft YaHei"]
      default_size: {min: 12, max: 16}

    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: {min: 6, max: 12}

    # V3.3新特性：完全概率化的颜色生成
    randomize_color_probability: 0.4

    # V3.3新特性：合并单元格对齐优化
    merged_cell_center_probability: 0.6

    # 颜色对比度配置
    color_contrast:
      min_contrast_ratio: 4.5
      use_soft_colors_probability: 0.7

  # 样式继承配置
  inheritance:
    font_family_change_probability: 0.2
    font_size_change_probability: 0.3
    alignment_change_probability: 0.4
    padding_change_probability: 0.2
    text_color_change_probability: 0.3
    background_color_change_probability: 0.2

  # 边框模式配置
  border_mode:
    mode: "semi"  # full(有线) / none(纯无线) / semi(半无线)
    row_line_probability: 0.6
    col_line_probability: 0.4
    outer_frame: true
    header_separator: true

  zebra_stripes: false
  sizing:
    row_height: "auto"
    col_width: "auto"

output:
  output_dir: "./output/"

seed: 42
```

## 🏗️ V3.3 技术亮点

### 🔧 核心算法优化

#### 1. 革命性的边框处理算法

**问题背景**
- V3.2及之前版本存在合并单元格边框不一致问题
- 复杂的边界线片段收集和合并逻辑导致边框显示异常

**V3.3解决方案：先确定边框，再合并**
```python
# 1. 先确定所有网格线状态
h_grid_lines = [random() < row_prob for _ in range(total_rows - 1)]
v_grid_lines = [random() < col_prob for _ in range(total_cols - 1)]

# 2. 执行单元格合并

# 3. 将网格线状态映射到合并后单元格的外轮廓
for cell in merged_cells:
    # 直接映射：合并单元格边框 = 其外轮廓对应的网格线状态
    cell.border_top = h_grid_lines[cell.row_index - 1] if cell.row_index > 0 else 0
    cell.border_bottom = h_grid_lines[cell.row_index + cell.row_span - 1] if not_last_row else 0
    # ... 左右边框类似
```

**算法优势**
- **逻辑简单**: 网格线概念直观，易于理解和调试
- **一致性保证**: 天然避免边框不一致问题
- **性能提升**: 算法复杂度从O(n²)降低到O(n)
- **代码精简**: 移除约200行复杂的边界线处理代码

#### 2. 智能的合并单元格对齐优化

**设计目标**: 减少合并单元格的视觉歧义，提高OCR标注准确性

**优化规则**
```python
def optimize_merged_cell_alignment(horizontal_align, vertical_align):
    # 规则1: 水平left/right → 垂直middle
    if horizontal_align in ['left', 'right']:
        return horizontal_align, 'middle'

    # 规则2: 垂直top/bottom → 水平center
    if vertical_align in ['top', 'bottom']:
        return 'center', vertical_align

    # 冲突时随机选择
    if both_rules_apply:
        return random_choice([apply_rule1(), apply_rule2()])
```

**实现特点**
- **非侵入性**: 在CSS生成时动态处理，不修改数据模型
- **优先级清晰**: 分层样式 > 对齐优化 > 基础样式
- **概率控制**: 通过配置控制优化启用概率

#### 3. 完全概率化的颜色生成系统

**V3.2问题**: 需要维护冗长的颜色列表，配置复杂

**V3.3解决方案**: 概率化颜色生成
```python
def generate_colors(randomize_probability, color_contrast_config):
    if random() < randomize_probability:
        # 生成随机颜色对，确保对比度
        return generate_contrasted_color_pair(color_contrast_config)
    else:
        # 使用默认黑白配色
        return "#000000", "#FFFFFF"
```

**配置简化对比**
```yaml
# V3.2 (复杂)
style:
  common_style:
    text_color: ["#2C3E50", "#34495E", "#1A252F", "#4A6741"]
    background_color: ["#FFFFFF", "#F8F9FA", "#E8F4FD", "#F0F8FF"]

# V3.3 (简洁)
style:
  common:
    randomize_color_probability: 0.4  # 40%概率生成随机颜色
    color_contrast:
      min_contrast_ratio: 4.5
      use_soft_colors_probability: 0.7
```

### 🔄 V3.3 完整调用链

V3.3版本的完整数据流：

```mermaid
flowchart TD
    A[配置加载] --> B[Resolver解析]
    B --> C[StructureBuilder构建结构]
    C --> D[先确定网格线状态]
    D --> E[执行单元格合并]
    E --> F[应用网格线到单元格边框]
    F --> G[ContentBuilder填充内容]
    G --> H[StyleBuilder生成CSS]
    H --> I[合并单元格对齐优化]
    I --> J[HtmlRenderer渲染]
    J --> K[AnnotationConverter转换标注]
    K --> L[FileUtils保存文件]
```

**关键改进点**
- **步骤D-F**: 全新的边框处理流程，确保一致性
- **步骤I**: 新增的合并单元格对齐优化
- **整体**: 移除了复杂的边界线片段处理逻辑

## 📋 配置文件说明

### 可用配置文件

| 配置文件 | 版本 | 说明 |
|---------|------|------|
| `configs/v3.4_default.yaml` | V3.4 | V3.4默认配置，展示所有新特性 |

### V3.3 配置特点

#### 精简的配置结构
```yaml
style:
  common:          # 公共样式配置
    # 字体、颜色、对齐等基础样式
  inheritance:     # 样式继承配置
    # 表头表体差异化概率
  border_mode:     # 边框模式配置
    # 统一的边框控制
```

#### 新增配置项说明

**字体目录概率化选择**
```yaml
font:
  font_dirs:
    - "./assets/fonts/"
    - "./assets/fonts/chinese/"
  font_dir_probabilities: [0.7, 0.3]  # 对应概率
```

**概率化颜色生成**
```yaml
randomize_color_probability: 0.4  # 40%概率生成随机颜色
color_contrast:
  min_contrast_ratio: 4.5
  use_soft_colors_probability: 0.7  # 70%概率使用柔和色彩
```

**合并单元格对齐优化**
```yaml
merged_cell_center_probability: 0.6  # 60%概率应用对齐优化
```

### 使用示例
```bash
# V3.4 默认配置
python -m table_render configs/v3.4_default.yaml --num-samples 10
```

## 📁 输出结构

生成的文件会保存在配置文件指定的输出目录中（默认为 `./output/`）：

```
output/
├── 000001-000500/
│   ├── images/
│   │   ├── image_000001.png
│   │   ├── image_000002.png
│   │   └── ...
│   ├── annotations/
│   │   ├── annotation_000001.json
│   │   ├── annotation_000002.json
│   │   └── ...
│   └── metadata/
│       ├── metadata_000001.json
│       ├── metadata_000002.json
│       └── ...
└── 000501-001000/
    └── ...
```

## 🔧 故障排除

### 常见错误

1. **配置文件未找到**
   ```
   错误: 配置文件未找到: configs/xxx.yaml
   ```
   **解决**: 检查配置文件路径是否正确，确保使用存在的配置文件

2. **缺少必需参数**
   ```
   error: the following arguments are required: --num-samples
   ```
   **解决**: 添加 `--num-samples` 参数

3. **字体文件未找到**
   ```
   警告: 字体目录不存在: ./assets/fonts/
   ```
   **解决**: 创建字体目录或修改配置中的字体路径

4. **V3.3配置验证失败**
   ```
   错误: 配置验证失败 - merged_cell_center_probability
   ```
   **解决**: 确保新增的V3.3配置项格式正确

5. **边框显示异常**
   ```
   问题: 合并单元格边框不一致
   ```
   **解决**: V3.3已彻底解决此问题，使用最新配置即可

### 调试技巧

```bash
# 查看详细日志
python -m table_render configs/v3.4_default.yaml --num-samples 1 2>&1 | tee debug.log

# 使用最小配置测试
python -m table_render configs/v3.4_default.yaml --num-samples 1

# 检查配置文件语法
python -c "import yaml; print(yaml.safe_load(open('configs/v3.4_default.yaml')))"

# 验证边框算法
python -c "from table_render.builders.structure_builder import StructureBuilder; print('边框算法正常')"
```

## 🔍 V3.3 实现效果

### 核心改进成果

#### 1. 边框一致性问题彻底解决
- **问题**: V3.2及之前版本存在合并单元格边框显示不一致
- **解决**: 全新的"先确定边框，再合并"算法确保100%一致性
- **效果**: 所有表格边框显示完全符合预期，无异常情况

#### 2. 配置复杂度大幅降低
- **V3.2**: 需要维护复杂的颜色列表和兼容性逻辑
- **V3.3**: 单一概率控制，配置文件精简60%以上
- **效果**: 用户学习成本显著降低，配置错误率减少

#### 3. 代码质量显著提升
- **移除代码**: 删除约200行复杂的边界线处理代码
- **算法优化**: 边框处理复杂度从O(n²)降至O(n)
- **维护性**: 代码结构更清晰，易于理解和扩展

#### 4. 视觉效果优化
- **合并单元格**: 相对居中对齐减少视觉歧义
- **颜色生成**: 智能的概率化颜色生成，效果更自然
- **字体选择**: 带权重的字体目录选择，支持更丰富的字体组合

### V3.3 核心模块

- **`table_render/config.py`** - 精简配置模型，新增概率化字段
- **`table_render/resolver.py`** - 支持新的配置解析逻辑
- **`table_render/builders/structure_builder.py`** - 全新的边框处理算法
- **`table_render/builders/style_builder.py`** - 合并单元格对齐优化
- **`table_render/utils/font_utils.py`** - 概率化字体目录选择
- **`table_render/utils/color_utils.py`** - 完全概率化的颜色生成

## 🚀 历史版本功能概览

### 版本 3.0+ 功能特性
- **高级样式系统**: 字体控制、颜色对齐、精细边框控制
- **分层样式系统**: 全局→列→行→单元格的优先级应用
- **复杂表头支持**: 多级表头、层次结构、灵活配置
- **随机尺寸控制**: 随机行高列宽、范围配置

### 版本 2.0+ 功能特性
- **随机化表格维度**: 行列数范围配置、智能随机选择
- **单元格合并**: rowspan/colspan支持、智能冲突避免
- **程序化内容生成**: 日期、货币、百分比等格式化数据
- **CSV数据源支持**: 真实数据加载、维度不匹配处理

### 版本 1.0 基础功能
- **核心渲染流水线**: 配置→结构→内容→样式→渲染→标注
- **HTML/CSS渲染引擎**: 无头浏览器渲染、精确截图
- **结构化标注**: 单元格边界框、逻辑位置、内容信息
- **标准化输出**: 图像、标注、元数据的规范化存储

## 安装

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 安装 Playwright 浏览器：
```bash
playwright install chromium
```

## 使用方法

### 基本使用

```bash
# 使用V3.4默认配置
python -m table_render configs/v3.4_default.yaml --num-samples 10

# 查看帮助信息
python -m table_render --help
```



### 配置文件

配置文件使用 YAML 格式。详细的配置示例请参考：
- `configs/v3.4_default.yaml` - V3.4完整配置示例
- `configs/v4_complete.yaml` - V4完整功能配置示例



## 项目结构

```
TableRender/
├── table_render/           # 核心Python包
│   ├── main.py             # 命令行入口
│   ├── config.py           # 配置模型（V3.4概率化增强）
│   ├── resolver.py         # 配置解析器（V3.4概率化引擎）
│   ├── main_generator.py   # 主生成器
│   ├── models.py           # 数据模型
│   ├── builders/           # 构建器模块
│   │   ├── structure_builder.py  # 结构生成器（V3.3新边框算法）
│   │   ├── content_builder.py    # 内容生成器
│   │   └── style_builder.py      # 样式生成器（V3.4可变尺寸支持）
│   ├── renderers/          # 渲染器模块
│   │   └── html_renderer.py      # HTML渲染器（V3.4增强CSS类）
│   └── utils/              # 工具模块
│       ├── prob_utils.py          # 概率处理工具（V3.4新增）
│       ├── font_utils.py          # 字体管理（V3.3概率化）
│       ├── color_utils.py         # 颜色管理（V3.3概率化）
│       ├── style_utils.py         # 样式继承（V3.4概率化增强）
│       ├── file_utils.py          # 文件操作（V3.4后缀支持）
│       └── annotation_converter.py # 标注转换
├── assets/                 # 静态资源
│   └── fonts/              # 字体文件目录
├── configs/                # 配置文件
│   ├── v3.4_default.yaml  # V3.4完整配置
│   ├── v4_complete.yaml   # V4完整功能配置（最新）
│   ├── v4_css_background_test.yaml # CSS背景图渲染测试
│   ├── v4_opencv_perspective_test.yaml # OpenCV透视变换测试
│   └── v4_postprocess_background_test.yaml # 传统后处理背景图测试
├── output/                 # 输出目录（生成的文件）
├── requirements.txt        # 项目依赖
└── README.md               # 项目说明文档
```

## 版本历史与开发计划

### ✅ 版本 4 - 鲁棒性与图像增强（已完成）

**图像后处理系统**
- 模糊效果：高斯模糊模拟相机失焦或运动模糊
- 噪声添加：高斯噪声模拟传感器噪声或压缩伪影
- 透视变换：轻微透视变换模拟拍摄角度变化，统一使用OpenCV实现
- 背景图合成：将表格图像贴到真实背景图上，模拟纸张、桌面等真实环境

**智能边距控制**
- 表格完整性保证：先完整渲染表格到背景图，确保不被截断
- 真实数据驱动：基于实际渲染的表格边界进行智能裁剪
- 边距精确控制：边距严格按配置执行，误差在1像素以内

**内容溢出处理**
- 智能溢出策略：支持截断(truncate)和换行(wrap)两种处理方式
- CSS级别控制：通过CSS规则精确控制内容显示行为

### ✅ 版本 3.4 - 概率化配置与可变尺寸（已完成）

**全面概率化配置机制**
- 概率化范围配置：支持多个数值范围及其概率分布，实现更丰富的随机化
- 概率化选项配置：支持离散选项及其概率分布，提升配置灵活性
- 自动概率归一化：无需手动确保概率总和为1，系统自动处理
- 多样性优先原则：配置设计确保生成的表格具有丰富的变化

**革命性的可变行高列宽功能**
- 智能尺寸配置：支持特定行/列的精确尺寸控制和概率性随机变化
- 真实文档模拟：模拟发票、报表等真实文档中的行高列宽差异
- 灵活配置类型：specific（指定特定行/列）和probabilistic（概率触发）两种模式
- 冲突解决机制：配置优先级和占用检测，避免重复应用

**增强的边框概率化配置**
- 复杂边框模式：支持多种边框模式的概率化组合
- 独立子配置：每种边框模式可配置独立的详细参数
- 灵活概率控制：精确控制不同边框样式的出现概率

**标注文件后缀支持**
- 灵活文件命名：支持为标注文件添加自定义后缀
- 批量处理友好：便于区分不同类型的标注文件
- 向后兼容：可选配置，不影响现有工作流

### ✅ 版本 3.3 - 精简与聚焦（已完成）

**精简配置架构**
- 移除V3.1兼容性：彻底删除向后兼容的复杂逻辑，聚焦核心模型
- 统一边框控制：所有边框样式完全由顶层`border_mode`配置项决定
- 配置结构简化：只保留`common`和`inheritance`两个核心部分

**革命性边框处理算法**
- 先确定边框，再合并：全新算法彻底解决合并单元格边框不一致问题
- 网格线概念：基于网格线状态的简单直观边框处理
- 性能优化：算法复杂度从O(n²)降低到O(n)，代码精简60%

**完全概率化的颜色生成**
- 移除静态颜色列表：不再需要维护冗长的颜色配置
- 概率控制：通过`randomize_color_probability`控制颜色随机化
- 智能颜色对：自动生成满足对比度要求的颜色组合

**合并单元格对齐优化**
- 相对居中对齐：减少合并单元格的视觉歧义，提高标注准确性
- 智能对齐规则：水平left/right时垂直居中，垂直top/bottom时水平居中
- 概率控制：通过`merged_cell_center_probability`控制优化启用概率

**概率化字体目录选择**
- 带权重的字体目录：支持为不同字体目录设置选择概率
- 灵活配置格式：支持单个目录、目录列表、带概率的目录配置

### ✅ 版本 3.2 - 样式系统优化（已完成）
- 样式继承机制：公共样式+概率性差异化
- 智能字体选择与容错：多目录扫描、兼容性检测
- 颜色对比度保证：WCAG AA标准、柔和色彩
- 灵活的边框控制系统：三种模式、概率控制
- 完全向后兼容：支持V3.1配置格式

### ✅ 版本 3.1 - 架构重构与概率化生成（已完成）
- 全新架构设计：Resolver模块、ResolvedParams模型
- 表头/主体结构分离：独立样式配置、语义化HTML
- 概率化配置系统：范围配置、选择配置、嵌套概率化
- 精确可复现性：元数据快照、种子控制、参数追溯

### ✅ 版本 3.0 - 高级样式与真实感（已完成）
- 高级样式系统（字体、颜色、对齐、边框）
- 分层样式应用（全局→列→行→单元格）
- 复杂表头支持（多级表头、跨列合并）
- 随机尺寸控制（行高、列宽）

### ✅ 版本 2.0 - 增强的结构与内容（已完成）
- 随机化表格维度、单元格合并功能
- 程序化生成格式化内容、CSV数据源支持

### ✅ 版本 1.0 - 核心渲染流水线（已完成）
- 基础表格结构生成、HTML/CSS渲染引擎
- 精确的单元格边界框标注、标准化输出格式

### 🔄 未来版本计划
- **版本 4.5**: 高级表格特性（复杂表头、嵌套表格、条件格式）
- **版本 5.0**: LaTeX 渲染引擎与可扩展核心

## V4 重要说明

### 核心改进
V4版本专注于**鲁棒性与图像真实性**，解决了表格图像生成中的关键问题：

- **标注精确性**: 透视变换后标注坐标与图像完全匹配，解决了坐标不一致的核心问题
- **图像真实性**: 背景图合成、透视变换、模糊噪声等效果模拟真实拍摄环境
- **智能裁剪**: 基于实际渲染结果的精确边距控制，确保表格完整性
- **向后兼容**: 完全兼容V3.x配置格式，新功能通过可选配置项启用

### V4 配置示例
```yaml
# V4 图像后处理配置
postprocessing:
  # V4.3新增：表格透明度融合
  table_blending:
    enable_transparency: true
    overall_transparency: 0.8

  # 透视变换
  perspective:
    probability: 0.8
    max_offset_ratio: 0.03

  # 背景图合成
  background:
    background_dirs: ["./assets/backgrounds/"]
    margin_control:
      range_list: [[20, 40], [40, 80]]
      probability_list: [0.6, 0.4]
```

## 🌟 V4.3 表格透明度融合使用指南

### 快速开始

1. **基础透明度配置**
```yaml
postprocessing:
  table_blending:
    enable_transparency: true
    overall_transparency: 0.8
  background:
    background_dirs: ["./assets/backgrounds/"]
```

2. **运行命令**
```bash
python -m table_render.main --config configs/v4.3_transparency_test.yaml --num-samples 5 --debug
```

### 透明度级别建议

| 透明度值 | 效果描述 | 适用场景 |
|---------|---------|---------|
| 0.9-1.0 | 轻微透明，保持表格清晰度 | 正式文档，需要高可读性 |
| 0.7-0.9 | 中等透明，平衡融合与可读性 | **推荐设置**，适合大多数场景 |
| 0.5-0.7 | 较强透明，明显的融合效果 | 艺术效果，背景图案简单 |
| 0.3-0.5 | 强透明，需要简单背景 | 特殊效果，水印样式 |

### 最佳实践

#### ✅ 推荐做法
- **透明度设置**: 使用0.7-0.9的透明度值，既有融合效果又保证可读性
- **背景选择**: 选择纹理相对简单、对比度适中的背景图
- **调试模式**: 首次使用时开启`--debug`模式，观察每个处理阶段的效果
- **批量测试**: 先用少量样本测试效果，满意后再大批量生成

#### ❌ 避免的做法
- 不要在复杂、高对比度的背景上使用过高的透明度
- 不要在没有背景图的情况下启用透明度（会导致表格过于淡化）
- 不要忽略文字可读性，系统会自动优化但仍需人工验证

### 技术特性

#### 自动文字优化
- **颜色调整**: 系统自动分析背景亮度，调整文字颜色确保对比度
- **阴影效果**: 根据背景特性自动添加文字阴影或描边
- **对比度保证**: 遵循WCAG可访问性标准，确保文字清晰可读

#### 渲染模式兼容
- **CSS模式**: 在HTML阶段直接应用透明度，性能更好
- **后处理模式**: 在图像处理阶段应用透明度，效果一致
- **自动选择**: 系统根据配置自动选择最适合的渲染模式

### 故障排除

#### 问题：透明度效果不明显
- **检查背景图**: 确保`background_dirs`路径正确且包含图片文件
- **检查透明度值**: 确认`overall_transparency`小于1.0
- **检查配置**: 确认`enable_transparency: true`

#### 问题：文字不清晰
- **调整透明度**: 适当提高`overall_transparency`值
- **检查背景**: 选择对比度较低的背景图
- **查看调试输出**: 使用`--debug`模式检查文字优化效果

## V3.3 重要说明

### 破坏性变更
V3.3版本为了实现"精简与聚焦"的目标，**移除了V3.1兼容性支持**：

- **不再支持**: V3.1和V3.2的旧配置格式
- **迁移要求**: 需要将配置文件更新为V3.3格式
- **收益**: 代码复杂度大幅降低，性能和稳定性显著提升

### 迁移指南
```yaml
# V3.2 配置格式
style:
  use_inheritance: true
  common_style:
    text_color: ["#000000"]
    background_color: ["#FFFFFF"]

# V3.3 配置格式
style:
  common:
    randomize_color_probability: 0.3
  inheritance:
    text_color_change_probability: 0.1
```

## 依赖说明

### 核心依赖
- `faker`: 用于生成格式化的程序化内容
- `pydantic`: 配置验证和类型安全
- `playwright`: 无头浏览器渲染
- `numpy`: 随机数生成和数值计算
- `pillow`: 图像处理（V4新增）
- `opencv-python`: 透视变换（V4新增）
- `pyyaml`: YAML配置文件解析

### 版本特性依赖
- **V4 图像后处理**: `pillow`用于图像增强，`opencv-python`用于透视变换
- **V3.4 概率化配置**: 基于`numpy`的高效随机选择算法
- **V3.4 可变尺寸**: 增强的CSS生成，无额外依赖

## 许可证

本项目采用 MIT 许可证。
