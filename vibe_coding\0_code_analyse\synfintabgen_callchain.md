# SynFinTabGen 调用链分析

## 调用链

### 节点: main.py

*   **所在代码文件相对路径**: `third_parties/synfintabgen/main.py`
*   **用途**: 项目的入口点，用于启动整个金融表格图像合成流程。
*   **输入参数**: 无。
*   **输出说明**: 无直接输出，其作用是实例化并调用 `DatasetGenerator` 来生成数据集。
*   **核心调用**:
    1.  `generator = DatasetGenerator()`: 初始化数据集生成器。
    2.  `generator(10)`: 调用生成器实例，生成10个样本。

### 节点: DatasetGenerator.__init__

*   **所在代码文件相对路径**: `third_parties/synfintabgen/synfintabgen/generator_dataset.py`
*   **用途**: `DatasetGenerator` 类的构造函数，负责初始化整个数据集生成流程所需的配置和组件。
*   **输入参数**:
    *   `config` (`DatasetGeneratorConfig`, 可选): 数据集生成的配置对象。如果未提供，则使用默认配置。该配置包含数据集路径、名称、文档宽度和高度等信息。
*   **输出说明**: 返回一个配置好的 `DatasetGenerator` 实例。
*   **核心调用**:
    *   `self._get_driver()`: 初始化并返回一个 Selenium Chrome WebDriver 实例，用于后续的HTML渲染和截图。
    *   `self._table_generator = TableGenerator()`: 实例化表格内容生成器。
    *   `self._theme_generator = ThemeGenerator()`: 实例化表格样式生成器。
    *   `self._document_generator = DocumentGenerator(...)`: 实例化HTML文档生成器。
    *   `self._image_generator = ImageGenerator(...)`: 实例化图像生成器。
    *   `self._annotation_generator = AnnotationGenerator(...)`: 实例化标注数据生成器。

### 节点: TableGenerator.__call__

*   **所在代码文件相对路径**: `third_parties/synfintabgen/synfintabgen/generator_table.py`
*   **用途**: 创建一个结构化但内容随机的财务表格。它负责生成表格的所有逻辑元素，包括表头、段落、行和单元格数据。
*   **输入参数**:
    *   `params` (`dict`): 一个包含各种随机生成参数的字典，用于控制表格的结构和内容细节（例如，段落数量、列数、数值范围等）。
*   **输出说明**: 返回一个二维列表（`List[List[dict]]`），其中每个内部列表代表一行，每个字典代表一个单元格。字典中包含了单元格的类型（HEADER/DATA）、内容、合并列数（colspan）和CSS类等信息。
*   **核心调用**:
    *   `self._create_table(params)`: 内部核心方法，协调整个表格的创建过程。
        *   `self._get_eoys(params)`: 获取表格的年份。
        *   `self._get_section_titles(params)`: 从预定义列表中随机选择段落标题。
        *   `self._create_table_header(...)`: 创建表格的列标题。
        *   `self._create_section(...)`: 为每个段落标题创建一个包含多行的段落。
            *   `self._create_row(...)`: 创建单行数据，包括行标题和随机生成的数值。

### 节点: ThemeGenerator.__call__
*   **所在代码文件相对路径**: `third_parties/synfintabgen/synfintabgen/generator_theme.py`
*   **用途**: 根据索引选择并执行一个特定的主题生成函数，以创建表格的CSS样式。
*   **输入参数**:
    *   `idx` (`int`): 用于选择主题函数的索引。
    *   `params` (`dict`): 包含自定义样式所需参数的字典（如字体、行高、边框样式等）。
*   **输出说明**: 返回一个字典，该字典以CSS选择器为键，以包含CSS属性和值的字典为值，代表了表格的完整主题样式。
*   **核心调用**:
    *   `self._theme_functions[idx](params)`: 调用一个具体的主题函数（如 `_theme_0`, `_theme_1` 等）来生成样式字典。

### 节点: DatasetGenerator.__call__

*   **所在代码文件相对路径**: `third_parties/synfintabgen/synfintabgen/generator_dataset.py`
*   **用途**: 核心的数据集生成循环。该方法根据指定的大小，迭代生成表格、主题、HTML文档、图像和标注。
*   **输入参数**:
    *   `size` (`int`): 要生成的数据集样本数量。
*   **输出说明**: 无直接返回值。该方法在磁盘上生成HTML文件、图像文件以及一个包含所有标注的JSON文件。
*   **核心调用**:
    *   `self._create_dataset_dir()`: 创建数据集所需的输出目录。
    *   `self._get_params()`: 生成用于当次迭代的随机化参数。
    *   `self._table_generator(params)`: 调用 `TableGenerator` 生成表格内容。
    *   `self._theme_generator(theme_idx, params)`: 调用 `ThemeGenerator` 生成表格样式。
    *   `self._document_generator(document_id, table, theme)`: 调用 `DocumentGenerator` 生成HTML文件。
    *   `self._image_generator(document_id, document_file_path)`: 调用 `ImageGenerator` 从HTML文件生成图像。
    *   `self._annotation_generator(document_id, theme_idx, table, params)`: 调用 `AnnotationGenerator` 生成当前样本的标注信息。
    *   `self._annotation_generator.write_to_file()`: 在循环结束后，将所有累积的标注数据写入JSON文件。

### 节点: DocumentGenerator.__call__

*   **所在代码文件相对路径**: `third_parties/synfintabgen/synfintabgen/generator_document.py`
*   **用途**: 将结构化的表格数据和CSS样式字典转换为一个完整的HTML文档，并将其保存到文件。
*   **输入参数**:
    *   `id` (`str`): 文档的唯一标识符，用作文件名。
    *   `table` (`List[List[dict]]`): 由 `TableGenerator` 生成的表格数据。
    *   `theme` (`dict`): 由 `ThemeGenerator` 生成的CSS样式字典。
*   **输出说明**: 返回生成的HTML文件的完整路径（`str`）。
*   **核心调用**:
    *   `self._create_html_document(id, table, theme)`: 内部核心方法，协调HTML文档的创建和保存。
        *   `self._create_html_table(table)`: 将表格数据结构转换为 `htmltree` 的 `Table` 对象。
        *   `self._create_html_head(id, theme)`: 创建HTML的 `<head>` 部分，包含元信息、标题和内联CSS样式。
        *   `doc.renderToFile(...)`: 使用 `htmltree` 库将构建的HTML对象树渲染并写入到指定路径的 `.html` 文件中。

### 节点: ImageGenerator.__call__

*   **所在代码文件相对路径**: `third_parties/synfintabgen/synfintabgen/generator_image.py`
*   **用途**: 使用Selenium WebDriver加载一个本地HTML文件，并将其内容截图保存为PNG图像。
*   **输入参数**:
    *   `id` (`str`): 图像的唯一标识符，用作文件名。
    *   `document_file_path` (`str`): 要渲染为图像的HTML文件的路径。
*   **输出说明**: 无直接返回值。该方法在指定的图像目录下创建一个PNG文件。
*   **核心调用**:
    *   `self._create_image_from_html(id, document_file_path)`: 内部核心方法，负责加载HTML并截图。
        *   `self._driver.get(document_file_path)`: 使用WebDriver在无头浏览器中打开指定的HTML文件。
        *   `self._driver.save_screenshot(...)`: 将当前浏览器窗口的内容保存为PNG图像文件。

### 节点: AnnotationGenerator.__call__

*   **所在代码文件相对路径**: `third_parties/synfintabgen/synfintabgen/generator_annotation.py`
*   **用途**: 为单个生成的表格样本创建详细的标注信息，并将其暂存到内存中。
*   **输入参数**:
    *   `id` (`str`): 表格的唯一标识符。
    *   `theme_idx` (`int`): 所使用主题的索引。
    *   `table` (`List[List[dict]]`): 结构化的表格数据。
    *   `params` (`dict`): 用于生成该表格的参数字典。
*   **输出说明**: 无直接返回值。此方法将生成的标注字典追加到类的内部列表 `self._tables` 中。
*   **核心调用**:
    *   `self._create_table_dict(...)`: 核心方法，构建包含所有标注信息的字典。
        *   `self._get_bounding_box(...)`: 循环调用，通过 Selenium WebDriver 查询渲染后 HTML 中每个元素（表格、行、单元格、单词）的ID，获取其边界框（bounding box）坐标。
        *   `self._create_q_and_a(...)`: 根据表格内容，随机生成一个问答（Q&A）对。
    *   `self._tables.append(...)`: 将完整的标注字典添加到列表中。

### 节点: AnnotationGenerator.write_to_file

*   **所在代码文件相对路径**: `third_parties/synfintabgen/synfintabgen/generator_annotation.py`
*   **用途**: 将内存中所有已生成的样本标注信息一次性写入到最终的 `annotations.json` 文件中。此方法在所有样本生成循环结束后被调用。
*   **输入参数**: 无。
*   **输出说明**: 无直接返回值。在磁盘上创建或覆盖 `annotations.json` 文件。
*   **核心调用**:
    *   `json.dump(self._tables, ...)`: 将存储在 `self._tables` 列表中的所有标注字典序列化为JSON格式并写入文件。

## 整体用途

该调用链完整地展示了 `SynFinTabGen` 项目的核心工作流程：一个全自动的、端到端的金融表格图像与标注数据集生成管线。其主要用途是为需要进行表格识别、结构恢复、信息提取等任务的机器学习模型，提供大规模、多样化且带有丰富标注信息的合成训练数据。

整个流程始于程序化地创建随机但结构合理的金融表格内容，接着为其应用不同的视觉主题（CSS样式），然后将此表格渲染成HTML文档。随后，通过无头浏览器对此HTML进行截图，生成表格的PNG图像。最关键的是，流程的最后一步会为每个生成的样本创建详尽的标注数据，包括：
-   **结构标注**：表格、行、单元格的层次结构。
-   **内容标注**：每个单元格内的文本内容。
-   **位置标注**：表格、行、单元格乃至每个单词在图像中的精确边界框（bounding box）。
-   **语义标注**：一个基于表格内容自动生成的问答（Q&A）对，可用于训练表格问答模型。

最终产出是一个包含图像、对应HTML源文件以及一个包含所有样本标注的JSON文件的完整数据集。

## 目录结构

调用链涉及的核心代码文件分布如下：

```
third_parties/synfintabgen/
├── main.py (项目入口)
└── synfintabgen/
    ├── configuration_dataset_generator.py (数据集生成器配置)
    ├── generator_dataset.py (数据集生成器，流程总控)
    ├── generator_table.py (表格内容生成器)
    ├── generator_theme.py (表格样式生成器)
    ├── generator_document.py (HTML文档生成器)
    ├── generator_image.py (图像生成器)
    └── generator_annotation.py (标注生成器)
```

## 调用时序图

```mermaid
sequenceDiagram
    participant M as main.py
    participant DG as synfintabgen/generator_dataset.py
    participant TG as synfintabgen/generator_table.py
    participant ThG as synfintabgen/generator_theme.py
    participant DocG as synfintabgen/generator_document.py
    participant IG as synfintabgen/generator_image.py
    participant AG as synfintabgen/generator_annotation.py

    M->>DG: generator = DatasetGenerator()
    M->>DG: generator(size)

    loop for each sample in size
        DG->>TG: table = __call__(params)
        TG-->>DG: returns table_data
        
        DG->>ThG: theme = __call__(idx, params)
        ThG-->>DG: returns theme_dict
        
        DG->>DocG: html_path = __call__(id, table_data, theme_dict)
        DocG-->>DG: returns html_file_path
        
        DG->>IG: __call__(id, html_file_path)
        IG-->>DG: (Saves image to disk)
        
        DG->>AG: __call__(id, theme_idx, table_data, params)
        AG-->>DG: (Appends annotation to memory)
    end

    DG->>AG: write_to_file()
    AG-->>DG: (Writes all annotations to JSON file)
```
