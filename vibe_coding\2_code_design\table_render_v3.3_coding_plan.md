# TableRender V3.3: 精简与聚焦 - 开发计划

**目标:** 基于V3.2的反馈，对样式系统进行一次彻底的精简和逻辑强化。移除向后兼容的复杂性，统一配置项，并用更智能的概率化生成取代静态列表，打造一个更简洁、更强大的样式引擎。

---

## 1. 项目结构与受影响模块

本次开发是一次“减法”和“聚焦”的重构，旨在提升代码质量和配置体验。

```
TableRender/
└── table_render/           # 核心Python包
    ├── config.py           # (重大修改) 大幅简化StyleConfig，移除V3.1兼容代码和冗余配置
    ├── resolver.py         # (重大修改) 移除兼容性逻辑，适配新的字体和颜色生成策略
    ├── utils/              # (受影响)
    │   ├── font_utils.py       # (增强) 实现字体目录的加权随机选择
    │   └── color_utils.py      # (增强) 实现完全动态的、基于概率的颜色生成
    └── builders/           # (轻微修改)
        └── style_builder.py    # (轻微修改) 适配简化后的样式参数
```

**配置文件变更:**
- **删除** `configs/v3.2_compatibility_test.yaml`。
- **重命名/重构** `configs/v3.2_enhanced_style.yaml` 为 `configs/v3.3_default.yaml`，作为新版本的唯一配置模板。

---

## 2. 渐进式开发与集成步骤

我们将本次优化分为4个关键的、可独立验证的步骤。

### 步骤 1: 移除V3.1兼容性，聚焦核心模型

**目标:** 清理代码库，彻底移除为兼容V3.1而存在的旧配置模式，让代码逻辑更纯粹。

**操作:**
1.  **修改 `table_render/config.py`**:
    -   从 `StyleConfig` 中彻底删除对独立 `header` 和 `body` 配置块的支持。
    -   确保 `StyleConfig` 现在只包含 `common`（公共样式）和 `inheritance`（继承配置）两个部分。
2.  **修改 `table_render/resolver.py`**:
    -   在 `resolve_style` 方法中，移除所有用于判断和处理旧版配置的 `if/else` 逻辑分支。
    -   代码现在假定收到的配置一定是V3.3的新结构。
3.  **清理配置文件**: 
    -   删除 `configs/v3.2_compatibility_test.yaml`。
    -   创建一个新的 `configs/v3.3_default.yaml`，仅包含 `common` 和 `inheritance` 结构，作为后续步骤的基础。

**验证标准:**
-   使用V3.1的旧配置文件启动程序，应在Pydantic模型验证阶段就明确报错。
-   使用新的 `v3.3_default.yaml` 配置文件，程序可以正常运行，功能无退化。
-   `config.py` 和 `resolver.py` 的代码行数减少，逻辑更清晰。

### 步骤 2: 统一边框控制，移除冗余配置

**目标:** 将所有边框相关的配置统一到 `border_mode` 下，消除概念混淆。

**操作:**
1.  **修改 `table_render/config.py`**:
    -   在 `CommonStyleConfig` 模型中，找到并**删除** `border` 字段。
2.  **修改 `table_render/resolver.py`**:
    -   检查 `resolve_style` 方法，确保其中不再读取或处理来自 `common_style` 的任何边框信息。
3.  **修改 `table_render/builders/style_builder.py`**:
    -   确认 `StyleBuilder` 收到的样式参数中不包含边框信息，所有边框的CSS规则都基于独立的 `ResolvedBorderParams` 生成。
4.  **更新 `configs/v3.3_default.yaml`**:
    -   确保配置文件中 `common` 样式部分不再有 `border` 键。

**验证标准:**
-   程序可正常运行。
-   表格的边框样式完全由顶层的 `border_mode` 配置项决定，`common_style` 中删除的 `border` 配置不再产生任何影响。

### 步骤 3: 实现字体目录的概率化选择

**目标:** 增强字体配置的灵活性，允许为不同的字体文件夹指定不同的选用概率。

**操作:**
1.  **修改 `table_render/config.py`**:
    -   修改 `FontConfig` 模型。将原来的 `font_dir` 或 `directories` 字段，改造为一个列表，列表项为包含 `path: str` 和 `probability: float` 的新Pydantic模型。
2.  **修改 `table_render/utils/font_utils.py`**:
    -   在 `FontManager` 中，增加一个 `_get_weighted_random_directory()` 方法，该方法根据配置的概率列表，返回一个被选中的字体目录路径。
3.  **修改 `table_render/resolver.py`**:
    -   在 `resolve_style` 流程中，调用 `FontManager` 的新方法来确定本次生成使用哪个字体目录，然后再从该目录中选择具体字体。
4.  **更新 `configs/v3.3_default.yaml`**:
    -   将字体配置更新为新的带概率的列表结构。

**验证标准:**
-   多次运行生成脚本，通过日志或生成结果可以观察到，程序会根据设定的概率从不同的字体文件夹中选取字体。
-   如果某个概率设为1，则每次都从该文件夹选择。

### 步骤 4: 实现颜色生成的完全概率化

**目标:** 移除配置文件中的静态颜色列表，改为“默认色+概率触发”的动态生成模式。

**操作:**
1.  **修改 `table_render/config.py`**:
    -   在 `CommonStyleConfig` 模型中，**删除** `text_color` 和 `background_color` 两个列表字段。
    -   新增一个 `randomize_color_probability: float` 字段。
2.  **修改 `table_render/utils/color_utils.py`**:
    -   增强 `ColorManager`。创建一个核心方法，如 `get_color_pair(probability)`。
    -   该方法内部逻辑为：如果 `random.random() > probability`，则直接返回默认色（如 `("#000000", "#FFFFFF")`）。否则，调用现有的 `generate_soft_color` 和 `ensure_readable_combination` 逻辑，动态生成一对新的、符合对比度要求的颜色并返回。
3.  **修改 `table_render/resolver.py`**:
    -   在 `resolve_style` 中，调用 `ColorManager` 的新方法来获得文本和背景颜色。
4.  **更新 `configs/v3.3_default.yaml`**:
    -   移除所有颜色列表，只保留新的 `randomize_color_probability` 配置项。

**验证标准:**
-   多次运行生成脚本，大部分生成的表格应为黑白默认色。
-   有接近 `randomize_color_probability` 概率的表格，会展现出各种不同的、柔和且清晰的随机颜色搭配。
-   配置文件变得极为简洁。

---

## 3. 预期效果

V3.3版本完成后，将实现以下核心提升：

1.  **配置极简**: 用户不再需要维护冗长的颜色列表，或在两种配置模式间切换，学习成本大大降低。
2.  **代码健壮**: 移除兼容性代码和冗余逻辑，降低了维护成本和潜在的bug风险。
3.  **效果更佳**: 字体和颜色的生成更智能、更可控、更多样，更贴近“一键生成高质量表格”的最终目标。
4.  **逻辑清晰**: 所有配置项都遵循单一职责原则，易于理解和扩展。
