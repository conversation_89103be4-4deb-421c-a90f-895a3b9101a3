# TableRender: 产品需求规划

---

## 1. 规划概述

本文档基于产品需求文档 (PRD)，将 TableRender 项目的开发分解为四个独立的、按优先级排序的迭代版本。每个版本都构建于前一个版本之上，并交付一个可独立使用的功能集。

- **版本 1 (MVP):** 核心渲染流水线
- **版本 2:** 增强的结构与内容
- **版本 3:** 高级样式与真实感
- **版本 4:** 鲁棒性与图像增强
- **版本 5:** LaTeX 渲染引擎与可扩展核心

---

## 2. 迭代版本规划

### 2.1. 版本 1: 核心渲染流水线 (MVP)

**目标:** 验证核心的端到端渲染流水线，能够生成一个基本的、无样式的表格图像及其精确的结构标注。

**功能范围:**

- **输入/输出:**
    - [ ] 实现核心配置对象解析，仅支持最简化的、非概率性配置。
    - [ ] 实现标准化的输出目录结构 (`images/`, `annotations/`, `metadata/`)。
    - [ ] 生成图像 (`.png`)、标注 (`.json`) 和元数据 (`.json`) 文件。
    - [ ] 标注文件必须包含每个单元格的精确 `bbox` 和 `lloc`。
    - [ ] 元数据文件必须包含用于复现的完整配置和随机种子。
- **渲染流水线:**
    - [ ] 搭建完整的HTML/CSS渲染流水线：配置 -> 内部模型 -> HTML/CSS -> 无头浏览器渲染 -> 标注生成。
- **内容生成:**
    - [ ] 仅支持单一内容源：程序化生成随机文本字符串。
- **结构生成:**
    - [ ] 仅支持固定的行数和列数。
    - [ ] **不支持**单元格合并。
    - [ ] **不支持**多级或复杂表头。
- **样式生成:**
    - [ ] **不支持**复杂的样式配置。
    - [ ] 仅支持全局默认的边框（例如，所有单元格都有1像素黑边框）以确保单元格可被识别。
    - [ ] 字体使用浏览器默认。

### 2.2. 版本 2: 增强的结构与内容

**目标:** 扩展数据源和表格结构的复杂性，使其能够生成更多样化、更接近真实数据的表格。

**功能范围 (基于版本 1):**

- **内容生成:**
    - [ ] 新增支持：从指定的 `.csv` 文件加载数据填充表格。
    - [ ] 新增支持：程序化生成格式化数据（例如，日期、货币、百分比）。
    - [ ] 实现对CSV维度与表格结构不匹配情况的概率性处理（截断、留白等）。
- **结构生成:**
    - [ ] 增强维度配置：支持行数和列数在指定范围内随机生成。
    - [ ] 新增支持：根据概率配置实现单元格的行合并 (`rowspan`) 和列合并 (`colspan`)。
    - [ ] 合并范围可配置。

### 2.3. 版本 3: 高级样式与真实感

**目标:** 引入全面的样式控制功能，使用户能够生成外观高度多样化和视觉上逼真的表格。

**功能范围 (基于版本 2):**

- **样式生成:**
    - [ ] 实现分层样式应用逻辑（全局 -> 列 -> 行 -> 单元格）。
    - [ ] 字体：支持从用户指定的目录加载字体文件，并配置字体族、大小、字重和字形。
    - [ ] 颜色：支持配置单元格背景色和文本颜色。
    - [ ] 对齐：支持配置单元格内文本的水平和垂直对齐。
    - [ ] 内边距：支持配置单元格内边距。
    - [ ] 边框：实现对单元格四条边的布尔开关控制。
    - [ ] 斑马纹：新增布尔选项，为行应用交替背景色。
    - [ ] 尺寸：支持随机拉伸/压缩行高和列宽。
- **结构生成:**
    - [ ] 新增支持：创建多级（复杂）表头。

### 2.4. 版本 4: 鲁棒性与图像增强

**目标:** 提升工具的稳定性和可靠性，处理边界情况，并引入可选的图像后处理功能以增加数据真实性。

**功能范围 (基于版本 3):**

- **边界情况与容错性:**
    - [ ] 实现对内容溢出的可配置处理（截断 vs. 文本换行）。
    - [ ] 实现对样式配置冲突的解决机制（随机选择并记录警告）。
- **图像增强 (可选后处理):**
    - [ ] 实现一个可选的图像增强模块。
    - [ ] 支持基本的增强效果，如模糊、噪声和轻微的透视变换。
- **非功能性需求:**
    - [ ] 对标注生成步骤进行性能审查和优化，减少DOM查询瓶颈。
    - [ ] 确保工具能够无人值守地完成大型生成任务。

### 2.5. 版本 5: LaTeX 渲染引擎与可扩展核心

**目标:** 引入对 LaTeX 格式的渲染支持，并重构系统以支持可插拔的渲染引擎，为未来的格式扩展奠定基础。

**功能范围 (基于版本 4):**

- **LaTeX 渲染引擎:**
    - [ ] 设计并实现一个独立的、用于渲染基于 LaTeX 的表格的流水线。
    - [ ] 支持将内部数据模型转换为 LaTeX 代码。
    - [ ] 集成 LaTeX 编译环境（如 MiKTeX, TeX Live）以生成图像。
    - [ ] 解决从 LaTeX 输出中提取精确单元格标注的挑战。
- **可扩展核心重构:**
    - [ ] 重构核心系统，定义统一的渲染引擎接口。
    - [ ] 将现有的 HTML/CSS 渲染器和新的 LaTeX 渲染器适配到该接口，使其成为可插拔模块。
    - [ ] 更新配置对象，允许用户选择使用哪个渲染引擎 (`html` 或 `latex`)。
