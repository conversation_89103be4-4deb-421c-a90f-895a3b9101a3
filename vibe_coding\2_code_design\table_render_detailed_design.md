# TableRender: 详细设计文档 (LLD)

本文档基于产品需求文档 (PRD) 和现有代码分析，为 `TableRender` 工具提供详细的软件设计。

## 1. 项目结构与总体设计

### 1.1. 项目目录结构

为了确保项目的可维护性和易用性，我们采用以下结构来组织代码、配置和资源：

```
TableRender/
├── table_render/           # 核心Python包
│   ├── __init__.py
│   ├── main.py             # 命令行入口
│   ├── config.py           # Pydantic 配置模型
│   ├── main_generator.py   # 核心生成器
│   ├── models/             # 内部数据模型 (TableModel, etc.)
│   ├── builders/           # 构建器 (Structure, Content, Style)
│   ├── renderers/          # 渲染器 (HtmlRenderer)
│   ├── postprocessors/     # 图像后处理器
│   └── utils/              # 辅助工具 (FileUtils, etc.)
│
├── configs/                # 存放所有 .yaml 配置文件
│   ├── default.yaml        # 一个基础的、可直接运行的配置示例
│   └── complex_style.yaml  # 另一个具有复杂样式的配置示例
│
├── assets/                 # 存放渲染所需的静态资源
│   ├── fonts/              # 存放 .ttf, .otf 等字体文件
│   └── backgrounds/        # 存放可选的背景图片
│
├── data/                   # 存放用于生成内容的数据源
│   ├── csv/                # .csv 文件
│   └── txt/                # .txt 文件 (如单词列表)
│
├── output/                 # 默认的输出目录，用于存放生成的图像和标注
│
├── requirements.txt        # 项目依赖
└── README.md               # 项目说明文档
```

**说明:**
- **`configs/`**: 用户可以通过命令行指定使用哪个配置文件。例如 `python -m table_render.main --config configs/complex_style.yaml`。
- **`assets/` 和 `data/`**: 在配置文件中，引用这些资源时可以使用相对路径（相对于项目根目录），使得项目可以轻松地被移动和分享。例如，在样式配置中可以只写 `font_name: "Alibaba-PuHuiTi-Regular.ttf"`，`StyleBuilder` 会自动在 `assets/fonts/` 目录下查找此文件。
- **`output/`**: 此目录应被添加到 `.gitignore` 中，以避免将生成的大量数据提交到版本控制。

## 2. 整体逻辑和交互时序图

核心工作流程遵循一个清晰的、分阶段的流水线模式，由 `MainGenerator` 协调。该流程确保了逻辑构建、样式生成和最终渲染的完全分离。

1.  **初始化**: 用户通过 `main.py` 启动程序，提供配置文件路径。
2.  **配置加载与验证**: `main.py` 加载 JSON 配置，并使用 `config.py` 中定义的 Pydantic 模型进行解析和验证，生成一个结构化的 `RenderConfig` 对象。
3.  **生成器启动**: `main.py` 实例化并调用 `MainGenerator`，传入 `RenderConfig` 对象和要生成的样本数量。
4.  **核心流水线 (循环执行)**: 对于要生成的每个样本：
    a.  **构建内部模型**: `MainGenerator` 依次调用 `StructureBuilder` 和 `ContentBuilder`。它们根据配置，共同创建一个与渲染无关的、纯粹的 `TableModel` 实例。这个实例包含了表格的所有逻辑信息（行、列、单元格内容、合并信息）。
    b.  **生成样式**: `MainGenerator` 调用 `StyleBuilder`，根据配置生成适用于 HTML 渲染的 CSS 样式字符串。
    c.  **选择渲染器**: `MainGenerator` 根据配置（例如 `config.render_engine = 'html'`）从 `renderers` 模块中选择合适的渲染器实例（这里是 `HtmlRenderer`）。
    d.  **执行渲染**: `MainGenerator` 调用渲染器的 `render()` 方法，将 `TableModel` 和 CSS 样式作为输入。`HtmlRenderer` 内部完成以下工作：
        i.  将 `TableModel` 转换为 HTML 字符串。
        ii. 使用 Playwright 启动一个无头浏览器页面。
        iii. 将 HTML 和 CSS 注入页面，渲染出表格图像。
        iv. 执行一段预设的 JavaScript 脚本，一次性地获取所有单元格和行文本的边界框（`bbox`）信息。
        v.  对页面进行截图。
        vi. 关闭浏览器页面。
    e.  **图像后处理 (可选)**: 如果配置中指定了图像增强，`MainGenerator` 会将“干净”的图像和标注数据传递给 `ImageAugmentor`，后者对图像进行变换（如模糊、噪声）并相应地调整标注中的 `bbox` 坐标。
    f.  **保存输出**: `MainGenerator` 调用 `FileUtils`，将最终的图像、JSON 标注和元数据（该样本的配置）保存到指定的输出目录结构中。

### 交互时序图 (Sequence Diagram)

```mermaid
sequenceDiagram
    participant User
    participant main.py
    participant MainGenerator
    participant StructureBuilder
    participant ContentBuilder
    participant StyleBuilder
    participant HtmlRenderer
    participant FileUtils

    User->>main.py: 提供 config.json 和样本数量
    main.py->>MainGenerator:  __init__(config)
    main.py->>MainGenerator: generate(num_samples)

    loop 每个样本
        MainGenerator->>StructureBuilder: build(config.structure)
        StructureBuilder-->>MainGenerator: 返回 TableModel (仅结构)

        MainGenerator->>ContentBuilder: build(config.content, TableModel)
        ContentBuilder-->>MainGenerator: 返回 TableModel (填充内容)

        MainGenerator->>StyleBuilder: build(config.style)
        StyleBuilder-->>MainGenerator: 返回 CSS 字符串

        MainGenerator->>HtmlRenderer: render(TableModel, css)
        Note right of HtmlRenderer: 1. TableModel -> HTML<br/>2. Playwright 启动<br/>3. 渲染并截图<br/>4. JS 获取所有 bbox<br/>5. 返回图像和标注
        HtmlRenderer-->>MainGenerator: 返回 (image, annotations)

        MainGenerator->>FileUtils: save(image, annotations, config)
        FileUtils-->>MainGenerator: 保存成功
    end

    MainGenerator-->>main.py: 生成完毕
```

## 3. 数据实体结构深化

内部数据模型是连接“逻辑构建”和“物理渲染”的桥梁。它被设计为纯粹的、与任何特定渲染格式（HTML, LaTeX等）无关的数据容器。

-   **TableModel**: 表格的顶层容器。
    -   `rows` (List[RowModel]): 包含表格中所有行的列表。
    -   `num_rows` (int): 表格的总行数。
    -   `num_cols` (int): 表格的总列数。
-   **RowModel**: 代表表格中的一行。
    -   `cells` (List[CellModel]): 包含该行中所有单元格的列表。
    -   `row_index` (int): 该行在表格中的逻辑索引（从0开始）。
-   **CellModel**: 代表一个单元格，是信息的核心载体。
    -   `cell_id` (str): 一个唯一的标识符（如 `cell-0-0`），用于在渲染后精确匹配标注。
    -   `row_index` (int): 单元格所在的行索引。
    -   `col_index` (int): 单元格所在的列索引。
    -   `row_span` (int): 单元格跨越的行数（默认为1）。
    -   `col_span` (int): 单元格跨越的列数（默认为1）。
    -   `content` (str): 单元格的文本内容。
    -   `is_header` (bool): 标记该单元格是否为表头（用于生成 `<th>` 或 `<td>`）。

### 实体关系图 (ER Diagram)

```mermaid
erDiagram
    TABLE_MODEL ||--o{ ROW_MODEL : contains
    ROW_MODEL ||--o{ CELL_MODEL : contains

    TABLE_MODEL {
        int num_rows
        int num_cols
    }

    ROW_MODEL {
        int row_index
    }

    CELL_MODEL {
        string cell_id
        int row_index
        int col_index
        int row_span
        int col_span
        string content
        bool is_header
    }
```

## 4. 配置项

系统由一个单一的、结构化的配置对象驱动。我们将使用 Pydantic 模型来定义和验证这个配置。以下是配置对象的顶层结构，详细的子结构将在文件详解部分展开。

```python
# config.py (Pydantic Model Pseudocode)

class RenderConfig(BaseModel):
    # 1. 输出设置
    output: OutputConfig

    # 2. 渲染引擎设置
    renderer: RendererConfig

    # 3. 表格结构设置
    structure: StructureConfig

    # 4. 内容生成设置
    content: ContentConfig

    # 5. 样式生成设置
    style: StyleConfig

    # 6. (可选) 图像后处理设置
    postprocessing: Optional[PostprocessingConfig] = None

    # 7. 全局设置
    seed: int = 42  # 用于可复现性的随机种子
```

## 5. 模块化文件详解 (File-by-File Breakdown)

本节将逐一详细描述在目录结构中定义的每个代码文件。

### 5.1. `main.py`

a. **文件用途说明**

作为项目的命令行入口。它使用 `argparse` 库来接收用户输入的参数（如配置文件路径和生成数量），加载并验证配置，然后初始化并启动 `MainGenerator` 来执行核心生成任务。

b. **文件内类图**

(该文件不包含类)

c. **函数/方法详解**

#### `main()`

-   **用途**: 解析命令行参数，协调整个生成流程。
-   **输入参数**: 无 (通过 `sys.argv` 从命令行获取)。
-   **输出**: 无 (将文件写入磁盘)。
-   **伪代码和注释**:

    ```python
    # 导入必要的库: argparse, json, pydantic, logging
    # 从 table_render.config 导入 RenderConfig
    # 从 table_render.main_generator 导入 MainGenerator

    def main():
        # 1. 设置日志记录器 (Logger)
        #    配置一个基本的日志系统，用于输出程序执行状态和错误信息。

        # 2. 设置命令行参数解析器 (ArgumentParser)
        parser = argparse.ArgumentParser(description="TableRender: 一个可控的表格图像合成工具")
        parser.add_argument("--config", type=str, required=True, help="指向JSON配置文件的路径")
        parser.add_argument("--num-samples", type=int, required=True, help="要生成的样本数量")
        args = parser.parse_args()

        # 3. 加载和验证配置
        try:
            # 打开并读取用户提供的JSON配置文件
            with open(args.config, 'r') as f:
                config_data = json.load(f)
            
            # 使用Pydantic模型验证和解析配置
            # 如果配置不符合RenderConfig的结构，Pydantic会自动抛出ValidationError
            render_config = RenderConfig(**config_data)
            log.info("配置加载并验证成功。")

        except FileNotFoundError:
            log.error(f"错误：配置文件未找到于 {args.config}")
            return
        except json.JSONDecodeError:
            log.error(f"错误：配置文件 {args.config} 不是一个有效的JSON。")
            return
        except ValidationError as e:
            log.error(f"错误：配置验证失败。\n{e}")
            return

        # 4. 初始化并运行主生成器
        try:
            log.info(f"开始生成 {args.num_samples} 个样本...")
            generator = MainGenerator(render_config)
            generator.generate(args.num_samples)
            log.info("所有样本生成完毕。")

        except Exception as e:
            # 捕获在生成过程中可能发生的任何未预料到的异常
            log.critical(f"生成过程中发生严重错误: {e}", exc_info=True)

    if __name__ == "__main__":
        main()
    ```

### 5.2. `table_render/config.py`

a. **文件用途说明**

定义整个项目的所有配置项。使用 Pydantic 模型来创建严格、结构化且自文档化的配置体系。这确保了在程序启动时就能对用户提供的配置进行有效的验证。

b. **文件内类图**

```mermaid
classDiagram
    class RenderConfig {
        +OutputConfig output
        +RendererConfig renderer
        +StructureConfig structure
        +ContentConfig content
        +StyleConfig style
        +PostprocessingConfig postprocessing
        +int seed
    }
    class OutputConfig {
        +str output_dir
        +str image_format
        +int samples_per_dir
    }
    class RendererConfig {
        +Literal['html', 'latex'] engine
        +int browser_width
        +int browser_height
    }
    class StructureConfig {
        # ... (详细字段)
    }
    class ContentConfig {
        # ... (详细字段)
    }
    class StyleConfig {
        # ... (详细字段)
    }
    RenderConfig *-- "1" OutputConfig
    RenderConfig *-- "1" RendererConfig
    RenderConfig *-- "1" StructureConfig
    RenderConfig *-- "1" ContentConfig
    RenderConfig *-- "1" StyleConfig
```

c. **函数/方法详解**

(该文件主要包含Pydantic模型定义，无复杂函数)

#### Pydantic 模型定义

-   **用途**: 定义配置结构。
-   **伪代码和注释**:

    ```python
    # 导入必要的库: pydantic, typing (List, Optional, Literal, Tuple)

    # ---- 子配置模型 ----

    class OutputConfig(BaseModel):
        """输出设置"""
        output_dir: str = "./output/"
        image_format: Literal['png', 'jpeg'] = 'png'
        samples_per_dir: int = 500

    class RendererConfig(BaseModel):
        """渲染器设置"""
        engine: Literal['html', 'latex'] = 'html' # 明确支持的渲染引擎
        browser_width: int = 1920
        browser_height: int = 1080
        # 此处为未来LaTeX渲染器留出扩展空间
        # latex_compiler: str = 'pdflatex'

    class RangeConfig(BaseModel):
        """通用范围配置，用于表示 min-max"""
        min: int
        max: int

    class StructureConfig(BaseModel):
        """表格结构设置"""
        rows: Union[int, RangeConfig]  # 固定行数或一个范围
        cols: Union[int, RangeConfig]  # 固定列数或一个范围
        merge_probability: float = 0.1 # 单元格合并概率
        max_row_span: int = 3
        max_col_span: int = 3
        header_rows: Union[int, RangeConfig] = 1 # 表头行数

    class ContentSourceConfig(BaseModel):
        """从txt文件读取内容的配置"""
        file_path: str
        encoding: str = 'utf-8'

    class CSVSourceConfig(BaseModel):
        """从CSV文件读取内容的配置"""
        file_path: str
        encoding: str = 'utf-8'
        mismatch_strategy: Literal['truncate', 'fill_empty', 'fill_random'] = 'truncate'

    class ContentConfig(BaseModel):
        """内容生成设置"""
        # 定义多种内容来源，程序将根据配置选择其一
        source_type: Literal['programmatic', 'csv', 'text_file']
        programmatic_types: Optional[List[str]] = ["date", "currency", "percentage"]
        text_file_source: Optional[ContentSourceConfig] = None
        csv_source: Optional[CSVSourceConfig] = None

    class FontConfig(BaseModel):
        """字体设置"""
        font_dir: str # 存放 .ttf/.otf 文件的目录
        default_family: str = "Arial"
        default_size: Union[int, RangeConfig] = 14
        bold_probability: float = 0.1
        italic_probability: float = 0.1

    class StyleConfig(BaseModel):
        """样式生成设置"""
        font: FontConfig
        text_color: Union[str, List[str]] = "#000000" # 固定颜色或颜色列表
        background_color: Union[str, List[str]] = "#FFFFFF"
        horizontal_align: List[Literal['left', 'center', 'right']] = ["left", "center", "right"]
        vertical_align: List[Literal['top', 'middle', 'bottom']] = ["top", "middle", 'bottom']
        border_on: bool = True
        padding: Union[int, RangeConfig] = 5
        zebra_stripes: bool = False

    class PostprocessingConfig(BaseModel):
        """图像后处理设置"""
        blur_probability: float = 0.0
        noise_probability: float = 0.0
        perspective_probability: float = 0.0

    # ---- 主配置模型 ----

    class RenderConfig(BaseModel):
        """项目总配置"""
        output: OutputConfig
        renderer: RendererConfig
        structure: StructureConfig
        content: ContentConfig
        style: StyleConfig
        postprocessing: Optional[PostprocessingConfig] = None
        seed: int = 42
    ```

### 5.3. `table_render/models/table_model.py`

a. **文件用途说明**

定义项目的核心内部数据模型。这些模型是纯粹的数据容器，用于在内存中表示一个完整的表格，包括其结构和内容。它们与任何特定的渲染技术（如HTML或LaTeX）解耦，是连接“构建器”和“渲染器”的桥梁。

b. **文件内类图**

```mermaid
classDiagram
    class TableModel {
        +List~RowModel~ rows
        +int num_rows
        +int num_cols
    }
    class RowModel {
        +List~CellModel~ cells
        +int row_index
    }
    class CellModel {
        +str cell_id
        +int row_index
        +int col_index
        +int row_span
        +int col_span
        +str content
        +bool is_header
    }
    TableModel o-- "*" RowModel
    RowModel o-- "*" CellModel
```

c. **函数/方法详解**

(该文件主要包含 dataclass 定义，无复杂函数)

#### Dataclass 定义

-   **用途**: 定义核心数据结构。
-   **伪代码和注释**:

    ```python
    # 导入必要的库: dataclasses, typing (List)
    from dataclasses import dataclass, field

    @dataclass
    class CellModel:
        """代表一个单元格的数据模型"""
        cell_id: str
        row_index: int
        col_index: int
        row_span: int = 1
        col_span: int = 1
        content: str = ""
        is_header: bool = False

    @dataclass
    class RowModel:
        """代表一行的数据模型"""
        cells: List[CellModel] = field(default_factory=list)
        row_index: int = -1

    @dataclass
    class TableModel:
        """代表整个表格的数据模型"""
        rows: List[RowModel] = field(default_factory=list)

        @property
        def num_rows(self) -> int:
            """计算表格的总行数"""
            return len(self.rows)

        @property
        def num_cols(self) -> int:
            """计算表格的总列数。假设为规则表格。"""
            if not self.rows:
                return 0
            # 通常情况下，我们计算第一行的单元格数量（考虑colspan）
            # 在一个规范化的模型中，这应该是固定的
            # 更准确的计算可能需要遍历所有单元格，但对于构建阶段，这个假设是合理的
            return sum(cell.col_span for cell in self.rows[0].cells)

    ```

### 5.4. `table_render/builders/structure_builder.py`

a. **文件用途说明**

负责根据 `StructureConfig` 配置来创建表格的结构骨架。这包括确定表格的尺寸（行数和列数），并根据设定的概率和约束随机地合并单元格。它生成一个只包含结构信息（如 `row_span`, `col_span`）的 `TableModel`，内容为空。

b. **文件内类图**

```mermaid
classDiagram
    class StructureBuilder {
        -random_state: np.random.RandomState
        +build(config: StructureConfig) TableModel
        -_get_randomized_value(value: Union[int, RangeConfig]) int
        -_create_initial_grid(num_rows: int, num_cols: int) List~List~Optional~CellModel~~
        -_merge_cells(grid: List~List~...~~, config: StructureConfig) void
        -_convert_grid_to_model(grid: List~List~...~~) TableModel
    }
```

c. **函数/方法详解**

#### `__init__(self, seed: int)`
-   **用途**: 初始化构建器，并创建一个基于种子的 `numpy.random.RandomState` 实例，以确保结构生成的可复现性。

#### `build(self, config: StructureConfig) -> TableModel`

-   **用途**: 作为主入口方法，协调整个结构构建过程。
-   **输入参数**: `config` (StructureConfig): 表格结构配置。
-   **输出**: `TableModel`: 一个仅包含结构信息的表格模型。
-   **伪代码和注释**:

    ```python
    def build(self, config: StructureConfig) -> TableModel:
        # 1. 根据配置确定最终的行数和列数
        #    使用 _get_randomized_value 辅助函数处理固定值或范围值
        num_rows = self._get_randomized_value(config.rows)
        num_cols = self._get_randomized_value(config.cols)
        header_rows = self._get_randomized_value(config.header_rows)

        # 2. 创建一个初始的二维网格 (grid)
        #    网格中的每个位置都填充一个基础的 CellModel
        grid = self._create_initial_grid(num_rows, num_cols, header_rows)

        # 3. 如果配置了合并概率，则执行单元格合并逻辑
        if config.merge_probability > 0:
            self._merge_cells(grid, config)

        # 4. 将内部使用的网格结构转换为最终的 TableModel
        table_model = self._convert_grid_to_model(grid)

        return table_model
    ```

#### `_merge_cells(self, grid: List[List[Optional[CellModel]]], config: StructureConfig)`

-   **用途**: 核心的单元格合并逻辑。遍历网格，根据概率决定是否尝试合并，并确保合并的有效性。
-   **伪代码和注释**:

    ```python
    def _merge_cells(self, grid, config):
        num_rows = len(grid)
        num_cols = len(grid[0])

        # 遍历网格中的每一个单元格作为潜在的合并起始点
        for r in range(num_rows):
            for c in range(num_cols):
                # 如果当前单元格已经被其他合并操作覆盖，则跳过
                if grid[r][c] is None:
                    continue

                # 根据概率决定是否尝试合并
                if self.random_state.rand() < config.merge_probability:
                    # 随机确定要跨越的行数和列数
                    # 范围是从1到配置的最大值，且不能超出表格边界
                    max_span_r = min(config.max_row_span, num_rows - r)
                    max_span_c = min(config.max_col_span, num_cols - c)
                    
                    row_span = self.random_state.randint(1, max_span_r + 1)
                    col_span = self.random_state.randint(1, max_span_c + 1)

                    # 如果跨度为1x1，则相当于不合并，跳过
                    if row_span == 1 and col_span == 1:
                        continue

                    # 检查目标合并区域内的所有单元格是否都可用（即未被其他合并操作占据）
                    is_merge_possible = True
                    for dr in range(row_span):
                        for dc in range(col_span):
                            if grid[r + dr][c + dc] is None:
                                is_merge_possible = False
                                break
                        if not is_merge_possible: break

                    # 如果可以合并，则执行合并操作
                    if is_merge_possible:
                        # 更新起始单元格的 row_span 和 col_span
                        grid[r][c].row_span = row_span
                        grid[r][c].col_span = col_span

                        # 将被合并的单元格在网格中标记为 None
                        for dr in range(row_span):
                            for dc in range(col_span):
                                if dr == 0 and dc == 0: continue # 跳过起始单元格本身
                                grid[r + dr][c + dc] = None
    ```

#### `_convert_grid_to_model(self, grid: List[List[Optional[CellModel]]]) -> TableModel`

-   **用途**: 将内部的二维网格表示转换为 `TableModel` 和 `RowModel` 的最终形式。
-   **伪代码和注释**:

    ```python
    def _convert_grid_to_model(self, grid) -> TableModel:
        table_model = TableModel()
        for r_idx, row_data in enumerate(grid):
            row_model = RowModel(row_index=r_idx)
            for cell in row_data:
                # 只添加实际的单元格，忽略被合并的 None 占位符
                if cell is not None:
                    row_model.cells.append(cell)
            table_model.rows.append(row_model)
        return table_model
    ```

### 5.5. `table_render/builders/content_builder.py`

a. **文件用途说明**

负责为已经具备结构的 `TableModel` 填充实际的文本内容。它根据 `ContentConfig` 的配置，从不同的数据源（程序化生成、CSV文件、文本文件）获取数据，并将其填入表格的每个单元格中。

b. **文件内类图**

```mermaid
classDiagram
    class ContentBuilder {
        -random_state: np.random.RandomState
        -faker: faker.Faker
        +build(table_model: TableModel, config: ContentConfig) TableModel
        -_fill_programmatically(table_model: TableModel, config: ContentConfig) void
        -_fill_from_csv(table_model: TableModel, config: ContentConfig) void
        -_fill_from_text_file(table_model: TableModel, config: ContentConfig) void
        -_get_random_programmatic_content(types: List[str]) str
    }
```

c. **函数/方法详解**

#### `__init__(self, seed: int)`
-   **用途**: 初始化构建器，创建一个 `numpy.random.RandomState` 实例用于可复现的随机选择，并初始化 `faker` 库用于程序化内容生成。

#### `build(self, table_model: TableModel, config: ContentConfig) -> TableModel`

-   **用途**: 主入口方法。根据 `config.source_type` 调用相应的填充方法。
-   **输入参数**:
    -   `table_model` (TableModel): 由 `StructureBuilder` 生成的、仅有结构的表格模型。
    -   `config` (ContentConfig): 内容生成配置。
-   **输出**: `TableModel`: 填充了内容的表格模型。
-   **伪代码和注释**:

    ```python
    def build(self, table_model: TableModel, config: ContentConfig) -> TableModel:
        # 根据配置的 source_type 分派到不同的处理函数
        source_type = config.source_type
        if source_type == 'programmatic':
            self._fill_programmatically(table_model, config)
        elif source_type == 'csv':
            if config.csv_source is None:
                raise ValueError("CSV source is selected but 'csv_source' config is missing.")
            self._fill_from_csv(table_model, config)
        elif source_type == 'text_file':
            if config.text_file_source is None:
                raise ValueError("Text file source is selected but 'text_file_source' config is missing.")
            self._fill_from_text_file(table_model, config)
        else:
            raise NotImplementedError(f"Content source type '{source_type}' is not supported.")

        return table_model
    ```

#### `_fill_programmatically(self, table_model: TableModel, config: ContentConfig)`

-   **用途**: 遍历表格模型，使用 `faker` 或其他程序化方法生成内容填充每个单元格。
-   **伪代码和注释**:

    ```python
    def _fill_programmatically(self, table_model, config):
        # 遍历 TableModel 中的每一个单元格
        for row in table_model.rows:
            for cell in row.cells:
                # 调用辅助函数获取一个随机类型的程序化内容
                cell.content = self._get_random_programmatic_content(config.programmatic_types)
    ```

#### `_fill_from_csv(self, table_model: TableModel, config: ContentConfig)`

-   **用途**: 从 CSV 文件加载数据，并根据 `mismatch_strategy` 策略填充表格。
-   **伪代码和注释**:

    ```python
    def _fill_from_csv(self, table_model, config):
        # 1. 加载CSV数据
        #    使用 pandas 或 csv 库读取 csv_source.file_path 的内容
        #    将数据存储为 List[List[str]]
        csv_data = self._load_csv_data(config.csv_source)
        num_csv_rows = len(csv_data)
        num_csv_cols = len(csv_data[0]) if num_csv_rows > 0 else 0

        # 2. 遍历 TableModel 填充内容
        for r_idx, row in enumerate(table_model.rows):
            for c_idx, cell in enumerate(row.cells):
                # 检查CSV数据源是否越界
                if r_idx < num_csv_rows and c_idx < num_csv_cols:
                    cell.content = csv_data[r_idx][c_idx]
                else:
                    # 处理行列数不匹配的情况
                    strategy = config.csv_source.mismatch_strategy
                    if strategy == 'fill_empty':
                        cell.content = ""
                    elif strategy == 'fill_random':
                        cell.content = self._get_random_programmatic_content(config.programmatic_types)
                    # 如果是 'truncate'，则不执行任何操作，内容将保持默认的空字符串
    ```

#### `_fill_from_text_file(self, table_model: TableModel, config: ContentConfig)`

-   **用途**: 从文本文件加载词汇列表，并随机选择词汇填充表格。
-   **伪代码和注释**:

    ```python
    def _fill_from_text_file(self, table_model, config):
        # 1. 加载文本文件内容
        #    读取 text_file_source.file_path 的内容
        #    将文件内容按行或空格分割，存为一个词汇列表 List[str]
        word_list = self._load_text_data(config.text_file_source)
        if not word_list:
            # 如果文件为空或无法读取，则不进行填充
            return

        # 2. 遍历 TableModel 填充内容
        for row in table_model.rows:
            for cell in row.cells:
                # 从词汇列表中随机选择一个或多个词汇
                cell.content = self.random_state.choice(word_list)
    ```

### 5.6. `table_render/builders/style_builder.py`

a. **文件用途说明**

负责根据 `StyleConfig` 配置动态生成 CSS (Cascading Style Sheets) 样式字符串。这些样式将直接应用于 HTML 渲染器，控制表格的视觉外观，包括字体、颜色、边框、对齐方式等。

b. **文件内类图**

```mermaid
classDiagram
    class StyleBuilder {
        -random_state: np.random.RandomState
        +build(config: StyleConfig) str
        -_generate_font_face_rules(config: FontConfig) str
        -_generate_global_rules() str
        -_generate_cell_rules(config: StyleConfig) str
        -_generate_class_based_rules(config: StyleConfig) str
        -_get_random_choice(options: Union[Any, List[Any]]) Any
    }
```

c. **函数/方法详解**

#### `__init__(self, seed: int)`
-   **用途**: 初始化构建器，并创建 `numpy.random.RandomState` 实例以确保随机样式选择的可复现性。

#### `build(self, config: StyleConfig) -> str`

-   **用途**: 主入口方法，调用各个辅助方法生成不同的 CSS 部分，并将它们组合成一个完整的样式字符串。
-   **输入参数**: `config` (StyleConfig): 样式配置。
-   **输出**: `str`: 一个包含所有 CSS 规则的字符串。
-   **伪代码和注释**:

    ```python
    def build(self, config: StyleConfig) -> str:
        css_parts = []

        # 1. 生成 @font-face 规则，用于引入自定义字体
        font_face_rules = self._generate_font_face_rules(config.font)
        css_parts.append(font_face_rules)

        # 2. 生成全局和表格基础样式
        global_rules = self._generate_global_rules()
        css_parts.append(global_rules)

        # 3. 生成所有单元格 (td, th) 的通用样式
        cell_rules = self._generate_cell_rules(config)
        css_parts.append(cell_rules)

        # 4. (可选) 生成斑马条纹样式
        if config.zebra_stripes:
            zebra_rule = "tr:nth-child(even) { background-color: #f2f2f2; }"
            css_parts.append(zebra_rule)
        
        # 5. 生成基于类的随机样式（用于单个单元格的随机化）
        #    这部分在渲染器中，会为每个cell随机分配class
        class_rules = self._generate_class_based_rules(config)
        css_parts.append(class_rules)

        return "\n".join(css_parts)
    ```

#### `_generate_font_face_rules(self, font_config: FontConfig) -> str`

-   **用途**: 扫描字体目录，为找到的每种字体生成 `@font-face` CSS 规则。
-   **伪代码和注释**:

    ```python
    def _generate_font_face_rules(self, font_config: FontConfig) -> str:
        # 1. 使用 os.listdir 或 glob 遍历 font_config.font_dir 目录下的 .ttf 和 .otf 文件
        # 2. 对于每个字体文件:
        #    - 提取字体族名 (font-family)，通常是文件名（不含扩展名）
        #    - 生成一个 @font-face 规则字符串
        #      @font-face {
        #          font-family: 'FontName';
        #          src: url('path/to/font.ttf');
        #      }
        # 3. 将所有规则拼接成一个字符串
        # 4. 如果目录为空或不存在，则返回空字符串
        pass
    ```

#### `_generate_cell_rules(self, config: StyleConfig) -> str`

-   **用途**: 生成应用于所有 `<th>` 和 `<td>` 元素的 CSS 规则。
-   **伪代码和注释**:

    ```python
    def _generate_cell_rules(self, config: StyleConfig) -> str:
        # 1. 随机选择一个默认字体族
        #    从 font_config.font_dir 扫描到的字体列表或默认字体中选择
        default_font = self._get_random_choice(config.font.default_family_options)

        # 2. 确定字体大小
        font_size = self._get_randomized_value(config.font.default_size)

        # 3. 确定内外边距
        padding = self._get_randomized_value(config.padding)

        # 4. 确定边框样式
        border_style = "1px solid black" if config.border_on else "none"

        # 5. 组装 CSS 规则
        return f"""
        th, td {{
            font-family: '{default_font}', sans-serif;
            font-size: {font_size}px;
            padding: {padding}px;
            border: {border_style};
            text-align: {self._get_random_choice(config.horizontal_align)};
            vertical-align: {self._get_random_choice(config.vertical_align)};
            color: {self._get_random_choice(config.text_color)};
            background-color: {self._get_random_choice(config.background_color)};
        }}
        """
    ```

#### `_generate_class_based_rules(self, config: StyleConfig) -> str`

-   **用途**: 创建一系列 CSS 类，这些类可以被随机分配给单个单元格，以实现更细粒度的样式多样性。
-   **伪代码和注释**:

    ```python
    def _generate_class_based_rules(self, config: StyleConfig) -> str:
        rules = []
        # 为每种水平对齐方式创建一个类
        for align in config.horizontal_align:
            rules.append(f".text-align-{align} {{ text-align: {align}; }}")
        
        # 为粗体和斜体创建类
        rules.append(".font-bold { font-weight: bold; }")
        rules.append(".font-italic { font-style: italic; }")

        # 为备选的文本颜色和背景色创建类
        if isinstance(config.text_color, list) and len(config.text_color) > 1:
            for i, color in enumerate(config.text_color):
                rules.append(f".text-color-{i} {{ color: {color}; }}")

        return "\n".join(rules)
    ```

### 5.7. `table_render/renderers/html_renderer.py`

a. **文件用途说明**

负责将 `TableModel` 和 CSS 样式字符串转换为最终的表格图像和精确的标注数据。它使用 Playwright 库来驱动一个无头浏览器，完成 HTML 的渲染、截图和 `bbox` 的提取。这是一个异步模块，以充分利用I/O操作的效率。

b. **文件内类图**

```mermaid
classDiagram
    class HtmlRenderer {
        -playwright: Playwright
        -browser: Browser
        +render(model: TableModel, css: str, config: RendererConfig) Tuple[bytes, dict]
        -_table_model_to_html(model: TableModel, css: str) str
        -_get_annotations_script() str
        +close_async()
    }
```

c. **函数/方法详解**

#### `__init__` & `create_async` (Static Method)
-   **用途**: 由于 `__init__` 不能是 `async` 的，我们使用一个异步的静态工厂方法 `create_async` 来正确地初始化 Playwright 和浏览器实例。构造函数本身保持简单。
-   **伪代码和注释**:

    ```python
    class HtmlRenderer:
        def __init__(self, playwright, browser):
            self.playwright = playwright
            self.browser = browser

        @staticmethod
        async def create_async():
            # 启动 Playwright
            playwright = await async_playwright().start()
            # 启动一个浏览器实例 (例如, chromium)
            browser = await playwright.chromium.launch()
            return HtmlRenderer(playwright, browser)

        async def close_async(self):
            # 关闭浏览器和 Playwright
            await self.browser.close()
            await self.playwright.stop()
    ```

#### `render(self, model: TableModel, css: str, config: RendererConfig) -> Tuple[bytes, dict]`

-   **用途**: 核心渲染方法。协调从 HTML 生成到截图和标注提取的全过程。
-   **输入参数**:
    -   `model` (TableModel): 包含结构和内容的表格模型。
    -   `css` (str): 由 `StyleBuilder` 生成的 CSS 样式。
    -   `config` (RendererConfig): 渲染器配置，如浏览器视口大小。
-   **输出**: `Tuple[bytes, dict]`: 包含图像二进制数据和标注字典的元组。
-   **伪代码和注释**:

    ```python
    async def render(self, model, css, config):
        # 1. 将 TableModel 转换为完整的 HTML 字符串
        html_content = self._table_model_to_html(model, css)

        # 2. 创建一个新的浏览器页面
        page = await self.browser.new_page()
        await page.set_viewport_size({"width": config.browser_width, "height": config.browser_height})

        # 3. 将 HTML 内容设置到页面上
        #    使用 file:// 协议或 data URI 来避免网络请求，提高速度和隔离性
        await page.set_content(html_content)

        # 4. 定位到表格元素，准备截图
        table_element = await page.query_selector("#main-table")
        if not table_element:
            raise RuntimeError("无法在页面中找到ID为 'main-table' 的表格元素")

        # 5. 获取标注数据
        #    执行JS脚本来获取所有单元格的 bounding boxes
        annotation_script = self._get_annotations_script()
        annotations = await page.evaluate(annotation_script)

        # 6. 对表格元素进行截图
        image_bytes = await table_element.screenshot(type=config.image_format)

        # 7. 关闭页面，释放资源
        await page.close()

        return image_bytes, annotations
    ```

#### `_table_model_to_html(self, model: TableModel, css: str) -> str`

-   **用途**: 将 `TableModel` 对象序列化为 HTML 字符串。
-   **伪代码和注释**:

    ```python
    def _table_model_to_html(self, model, css):
        # 1. 创建 HTML 表格的基本结构
        html = "<html><head><style>" + css + "</style></head><body>"
        html += "<table id='main-table'>"

        # 2. 遍历 TableModel 的行和单元格
        for row_model in model.rows:
            html += "<tr>"
            for cell_model in row_model.cells:
                # 根据 is_header 决定使用 <th> 或 <td>
                tag = "th" if cell_model.is_header else "td"
                # 将 cell_id 作为 HTML 元素的 id，用于标注
                # 将 row_span 和 col_span 作为 HTML 属性
                html += f'<{tag} id="{cell_model.cell_id}" rowspan="{cell_model.row_span}" colspan="{cell_model.col_span}">{cell_model.content}</{tag}>'
            html += "</tr>"

        html += "</table></body></html>"
        return html
    ```

#### `_get_annotations_script() -> str`

-   **用途**: 生成用于提取标注的 JavaScript 代码字符串。这段脚本将在浏览器上下文中执行。
-   **伪代码和注释**:

    ```javascript
    // (This is Javascript code, returned as a Python string)
    function getAnnotations() {
        const annotations = {
            'cells': [],
            // 'lines': [], // 可选：为行级标注预留
        };
        const table = document.getElementById('main-table');
        if (!table) return annotations;

        // 获取表格相对于视口的偏移量，用于将单元格坐标转换为相对于表格的坐标
        const tableRect = table.getBoundingClientRect();

        // 遍历所有 th 和 td 元素
        const cells = table.querySelectorAll('th, td');
        cells.forEach(cell => {
            const rect = cell.getBoundingClientRect();
            annotations.cells.push({
                'id': cell.id, // cell-0-0, cell-0-1, etc.
                'bbox': [
                    rect.left - tableRect.left, // x_min
                    rect.top - tableRect.top,   // y_min
                    rect.right - tableRect.left, // x_max
                    rect.bottom - tableRect.top // y_max
                ],
                'content': cell.textContent
            });

            // 可选：进一步获取行级或词级标注
            // ... (此部分逻辑较为复杂，可在后续迭代中实现)
        });

        return annotations;
    }
    // 返回函数调用表达式，以便 evaluate 可以执行它
    getAnnotations();
    ```

### 5.8. `table_render/main_generator.py`

a. **文件用途说明**

作为整个表格生成流程的核心协调器。它初始化并管理所有的构建器（Structure, Content, Style）和渲染器（HtmlRenderer），并按顺序调用它们来完成从配置到最终产物（图像和标注）的完整流水线。

b. **文件内类图**

```mermaid
classDiagram
    class MainGenerator {
        -config: RenderConfig
        +generate(num_samples: int)
        -_generate_single()
    }
    MainGenerator ..> StructureBuilder : uses
    MainGenerator ..> ContentBuilder : uses
    MainGenerator ..> StyleBuilder : uses
    MainGenerator ..> HtmlRenderer : uses
    MainGenerator ..> FileUtils : uses
```

c. **函数/方法详解**

#### `__init__(self, config: RenderConfig)`
-   **用途**: 初始化主生成器，存储配置对象。
-   **输入参数**: `config` (RenderConfig): 经过验证的完整配置对象。
-   **伪代码和注释**:

    ```python
    # 导入: RenderConfig, builders, renderers, utils
    import asyncio

    class MainGenerator:
        def __init__(self, config: RenderConfig):
            self.config = config
            # 为了可复现性，创建一个主随机状态
            self.random_state = np.random.RandomState(config.seed)
    ```

#### `generate(self, num_samples: int)`

-   **用途**: 主生成循环。根据 `num_samples` 的数量，多次调用 `_generate_single` 方法来生成样本。它使用 `asyncio.run` 来启动异步流程。
-   **输入参数**: `num_samples` (int): 要生成的样本总数。
-   **伪代码和注释**:

    ```python
    def generate(self, num_samples: int):
        # 使用 asyncio.run 启动异步的生成过程
        asyncio.run(self._async_generate(num_samples))

    async def _async_generate(self, num_samples: int):
        # 1. 初始化渲染器
        #    渲染器（特别是Playwright）的启动是异步的，且昂贵，
        #    因此我们只在整个生成会话中初始化一次。
        renderer = await HtmlRenderer.create_async()

        try:
            # 2. 循环生成每个样本
            for i in range(num_samples):
                log.info(f"正在生成样本 {i + 1}/{num_samples}...")
                
                # 为每个样本生成一个独立的随机种子，以确保多样性，同时保持整体可复现
                sample_seed = self.random_state.randint(0, 2**32 - 1)
                
                # 调用单个样本的生成逻辑
                image_bytes, annotations = await self._generate_single(sample_seed, renderer)

                # 3. 保存产物
                #    文件名可以基于样本索引或一个UUID
                file_utils.save_sample(
                    output_dir=self.config.output.output_dir,
                    sample_index=i,
                    image_bytes=image_bytes,
                    annotations=annotations,
                    image_format=self.config.output.image_format
                )
        finally:
            # 4. 确保无论成功还是失败，都关闭渲染器释放资源
            await renderer.close_async()
    ```

#### `_generate_single(self, seed: int, renderer: HtmlRenderer) -> Tuple[bytes, dict]`

-   **用途**: 执行一次完整的、从头到尾的表格生成流程。
-   **输入参数**:
    -   `seed` (int): 用于本次样本生成的所有随机操作的种子。
    -   `renderer` (HtmlRenderer): 已初始化的渲染器实例。
-   **输出**: `Tuple[bytes, dict]`: 包含图像数据和标注的元组。
-   **伪代码和注释**:

    ```python
    async def _generate_single(self, seed, renderer):
        # 1. 初始化所有构建器，并传入当前样本的种子
        structure_builder = StructureBuilder(seed)
        content_builder = ContentBuilder(seed)
        style_builder = StyleBuilder(seed)

        # 2. 按顺序执行构建流程
        #    a. 构建结构
        table_model_structure = structure_builder.build(self.config.structure)

        #    b. 填充内容
        table_model_filled = content_builder.build(table_model_structure, self.config.content)

        #    c. 生成样式
        css_string = style_builder.build(self.config.style)

        # 3. 使用渲染器生成图像和标注
        image_bytes, annotations = await renderer.render(
            model=table_model_filled,
            css=css_string,
            config=self.config.renderer
        )

        # 4. (可选) 应用图像后处理
        if self.config.postprocessing:
            postprocessor = ImagePostprocessor(seed)
            image_bytes = postprocessor.process(image_bytes, self.config.postprocessing)

        return image_bytes, annotations
    ```

### 5.9. `table_render/postprocessors/image_postprocessor.py`

a. **文件用途说明**

提供对生成图像的（可选）后处理功能。这包括添加各种形式的图像退化效果，如高斯模糊、噪声或透视变换，以增强生成数据集的真实性和多样性。所有操作均使用 `Pillow` 和 `OpenCV` 库完成。

b. **文件内类图**

```mermaid
classDiagram
    class ImagePostprocessor {
        -random_state: np.random.RandomState
        +process(image_bytes: bytes, config: PostprocessingConfig) bytes
        -_apply_blur(image: Image, probability: float) Image
        -_apply_noise(image: Image, probability: float) Image
        -_apply_perspective_transform(image: Image, probability: float) Image
    }
```

c. **函数/方法详解**

#### `__init__(self, seed: int)`
-   **用途**: 初始化后处理器，并创建 `numpy.random.RandomState` 实例以确保随机效果的可复现性。

#### `process(self, image_bytes: bytes, config: PostprocessingConfig) -> bytes`

-   **用途**: 主处理方法。根据配置中的概率，依次应用各种后处理效果。
-   **输入参数**:
    -   `image_bytes` (bytes): 原始的、由渲染器生成的图像数据。
    -   `config` (PostprocessingConfig): 后处理效果的配置。
-   **输出**: `bytes`: 经过处理后的图像数据。
-   **伪代码和注释**:

    ```python
    # 导入: Pillow (Image, ImageFilter), numpy, cv2, io

    def process(self, image_bytes: bytes, config: PostprocessingConfig) -> bytes:
        # 1. 将输入的字节数据转换为 Pillow Image 对象
        image = Image.open(io.BytesIO(image_bytes))

        # 2. 按顺序应用各种效果
        #    每个效果的应用都由其概率决定
        image = self._apply_blur(image, config.blur_probability)
        image = self._apply_noise(image, config.noise_probability)
        image = self._apply_perspective_transform(image, config.perspective_probability)

        # 3. 将处理后的 Pillow Image 对象转换回字节数据
        output_buffer = io.BytesIO()
        image.save(output_buffer, format='PNG') # 格式可以根据需要调整
        return output_buffer.getvalue()
    ```

#### `_apply_blur(self, image: Image, probability: float) -> Image`

-   **用途**: 根据概率对图像应用高斯模糊。
-   **伪代码和注释**:

    ```python
    def _apply_blur(self, image, probability):
        if self.random_state.rand() < probability:
            # 随机选择一个模糊半径
            radius = self.random_state.uniform(1.0, 3.0)
            return image.filter(ImageFilter.GaussianBlur(radius=radius))
        return image
    ```

#### `_apply_noise(self, image: Image, probability: float) -> Image`

-   **用途**: 根据概率向图像添加高斯噪声。
-   **伪代码和注释**:

    ```python
    def _apply_noise(self, image, probability):
        if self.random_state.rand() < probability:
            # 将 Pillow Image 转换为 numpy array 以便进行像素操作
            img_array = np.array(image)
            # 随机确定噪声的强度
            noise_intensity = self.random_state.randint(5, 20)
            # 生成与图像维度相同的高斯噪声
            noise = self.random_state.normal(0, noise_intensity, img_array.shape)
            # 将噪声添加到图像上，并确保像素值在 0-255 范围内
            noisy_img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)
            # 将 numpy array 转换回 Pillow Image
            return Image.fromarray(noisy_img_array)
        return image
    ```

#### `_apply_perspective_transform(self, image: Image, probability: float) -> Image`

-   **用途**: 根据概率对图像应用轻微的透视变换。
-   **伪代码和注释**:

    ```python
    def _apply_perspective_transform(self, image, probability):
        if self.random_state.rand() < probability:
            # 将 Pillow Image 转换为 OpenCV 格式 (numpy array)
            img_cv = np.array(image.convert('RGB'))
            h, w = img_cv.shape[:2]

            # 定义原始图像的四个角点
            src_points = np.float32([[0, 0], [w - 1, 0], [0, h - 1], [w - 1, h - 1]])

            # 在原始角点的基础上添加少量随机扰动，以创建目标角点
            max_offset = min(h, w) * 0.05 # 最大偏移量为图像短边长度的5%
            dx1, dy1 = self.random_state.uniform(-max_offset, max_offset, 2)
            # ... (为其他三个角点生成随机偏移)
            dst_points = np.float32([
                [dx1, dy1], 
                [w - 1 + ..., ...], 
                [... , h - 1 + ...],
                [w - 1 + ..., h - 1 + ...]
            ])

            # 计算透视变换矩阵并应用它
            matrix = cv2.getPerspectiveTransform(src_points, dst_points)
            warped_img = cv2.warpPerspective(img_cv, matrix, (w, h))

            # 将 OpenCV 图像转换回 Pillow Image
            return Image.fromarray(warped_img)
        return image
    ```

### 5.10. `table_render/utils/file_utils.py`

a. **文件用途说明**

提供文件系统操作的辅助函数。主要负责根据输出配置，将生成的图像和标注数据保存到指定的目录结构中。这是一个无状态的模块，只包含静态方法。

b. **文件内类图**

(该文件不包含类，仅包含静态函数)

c. **函数/方法详解**

#### `save_sample(output_dir: str, sample_index: int, image_bytes: bytes, annotations: dict, image_format: str, samples_per_dir: int)`

-   **用途**: 保存单个生成样本的图像和标注文件。
-   **输入参数**:
    -   `output_dir` (str): 主输出目录。
    -   `sample_index` (int): 当前样本的全局索引。
    -   `image_bytes` (bytes): 图像的二进制数据。
    -   `annotations` (dict): 标注的字典数据。
    -   `image_format` (str): 图像的保存格式 ('png' 或 'jpeg')。
    -   `samples_per_dir` (int): 每个子目录存放的样本数量。
-   **伪代码和注释**:

    ```python
    # 导入: os, json

    def save_sample(output_dir, sample_index, image_bytes, annotations, image_format, samples_per_dir):
        # 1. 计算样本应该存放在哪个子目录中
        #    例如，样本0-499在 '0000' 目录, 500-999在 '0001' 目录
        subdir_index = sample_index // samples_per_dir
        subdir_name = f"{subdir_index:04d}"
        target_dir = os.path.join(output_dir, subdir_name)

        # 2. 确保目标目录存在
        #    os.makedirs(..., exist_ok=True) 可以安全地创建目录，如果已存在则不会报错
        os.makedirs(target_dir, exist_ok=True)

        # 3. 定义文件名
        #    文件名基于样本的全局索引
        base_filename = f"{sample_index:06d}"
        image_filename = f"{base_filename}.{image_format}"
        json_filename = f"{base_filename}.json"

        # 4. 构造完整的文件路径
        image_path = os.path.join(target_dir, image_filename)
        json_path = os.path.join(target_dir, json_filename)

        # 5. 写入图像文件
        try:
            with open(image_path, 'wb') as f:
                f.write(image_bytes)
        except IOError as e:
            log.error(f"无法写入图像文件 {image_path}: {e}")

        # 6. 写入JSON标注文件
        try:
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(annotations, f, ensure_ascii=False, indent=4)
        except IOError as e:
            log.error(f"无法写入标注文件 {json_path}: {e}")

    ```
