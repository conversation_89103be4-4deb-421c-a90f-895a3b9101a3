"""
主生成器

协调整个表格生成流程的核心类。
"""

import asyncio
import logging
import numpy as np
import os
from pathlib import Path
from typing import Tuple

from .config import RenderConfig
from .resolver import Resolver
from .builders.structure_builder import StructureBuilder
from .builders.content_builder import ContentBuilder
from .builders.style_builder import StyleBuilder
from .renderers.html_renderer import HtmlRenderer
from .utils.file_utils import FileUtils
from .utils.annotation_converter import AnnotationConverter
from .postprocessors.image_augmentor import ImageAugmentor  # V4.0新增


class MainGenerator:
    """
    主生成器类
    
    负责协调整个表格生成流水线，包括结构构建、内容填充、样式生成和渲染。
    """
    
    def __init__(self, config: RenderConfig, debug_mode: bool = False):
        """
        初始化主生成器

        Args:
            config: 经过验证的配置对象
            debug_mode: 是否启用调试模式
        """
        self.config = config
        self.debug_mode = debug_mode
        self.logger = logging.getLogger(__name__)
        # 为了可复现性，创建一个主随机状态
        self.random_state = np.random.RandomState(config.seed)

        if self.debug_mode:
            self.logger.info("调试模式已启用")
        # V3.1新增：创建配置解析器
        self.resolver = Resolver()
        
    def generate(self, num_samples: int):
        """
        生成指定数量的表格样本

        Args:
            num_samples: 要生成的样本数量
        """
        # 使用asyncio运行异步生成过程
        asyncio.run(self._async_generate(num_samples))

    async def _async_generate(self, num_samples: int):
        """
        异步生成过程

        Args:
            num_samples: 要生成的样本数量
        """
        self.logger.info(f"主生成器已初始化，配置种子: {self.config.seed}")

        # 确保输出目录存在
        output_dirs = FileUtils.ensure_output_dirs(self.config.output.output_dir)

        # 初始化渲染器和转换器（不在这里设置调试目录，每个样本单独设置）
        renderer = await HtmlRenderer.create_async()
        annotation_converter = AnnotationConverter()

        try:
            for i in range(num_samples):
                self.logger.info(f"正在生成第 {i + 1}/{num_samples} 个样本...")

                # 为每个样本生成一个独立的随机种子
                sample_seed = self.random_state.randint(0, 2**32 - 1)

                # V3.1新工作流：首先解析配置为具体参数
                resolved_params = self.resolver.resolve(self.config, sample_seed)

                # 构建表格结构
                structure_builder = StructureBuilder(sample_seed)
                table_model = structure_builder.build(
                    resolved_params.structure,
                    resolved_params.style.border_mode,
                    resolved_params.style.border_details
                )

                # 填充表格内容
                content_builder = ContentBuilder(sample_seed)
                table_model = content_builder.build(table_model, resolved_params.content)

                # 生成样式
                style_builder = StyleBuilder(sample_seed)
                # V4.3新增：传递透明度配置
                transparency_config = resolved_params.postprocessing if resolved_params.postprocessing else None
                css_string = style_builder.build(resolved_params.style, table_model, transparency_config)

                # 设置当前样本的调试目录
                if self.debug_mode:
                    sample_debug_dir = os.path.join(self.config.output.output_dir, f"debug_sample_{i:06d}")
                    renderer.debug_mode = True
                    renderer.debug_output_dir = sample_debug_dir
                    renderer.debug_stage_counter = 0  # 重置计数器
                    # 确保调试目录存在
                    Path(sample_debug_dir).mkdir(parents=True, exist_ok=True)
                else:
                    renderer.debug_mode = False
                    renderer.debug_output_dir = None

                # V4.0新增：检查是否使用CSS背景图渲染模式
                if (resolved_params.postprocessing and
                    resolved_params.postprocessing.apply_background):

                    # CSS渲染模式：直接在HTML阶段集成背景图和透视变换
                    image_bytes, raw_annotations = await renderer.render(
                        table_model, css_string, resolved_params.postprocessing
                    )



                    # 转换标注格式
                    image_filename = f"{i:06d}.png"
                    final_annotations = annotation_converter.convert_to_final_format(
                        raw_annotations, table_model, image_filename
                    )

                    # 设置调试输出目录
                    debug_output_dir = None
                    if self.debug_mode:
                        debug_output_dir = os.path.join(self.config.output.output_dir, f"debug_sample_{i:06d}")

                    image_augmentor = ImageAugmentor(
                        sample_seed,
                        debug_mode=self.debug_mode,
                        debug_output_dir=debug_output_dir
                    )

                    # V4.5修改：CSS模式下先应用透视变换（如果启用）
                    if (resolved_params.postprocessing.apply_perspective and
                        resolved_params.postprocessing.perspective_offset_ratio):

                        self.logger.info("CSS模式：开始应用OpenCV透视变换")
                        # 创建仅包含透视变换的参数
                        perspective_params = self._create_perspective_only_params(resolved_params.postprocessing)
                        image_bytes, final_annotations = image_augmentor.process(
                            image_bytes, final_annotations, perspective_params
                        )
                        self.logger.info("OpenCV透视变换完成")

                    # 然后应用其他非背景效果（模糊、噪声）
                    if (resolved_params.postprocessing.apply_blur or
                        resolved_params.postprocessing.apply_noise):

                        # 创建仅包含模糊和噪声的参数
                        blur_noise_params = self._create_blur_noise_params(resolved_params.postprocessing)
                        image_bytes, final_annotations = image_augmentor.process(
                            image_bytes, final_annotations, blur_noise_params
                        )
                        self.logger.debug("模糊和噪声效果应用完成")

                    # V4.5新增：CSS模式下的降质效果处理
                    if (resolved_params.postprocessing.apply_degradation_blur or
                        resolved_params.postprocessing.apply_degradation_noise or
                        resolved_params.postprocessing.apply_degradation_fade_global or
                        resolved_params.postprocessing.apply_degradation_fade_local or
                        resolved_params.postprocessing.apply_degradation_uneven_lighting or
                        resolved_params.postprocessing.apply_degradation_jpeg or
                        resolved_params.postprocessing.apply_degradation_darker_brighter or
                        resolved_params.postprocessing.apply_degradation_gamma_correction):

                        self.logger.info("CSS模式：开始应用降质效果")
                        # 创建包含降质效果的参数
                        degradation_params = self._create_degradation_params(resolved_params.postprocessing)
                        image_bytes, final_annotations = image_augmentor.process(
                            image_bytes, final_annotations, degradation_params
                        )
                        self.logger.info("CSS模式：降质效果应用完成")

                    # V4.2新增：CSS模式下的智能边距裁剪
                    if (resolved_params.postprocessing.margin_control and
                        resolved_params.postprocessing.margin_control.range_list):
                        self.logger.info("[MARGIN_CONTROL] CSS模式：开始智能边距裁剪")
                        image_bytes, final_annotations = self._apply_smart_crop(
                            image_bytes, final_annotations, resolved_params.postprocessing.margin_control, sample_seed
                        )

                else:
                    # 传统模式：先渲染表格，再进行图像后处理
                    image_bytes, raw_annotations = await renderer.render(table_model, css_string)

                    # 转换标注格式
                    image_filename = f"{i:06d}.png"
                    self.logger.debug(f"原始标注: {raw_annotations}")
                    final_annotations = annotation_converter.convert_to_final_format(
                        raw_annotations, table_model, image_filename
                    )
                    self.logger.debug(f"转换后标注: {final_annotations}")

                    # 图像后处理（同时处理图像和标注）
                    if resolved_params.postprocessing is not None:
                        self.logger.debug(f"开始图像后处理，输入标注: {final_annotations}")

                        # 设置调试输出目录
                        debug_output_dir = None
                        if self.debug_mode:
                            debug_output_dir = os.path.join(self.config.output.output_dir, f"debug_sample_{i:06d}")

                        image_augmentor = ImageAugmentor(
                            sample_seed,
                            debug_mode=self.debug_mode,
                            debug_output_dir=debug_output_dir
                        )
                        image_bytes, final_annotations = image_augmentor.process(
                            image_bytes, final_annotations, resolved_params.postprocessing
                        )
                        self.logger.debug(f"传统模式后处理完成，最终标注: {final_annotations}")

                # V3.1步骤5：准备元数据，使用resolved_params而不是原始config
                metadata = {
                    'resolved_params': resolved_params.dict(),  # 精确的、可复现的参数
                    'original_config': self.config.dict(),     # 保留原始配置用于参考
                    'sample_seed': sample_seed,
                    'sample_index': i
                }

                # 保存文件
                self.logger.debug(f"保存文件前的最终标注: {final_annotations}")
                FileUtils.save_sample(
                    sample_index=i,
                    image_bytes=image_bytes,
                    annotations=final_annotations,
                    metadata=metadata,
                    output_dirs=output_dirs,
                    label_suffix=resolved_params.output.label_suffix
                )
                self.logger.debug(f"文件保存完成: 样本 {i}")

                self.logger.info(f"样本 {i + 1} 生成完成")

        finally:
            # 确保关闭渲染器
            await renderer.close_async()

        self.logger.info("所有样本生成完毕")





    def _create_perspective_only_params(self, postprocessing_params):
        """
        创建仅包含透视变换的后处理参数

        Args:
            postprocessing_params: 原始后处理参数

        Returns:
            仅包含透视变换的后处理参数
        """
        from .config import ResolvedPostprocessingParams

        return ResolvedPostprocessingParams(
            apply_blur=False,
            blur_radius=None,
            apply_noise=False,
            noise_intensity=None,
            apply_perspective=postprocessing_params.apply_perspective,
            perspective_offset_ratio=postprocessing_params.perspective_offset_ratio,
            apply_background=False,   # 背景图已在HTML中完成
            background_image_path=None,
            max_scale_factor=None,
            css_background_width=None,
            css_background_height=None,
            css_table_left=None,
            css_table_top=None,
            css_crop_width=None,
            css_crop_height=None,
            css_bg_offset_x=None,
            css_bg_offset_y=None,
            margin_control=postprocessing_params.margin_control,
            enable_transparency=postprocessing_params.enable_transparency,
            overall_transparency=postprocessing_params.overall_transparency,
            # V4.5新增：降质效果参数
            apply_degradation_blur=False,  # 透视变换阶段不应用降质效果
            apply_degradation_noise=False,
            apply_degradation_fade_global=False,
            apply_degradation_fade_local=False,
            apply_degradation_uneven_lighting=False,
            apply_degradation_jpeg=False,
            apply_degradation_darker_brighter=False,
            apply_degradation_gamma_correction=False
        )

    def _create_blur_noise_params(self, postprocessing_params):
        """
        创建仅包含模糊和噪声的后处理参数

        Args:
            postprocessing_params: 原始后处理参数

        Returns:
            仅包含模糊和噪声效果的后处理参数
        """
        from .config import ResolvedPostprocessingParams

        return ResolvedPostprocessingParams(
            apply_blur=postprocessing_params.apply_blur,
            blur_radius=postprocessing_params.blur_radius,
            apply_noise=postprocessing_params.apply_noise,
            noise_intensity=postprocessing_params.noise_intensity,
            apply_perspective=False,  # 透视变换已单独处理
            perspective_offset_ratio=None,
            apply_background=False,   # 背景图已在HTML中完成
            background_image_path=None,
            max_scale_factor=None,
            css_background_width=None,
            css_background_height=None,
            css_table_left=None,
            css_table_top=None,
            css_crop_width=None,
            css_crop_height=None,
            css_bg_offset_x=None,
            css_bg_offset_y=None,
            margin_control=postprocessing_params.margin_control,
            enable_transparency=postprocessing_params.enable_transparency,
            overall_transparency=postprocessing_params.overall_transparency,
            # V4.5新增：降质效果参数 - 在模糊噪声阶段不应用降质效果
            apply_degradation_blur=False,
            apply_degradation_noise=False,
            apply_degradation_fade_global=False,
            apply_degradation_fade_local=False,
            apply_degradation_uneven_lighting=False,
            apply_degradation_jpeg=False,
            apply_degradation_darker_brighter=False,
            apply_degradation_gamma_correction=False
        )

    def _create_degradation_params(self, postprocessing_params):
        """
        创建仅包含降质效果的后处理参数

        Args:
            postprocessing_params: 原始后处理参数

        Returns:
            仅包含降质效果的后处理参数
        """
        from .config import ResolvedPostprocessingParams

        return ResolvedPostprocessingParams(
            apply_blur=False,  # 模糊和噪声已单独处理
            blur_radius=None,
            apply_noise=False,
            noise_intensity=None,
            apply_perspective=False,  # 透视变换已单独处理
            perspective_offset_ratio=None,
            apply_background=False,   # 背景图已在HTML中完成
            background_image_path=None,
            max_scale_factor=None,
            css_background_width=None,
            css_background_height=None,
            css_table_left=None,
            css_table_top=None,
            css_crop_width=None,
            css_crop_height=None,
            css_bg_offset_x=None,
            css_bg_offset_y=None,
            margin_control=None,  # 边距裁剪将单独处理
            enable_transparency=postprocessing_params.enable_transparency,
            overall_transparency=postprocessing_params.overall_transparency,
            # V4.5新增：降质效果参数 - 传递所有降质效果
            apply_degradation_blur=postprocessing_params.apply_degradation_blur,
            apply_degradation_noise=postprocessing_params.apply_degradation_noise,
            apply_degradation_fade_global=postprocessing_params.apply_degradation_fade_global,
            apply_degradation_fade_local=postprocessing_params.apply_degradation_fade_local,
            apply_degradation_uneven_lighting=postprocessing_params.apply_degradation_uneven_lighting,
            apply_degradation_jpeg=postprocessing_params.apply_degradation_jpeg,
            apply_degradation_darker_brighter=postprocessing_params.apply_degradation_darker_brighter,
            apply_degradation_gamma_correction=postprocessing_params.apply_degradation_gamma_correction
        )

    def _apply_smart_crop(self, image_bytes: bytes, annotations: dict, margin_config, sample_seed: int) -> Tuple[bytes, dict]:
        """
        应用智能边距裁剪

        Args:
            image_bytes: 图像字节数据
            annotations: 标注数据
            margin_config: 边距控制配置
            sample_seed: 样本种子

        Returns:
            (裁剪后的图像字节, 更新后的标注)
        """
        try:
            from PIL import Image
            import io
            import copy
            import numpy as np

            # 将字节数据转换为PIL图像
            image = Image.open(io.BytesIO(image_bytes))
            self.logger.info(f"[SMART_CROP] 原始图像尺寸: {image.size}")

            # 1. 计算表格的真实边界
            table_bounds = self._calculate_table_bounds(annotations)
            self.logger.info(f"[SMART_CROP] 表格真实边界: {table_bounds}")

            # 2. 根据配置选择边距
            margin = self._select_margin_from_config(margin_config, sample_seed)
            self.logger.info(f"[SMART_CROP] 选择边距: {margin}")

            # 3. 计算裁剪区域
            crop_box = self._calculate_crop_box(table_bounds, margin, image.size)
            self.logger.info(f"[SMART_CROP] 裁剪区域: {crop_box}")

            # 4. 裁剪图像
            cropped_image = image.crop(crop_box)
            self.logger.info(f"[SMART_CROP] 裁剪后尺寸: {cropped_image.size}")

            # 5. 更新标注坐标
            updated_annotations = self._update_annotations_for_crop(annotations, crop_box)

            # 6. 转换回字节数据
            output_buffer = io.BytesIO()
            cropped_image.save(output_buffer, format='PNG')
            cropped_bytes = output_buffer.getvalue()

            return cropped_bytes, updated_annotations

        except Exception as e:
            self.logger.error(f"智能裁剪失败: {e}，返回原始图像")
            return image_bytes, annotations

    def _calculate_table_bounds(self, annotations: dict) -> dict:
        """计算表格的真实边界"""
        all_x = []
        all_y = []

        for cell in annotations.get('cells', []):
            bbox = cell.get('bbox', {})
            for point_key in ['p1', 'p2', 'p3', 'p4']:
                if point_key in bbox:
                    x, y = bbox[point_key]
                    all_x.append(x)
                    all_y.append(y)

        if not all_x or not all_y:
            raise ValueError("无法从标注数据中提取有效的坐标点")

        return {
            "min_x": min(all_x),
            "min_y": min(all_y),
            "max_x": max(all_x),
            "max_y": max(all_y)
        }

    def _select_margin_from_config(self, margin_config, sample_seed: int) -> int:
        """根据边距配置选择边距值"""
        import numpy as np
        random_state = np.random.RandomState(sample_seed)

        if not margin_config or not hasattr(margin_config, 'range_list') or not margin_config.range_list:
            return 50  # 默认边距

        range_list = margin_config.range_list
        probability_list = getattr(margin_config, 'probability_list', None)

        if not probability_list or len(probability_list) != len(range_list):
            probability_list = [1.0 / len(range_list)] * len(range_list)

        # 归一化概率
        total_prob = sum(probability_list)
        if total_prob > 0:
            normalized_probabilities = [p / total_prob for p in probability_list]
        else:
            normalized_probabilities = [1.0 / len(range_list)] * len(range_list)

        # 选择边距范围
        selected_index = random_state.choice(len(range_list), p=normalized_probabilities)
        selected_range = range_list[selected_index]

        # 在范围内随机选择
        min_margin, max_margin = selected_range
        actual_margin = random_state.randint(min_margin, max_margin + 1)

        self.logger.info(f"[MARGIN_SELECT] 选择范围: {selected_range}, 实际边距: {actual_margin}")
        return actual_margin

    def _calculate_crop_box(self, table_bounds: dict, margin: int, image_size: tuple) -> tuple:
        """计算裁剪区域"""
        image_width, image_height = image_size

        # 基于表格边界和边距计算裁剪区域
        crop_left = max(0, int(table_bounds["min_x"] - margin))
        crop_top = max(0, int(table_bounds["min_y"] - margin))
        crop_right = min(image_width, int(table_bounds["max_x"] + margin))
        crop_bottom = min(image_height, int(table_bounds["max_y"] + margin))

        return (crop_left, crop_top, crop_right, crop_bottom)

    def _update_annotations_for_crop(self, annotations: dict, crop_box: tuple) -> dict:
        """更新标注坐标以适应裁剪"""
        import copy

        crop_left, crop_top, crop_right, crop_bottom = crop_box
        updated_annotations = copy.deepcopy(annotations)

        # 更新所有单元格的坐标
        if 'cells' in updated_annotations:
            for cell in updated_annotations['cells']:
                if 'bbox' in cell:
                    bbox = cell['bbox']
                    for point_key in ['p1', 'p2', 'p3', 'p4']:
                        if point_key in bbox:
                            x, y = bbox[point_key]
                            bbox[point_key] = [x - crop_left, y - crop_top]

        # 更新表格边界框
        if 'table_bbox' in updated_annotations:
            table_bbox = updated_annotations['table_bbox']
            if len(table_bbox) >= 4:
                updated_annotations['table_bbox'] = [
                    table_bbox[0] - crop_left,
                    table_bbox[1] - crop_top,
                    table_bbox[2] - crop_left,
                    table_bbox[3] - crop_top
                ]

        return updated_annotations
