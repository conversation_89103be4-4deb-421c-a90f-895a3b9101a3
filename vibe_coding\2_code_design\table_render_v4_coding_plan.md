# TableRender v4.0 开发计划

**目标:** 根据产品需求，V4 版本旨在提升工具的鲁棒性并引入图像增强功能。具体包括：实现内容溢出处理、样式冲突解决、可选的图像后处理（模糊、噪声、透视变换）以及对现有流程的性能优化。

---

## 1. 目录结构规划

为支持新功能，项目目录结构将进行扩展。主要变化是新增 `postprocessors` 模块，用于存放所有图像后处理相关的代码。

```
TableRender/
├── table_render/           # 核心Python包
│   ├── ... (现有模块)
│   ├── builders/           # 构建器
│   │   └── style_builder.py  # [修改] 添加内容溢出处理逻辑
│   ├── postprocessors/     # [新增] 图像后处理器模块
│   │   ├── __init__.py
│   │   ├── base_augmentor.py # [新增] 后处理器基类
│   │   └── image_augmentor.py  # [新增] 具体的图像增强实现
│   ├── config.py           # [修改] 添加V4版本新功能配置项
│   └── main_generator.py   # [修改] 集成后处理步骤
│
├── ... (其他目录保持不变)
```

## 2. 受影响的现有模块说明

- **`table_render/config.py`**: 需要大幅扩展，为内容溢出策略、样式冲突解决和所有图像增强功能（模糊、噪声、透视）添加新的 Pydantic 配置模型。这是驱动所有新功能的基础。
- **`table_render/resolver.py`**: 需要更新以识别并解析 `config.py` 中新增的 V4 配置项，并将它们传递给下游模块。
- **`table_render/builders/style_builder.py`**: 将根据解析后的新配置，生成用于控制内容溢出（截断或换行）的 CSS 规则。
- **`table_render/main_generator.py`**: 将在渲染步骤之后、保存文件之前，根据配置选择性地调用新的图像后处理模块。
- **`table_render/renderers/html_renderer.py`**: 将对其内部用于提取标注的 JavaScript 脚本进行审查和优化，以减少性能瓶颈。

---

## 3. 渐进式开发与集成步骤

我们将 V4 的开发分解为以下5个独立、可验证的小步迭代。

### 第 1 步：扩展配置模型

**目标:** 在 `config.py` 中为 V4 的所有新功能添加配置项，为后续开发奠定基础。

**操作:**
1.  **修改 `StyleConfig`**: 
    -   添加 `overflow_strategy: Literal['truncate', 'wrap'] = 'wrap'` 用于控制内容溢出。
2.  **创建 `PostprocessingConfig`**: 
    -   新建 `BlurConfig`, `NoiseConfig`, `PerspectiveConfig` 类，分别定义各自的概率（`probability`）和强度范围（如 `factor_range`）。
    -   创建 `PostprocessingConfig` 类，包含上述三个配置类的可选实例。
3.  **更新 `RenderConfig`**: 
    -   在主配置 `RenderConfig` 中添加 `postprocessing: Optional[PostprocessingConfig] = None`。

**验证:** 
- 运行现有测试或使用旧配置文件生成图像，确保程序行为不变。
- 创建一个包含新配置项（使用默认值）的 `v4_test.yaml`，程序应能成功加载并验证通过，无报错。

### 第 2 步：实现内容溢出处理

**目标:** 根据配置，实现单元格内文本溢出时的“截断”或“自动换行”功能。

**操作:**
1.  **更新 `resolver.py`**: 修改 `_resolve_style_params` 方法，使其能够解析 `overflow_strategy` 并将其添加到 `ResolvedStyleParams` 中。
2.  **修改 `style_builder.py`**: 在 `build` 方法中，读取解析后的 `overflow_strategy` 值。
    -   如果值为 `'truncate'`，则向单元格的全局 CSS 中添加 `white-space: nowrap; overflow: hidden; text-overflow: ellipsis;`。
    -   如果值为 `'wrap'`，则添加 `word-wrap: break-word; white-space: normal;`。

**验证:**
-   在 `v4_test.yaml` 中设置 `overflow_strategy: 'truncate'`。
-   配置一个内容很长、列宽很窄的表格进行生成。
-   观察输出图像，确认长文本被正确截断并显示省略号。切换为 `'wrap'` 后，文本应能正确换行。

### 第 3 步：实现图像后处理模块框架

**目标:** 搭建图像后处理模块的基本框架，并实现第一个增强效果：模糊。

**操作:**
1.  **创建 `postprocessors/base_augmentor.py`**: 定义一个抽象基类 `BaseAugmentor`，包含一个 `augment(image: Image) -> Image` 的抽象方法。
2.  **创建 `postprocessors/image_augmentor.py`**: 
    -   创建 `ImageAugmentor` 类。
    -   实现 `_apply_blur` 方法，使用 `Pillow` 库的 `ImageFilter.GaussianBlur` 对图像进行模糊处理。
    -   添加一个主 `process(image, resolved_postproc_params)` 方法，该方法根据传入的已解析参数，决定是否调用 `_apply_blur`。
3.  **更新 `resolver.py`**: 解析 `PostprocessingConfig`，将其转换为确定性的参数（例如，根据概率决定本次是否应用模糊，并从范围中选择一个具体的模糊半径）。
4.  **修改 `main_generator.py`**: 
    -   在 `_async_generate` 循环中，实例化 `ImageAugmentor`。
    -   在 `HtmlRenderer.render` 之后，`FileUtils.save_sample` 之前，添加一个调用步骤：如果解析后的后处理参数存在，则调用 `image_augmentor.process()`，并将返回的新图像用于保存。

**验证:**
-   在 `v4_test.yaml` 中配置模糊效果，设置 `probability: 1.0`。
-   生成图像，确认输出的图像是模糊的。
-   将概率设为 `0.0`，确认输出的图像是清晰的。

### 第 4 步：完成所有图像增强效果

**目标:** 在已有的后处理框架上，添加噪声和透视变换两种增强效果。

**操作:**
1.  **修改 `postprocessors/image_augmentor.py`**: 
    -   实现 `_apply_noise` 方法：创建一个与图像大小相同的随机噪声层，并将其叠加到原图上。
    -   实现 `_apply_perspective_transform` 方法：使用 `Pillow` 的透视变换功能，对图像进行轻微的扭曲。
    -   更新 `process` 方法，使其能根据参数链式调用多种增强效果。

**验证:**
-   在 `v4_test.yaml` 中分别或组合启用噪声和透视变换效果，并设置 `probability: 1.0`。
-   生成图像，目视检查图像是否包含了预期的噪声和形变效果。

### 第 5 步：性能审查与样式冲突日志

**目标:** 优化已知的性能瓶颈，并为样式冲突添加警告日志，完成 V4 的全部功能。

**操作:**
1.  **优化 `html_renderer.py`**: 
    -   审查 `render` 方法中执行的 JavaScript 脚本。
    -   尝试将多次 DOM 查询合并为一次，将所有需要的数据（`bbox`, `textContent` 等）在一个 `evaluate` 调用中批量返回，减少浏览器与Python之间的通信开销。
2.  **实现样式冲突日志**: 
    -   在 `resolver.py` 的 `_resolve_style_params` 中，当检测到样式被覆盖时（例如，单元格样式覆盖了行样式），使用 `logging.warning()` 记录一条信息，指明哪个单元格的哪个属性发生了覆盖。

**验证:**
-   **性能**: 对比修改前后的 `main_generator.py` 运行时间，确认在生成大量样本（如100个）时，总耗时有明显下降。
-   **日志**: 创建一个同时定义了行背景色和单元格背景色的配置，运行生成。检查控制台输出，确认看到了关于样式冲突的警告日志。

---

## 4. 实现流程图

以下 Mermaid 图展示了 V4 版本完整的、包含后处理步骤的生成流程。

```mermaid
flowchart TD
    A["开始 (MainGenerator.generate)"] --> B["解析配置 (Resolver.resolve)"];
    subgraph B
        direction LR
        B1["解析结构/内容/样式"] --> B2["[V4 新增] 解析后处理参数"];
    end
    B --> C["构建模型 (Builders)"];
    C --> D["渲染图像 (HtmlRenderer.render)"];
    D --> E{"是否需要后处理? (config.postprocessing)"};
    E -- "是" --> F["[V4 新增] 调用 ImageAugmentor.process"];
    F --> G["获取增强后的图像"];
    E -- "否" --> H["获取原始图像"];
    G --> I["保存图像和标注 (FileUtils.save_sample)"];
    H --> I;
    I --> J["结束"];
```

## 总结

V4 版本的开发将通过5个渐进式步骤完成，每个步骤都有明确的目标和可验证的输出。这种方法确保了开发过程的可控性和质量，同时为未来的功能扩展奠定了坚实的基础。

通过引入图像后处理功能和改进的样式处理，V4 版本将显著提升 TableRender 工具的实用性和生成图像的质量。
