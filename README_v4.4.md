# TableRender V4.4 版本功能总结

## 版本概述

TableRender V4.4版本是在V4.0图像后处理基础上的重要升级，主要集成了8种降质效果，显著增强了数据增强能力。本版本保持了良好的向后兼容性，通过概率化配置提供灵活的降质效果控制。

## V4.4 新增功能

### 🎯 核心特性

#### 1. 8种降质效果集成
- **模糊效果** (`degradation_blur`): 高斯/运动/平均模糊按权重随机选择（25%/50%/25%）
- **噪声效果** (`degradation_noise`): 高斯噪声添加
- **全局褪色** (`degradation_fade_global`): 整体图像褪色处理
- **局部褪色** (`degradation_fade_local`): 分块局部褪色效果
- **不均匀光照** (`degradation_uneven_lighting`): 模拟扫描仪光照不均
- **JPEG压缩** (`degradation_jpeg`): 压缩伪影模拟
- **亮度对比度调整** (`degradation_darker_brighter`): 曝光调整模拟
- **伽马校正** (`degradation_gamma_correction`): 显示器差异模拟

#### 2. 概率化控制机制
- **独立概率配置**: 每种效果独立的触发概率（0.0-1.0）
- **模糊组互斥**: 高斯/运动/平均模糊三选一，避免重复应用
- **可复现性**: 基于随机种子的确定性效果应用

#### 3. 标注坐标保持
- **几何结构不变**: 降质效果只改变像素值，不改变图像几何结构
- **标注精度保持**: 单元格边界框坐标保持完全精确
- **与变换效果区分**: 透视变换和背景合成会修改坐标，降质效果不会

#### 4. 调试模式增强
- **阶段性保存**: 降质前后状态的完整保存
- **可视化支持**: 带标注框的可视化图像生成
- **详细日志**: 每个降质效果的应用状态记录

### 🔧 技术实现

#### 1. 模块化设计
```
table_render/
├── postprocessors/
│   ├── degradation_processor.py   # 新增：降质处理器封装
│   └── image_augmentor.py         # 修改：集成降质功能
├── config.py                      # 修改：扩展配置模型
└── resolver.py                    # 修改：降质效果参数解析
```

#### 2. 依赖集成
- **doc_degradation模块**: 复用第三方降质算法库
- **最小侵入集成**: 保持现有架构完全兼容
- **错误隔离**: 单个效果失败不影响其他效果

#### 3. 执行顺序
```
CSS渲染 → 模糊/噪声 → 透视变换 → 背景合成 → 降质效果 → 文件保存
```

## 配置参数详解

### 基本配置格式

```yaml
postprocessing:
  # V4.4新增：降质效果配置
  degradation_blur:
    probability: 0.5              # 模糊效果触发概率
  degradation_noise:
    probability: 0.5              # 噪声效果触发概率
  degradation_fade_global:
    probability: 0.2              # 全局褪色触发概率
  degradation_fade_local:
    probability: 1.0              # 局部褪色触发概率
  degradation_uneven_lighting:
    probability: 0.2              # 不均匀光照触发概率
  degradation_jpeg:
    probability: 0.2              # JPEG压缩触发概率
  degradation_darker_brighter:
    probability: 0.2              # 亮度对比度调整触发概率
  degradation_gamma_correction:
    probability: 0.2              # 伽马校正触发概率
```

### 参数控制机制

#### 1. 概率控制
- **范围**: 0.0-1.0，0.0表示从不触发，1.0表示总是触发
- **独立性**: 每种效果独立判断，可以同时应用多种效果
- **随机性**: 基于配置的随机种子确保可复现

#### 2. 参数来源
- **配置文件参数**: 大部分效果使用 `doc_degradation/configs/config.py` 中的参数范围
- **硬编码逻辑**: 模糊效果的kernel_size使用特殊的硬编码逻辑
- **混合控制**: JPEG压缩对彩图（质量50）和灰度图（质量5-10）使用不同策略

#### 3. 具体参数范围

| 效果类型 | 主要参数 | 参数范围 | 说明 |
|---------|---------|----------|------|
| 模糊效果 | sigma | 0.5-0.7 | 高斯模糊标准差 |
| 噪声效果 | std | 0.5-1.0 | 高斯噪声标准差 |
| 全局褪色 | alpha | 1-2 | 褪色强度 |
| 局部褪色 | brightness | 3-8 | 亮度因子 |
| 不均匀光照 | variation | 0.2-0.7 | 光照变化强度 |
| JPEG压缩 | quality | 5-10(灰度)/50(彩色) | 压缩质量 |
| 亮度对比度 | contrast | 1.5-2.0 | 对比度因子 |
| 伽马校正 | gamma | 2.0-2.5 | 伽马值 |

## doc_degradation 功能对比

### ✅ 已实现功能（V4.4集成的8种）

1. **BLUR** - 模糊效果
   - 高斯模糊 (gaussian_blur)
   - 运动模糊 (motion_blur) 
   - 平均模糊 (average_blur)

2. **NOISE** - 噪声效果
   - 高斯噪声 (gaussian_noise)

3. **FADE_GLOBAL** - 全局褪色
4. **FADE_LOCAL** - 局部褪色
5. **UNEVEN_LIGHTING** - 不均匀光照
6. **JPEG** - JPEG压缩
7. **DARKER_BRIGHTER** - 亮度对比度调整
8. **GAMMA_CORRECTION** - 伽马校正

### ❌ 未实现功能（doc_degradation中存在但V4.4未集成）

1. **LINE_BROKEN** - 文字断线效果
2. **ARTISTIC_INK** - 艺术点墨喷洒效果
3. **GEOMETRIC** - 几何变形（已有透视变换替代）
4. **SINC** - Sinc滤波效果
5. **USM_SHARPEN** - USM锐化效果
6. **RANDOM_RESIZE** - 随机缩放效果
7. **ESRGAN_BLUR** - ESRGAN模糊效果
8. **IRIS_BLUR_LARGE** - 虹膜大模糊效果
9. **BLUR_MIX** - 模糊混合效果
10. **NOISE_MIX** - 噪声混合效果

### 🎯 选择策略

V4.4版本选择了8种最实用的降质效果，主要考虑：
- **实用性**: 模拟真实文档扫描和拍照中的常见问题
- **稳定性**: 选择经过验证的稳定算法
- **性能**: 避免过于复杂的计算密集型效果
- **兼容性**: 确保与现有后处理流程的良好集成

## 使用示例

### 基本使用

```bash
# 使用包含降质效果的配置
python -m table_render configs/v4_css_background_test.yaml --num-samples 5

# 启用调试模式查看降质效果
python -m table_render configs/v4_css_background_test.yaml --num-samples 3 --debug
```

### 配置文件示例

参考配置文件：
- `configs/v4_css_background_test.yaml` - CSS模式降质效果测试
- `configs/v4_postprocess_background_test.yaml` - 传统模式降质效果测试

### 调试输出

启用调试模式后，每个样本会生成以下文件：
```
output/debug_sample_000001/
├── 01_original_input.png              # 原始输入
├── 01_original_input.json             # 原始标注
├── 02_before_degradation.png          # 降质前状态
├── 02_before_degradation.json         # 降质前标注
├── 03_after_degradation.png           # 降质后状态
├── 03_after_degradation.json          # 降质后标注
├── 03_after_degradation_with_boxes.png # 带标注框可视化
└── 04_postprocess_output.png          # 最终输出
```

## 错误处理机制

1. **降质管道初始化失败**: 跳过所有降质处理，继续其他流程
2. **单个效果失败**: 记录错误日志，继续处理其他效果
3. **配置验证失败**: 程序启动时即报错退出
4. **文件保存失败**: 抛出异常，中断当前样本处理

## 性能考虑

1. **按需初始化**: 只有启用降质效果时才初始化降质处理器
2. **错误恢复**: 单个效果失败不影响整体流程
3. **内存管理**: 及时转换图像格式，避免内存泄漏
4. **调试开销**: 调试模式下会产生额外的文件I/O开销

## 总结

TableRender V4.4版本通过集成8种实用的降质效果，显著增强了数据增强能力。整个设计保持了良好的模块化架构，降质功能作为独立模块无缝集成到现有的图像后处理流程中。通过概率化配置和详细的调试支持，用户可以灵活控制降质效果的应用，生成更加真实和多样化的表格图像数据集。
