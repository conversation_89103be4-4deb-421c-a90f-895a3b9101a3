{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0013f863", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from typing import List\n", "import json\n", "from jinja2 import Environment, FileSystemLoader\n", "from weasyprint import HTML\n", "import pdfkit\n", "from pdf2image import convert_from_bytes\n", "import pyvips\n", "import cv2 as cv\n", "import re\n", "from subprocess import run, PIPE\n", "import glob\n", "import itertools\n", "import time\n", "import cProfile\n", "from clibrary import library\n", "import multiprocessing\n", "np.random.seed(1)"]}, {"cell_type": "markdown", "id": "e7f87bbc", "metadata": {}, "source": ["### Template Generator\n", "Generate table with certain properties (borderless,boredered and so on). Results in a string representing the tabl3 in html. The required input is a pandas dataframe.\n", "- borderless (no borders)\n", "- bordered header (only header bordered)\n", "- bordered header and bottom (only header and end bordered)\n", "- bordered internal columns (only internal column bordered)\n", "- bordered columns (columns bordered)\n", "- bordered (grid table )"]}, {"cell_type": "code", "execution_count": 2, "id": "4776a8db", "metadata": {}, "outputs": [], "source": ["class TemplateGenerator:\n", "    ''' remove top rule, mid rule , bottom rule'''\n", "    def clean_template(self,template:str)->str:\n", "        line = template.replace('style=\"text-align: right;\"',\"\")\n", "        return line\n", "    \n", "    ''' format the column names '''\n", "    def format_columns(self,df)->List[str]:\n", "        cols = list(df.columns)\n", "        formated_cols = [''.join(['[',col,']']) for col in cols]\n", "        return formated_cols\n", "    \n", "    ''' format column names to bold '''\n", "    def format_template(self,template:str)->str:\n", "        line = template.replace('class=\"dataframe\"',\"\")\n", "        return line\n", "        \n", "    ''' no borders'''\n", "    def borderless(self,df)->str:\n", "        template = df.to_html(index=False,border=0)\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        return template\n", "    \n", "    ''' no border except in header'''\n", "    def bordered_header(self,df)->str:\n", "        template = df.to_html(index=False,border=0)\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        template = template.replace(\"<th>\",'<th style=\"border:thin solid black;\">')\n", "        return template\n", "    \n", "    ''' no border except in header and bottom'''\n", "    def bordered_header_bottom(self,df)->str:\n", "        template = self.bordered_header(df)\n", "        lines = (template.split(\"\\n\"))\n", "        style = '<tr style = \"border-bottom: thin solid black;\"'\n", "        for i in range(len(lines)-1,-1,-1):\n", "            if lines[i].find(\"<tr\")!=-1:\n", "                lines[i] = lines[i].replace(\"<tr\",style)\n", "                break;\n", "        template = \"\\n\".join(lines);\n", "        template = template.replace(\"<table\",'<table style=\"border-collapse:collapse\"')\n", "        return template\n", "    \n", "    ''' only internal columns bordered would essentialy be same as latex so no need '''\n", "    def bordered_internal_columns(self,df)->str:\n", "        template = self.bordered_header(df)\n", "        t_style = '<table style=\"border-collapse:collapse; border-style: hidden;\"'\n", "        style = '<td style=\"border-left: thin solid black;border-right: thin solid black  \"'\n", "        template = template.replace(\"<td\",style)\n", "        template = template.replace('<table',t_style)\n", "        return template\n", "    \n", "    ''' only columns bordered '''\n", "    def bordered_columns(self,df)->str:\n", "        template = self.bordered_header(df)\n", "        style = '<td style=\"border: thin solid black;\"'\n", "        template = template.replace(\"<td\",style)\n", "        return template\n", "    \n", "    ''' partialy bordered'''\n", "    def partialy_bordered(self,df)->str:\n", "        template = self.bordered_header_bottom(df)\n", "        style = '<td style=\"border-left: thin solid black; border-right: thin solid black;\"'\n", "        template = template.replace(\"<td\",style)\n", "        lines = template.split(\"\\n\")\n", "        idx = []\n", "        for i in range(len(lines)):\n", "            line = lines[i]\n", "            found = line.find(\"<tr\")\n", "            if found!=-1:\n", "                idx.append(i)\n", "        mid = len(idx)//2\n", "        replaced = []\n", "        for i in idx[2:mid]:\n", "            temp = lines[i]\n", "            temp = temp.replace('<tr','<tr style=\"border-bottom:thin solid black\"')\n", "            lines[i] = temp\n", "        template = \"\\n\".join(lines)\n", "        return template\n", "    \n", "    ''' all borders '''\n", "    def bordered(self,df)->str:\n", "        template = df.to_html(index=False,border=1)\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        return template  "]}, {"cell_type": "markdown", "id": "c44256fa", "metadata": {}, "source": ["### Complex Template Generator\n", "Generate a table with a table in one of its columns. Results string representing the tabular in latex. The required input is 2 pandas dataframes (inner and outer). Extends the Template Generator.\n", "- Embedded (bordered outer table in which one of the columns contains a tables generate by template generator )"]}, {"cell_type": "code", "execution_count": 3, "id": "5ca720c8", "metadata": {}, "outputs": [], "source": ["class ComplexTemplateGenerator(TemplateGenerator):\n", "    ''' all borders for outer tables '''\n", "    def bordered(self,df_c)->str:\n", "        df = df_c.copy()\n", "        r_index = np.random.randint(0,df.shape[0])\n", "        c_index = np.random.randint(0,df.shape[1])\n", "        index = c_index\n", "        df.iat[r_index,c_index] = '*'\n", "        template = df.to_html(index=False,border=1)\n", "        template = self.clean_template(template)\n", "        template = self.format_template(template)\n", "        return template\n", "    \n", "    ''' format a complex(embedded) template '''\n", "    def format_complex_template(self,outer_template:str,inner_template:str):\n", "        lines = outer_template.split(\"\\n\")\n", "        for i in range(len(lines)):\n", "            line = lines[i]\n", "            if line.find(\"*\")!=-1:\n", "                lines[i] = lines[i].replace(\"*\",inner_template)\n", "            \n", "        template = \"\\n\".join(lines)\n", "        return template\n", "    \n", "    def embedded(self,df_outer,df_inner,f)->str:\n", "        df_outer = df_outer.astype(str)\n", "        df_inner = df_inner.astype(str)\n", "        outer_str = self.bordered(df_outer)\n", "        inner_str = f(df_inner)\n", "        template = self.format_complex_template(outer_str,inner_str)\n", "        return template"]}, {"cell_type": "markdown", "id": "08b34804", "metadata": {}, "source": ["### Table Generator\n", "\n", "Wraps a generated tabular around a table and adds a caption. Also draws borders for use later in making table mask. The expected input is a html format string coming from the template or complex template generator."]}, {"cell_type": "code", "execution_count": 4, "id": "fe84e8db", "metadata": {}, "outputs": [], "source": ["class TableGenerator:\n", "    def __init__(self):\n", "        self.transparent = ' bgcolor=\"white\"'\n", "        self.colour = ' bgcolor=\"red\"'\n", "        \n", "    def outlined_table(self,template:str)->str:\n", "        template = template.replace('<table',''.join(['<table',self.colour]))\n", "        return template\n", "        \n", "    def outlined_tables(self,templates:List[str])->List[str]:\n", "        tables = []\n", "        for template in templates:\n", "            table = self.outlined_table(template)\n", "            tables.append(table)\n", "        return tables\n", "        \n", "    def table(self,template:str)->str:\n", "        template = template.replace('<table',''.join(['<table',self.transparent]))\n", "        return template\n", "    \n", "    def tables(self,templates:List[str])->List[str]:\n", "        tables = []\n", "        for template in templates:\n", "            table = self.table(template)\n", "            tables.append(table)\n", "        return tables\n", "        "]}, {"cell_type": "markdown", "id": "b3e9c2db", "metadata": {}, "source": ["### Table Writer\n", "Uses a default latex template and writes tables to it (Includes writing random text), which can later be converted to pdf."]}, {"cell_type": "code", "execution_count": 5, "id": "4fc011a8", "metadata": {}, "outputs": [], "source": ["class TableWriter:    \n", "    def __init__(self):\n", "        self.env = Environment(loader=FileSystemLoader('.'))\n", "        self.template = self.env.get_template(\"templates/html/template.html\")\n", "        with open('templates/latex/config.json','r') as f:\n", "            paragraphs = json.load(f)\n", "        self.paragraphs = []\n", "        for key in paragraphs:\n", "            text = \"\\n\".join(paragraphs[key])\n", "            self.paragraphs.append(text)\n", "            \n", "    ''' just write a single table '''\n", "    def write_single(self,table:str)->str:\n", "        tables = [{\"header\":\"Table 1\",\n", "                 \"table\": table,'text':self.paragraphs[0]}]\n", "        template_vars = {\"title\" : \"Sales Funnel Report - National\",\n", "                 \"tables\": tables,\"close\":self.paragraphs[-1]}\n", "        html_out = self.template.render(template_vars)\n", "        return html_out\n", "    \n", "    ''' write multiple tables '''\n", "    def write(self,tables:List[str])->str:\n", "        result = []\n", "        for i in range(len(tables)):\n", "            t = {\"header\":\"Table \"+str(i+1),\"table\":tables[i],'text':self.paragraphs[i]}\n", "            result.append(t)\n", "        template_vars = {\"title\" : \"Sales Funnel Report - National\",\n", "                 \"tables\": result,\"close\":self.paragraphs[-1]}\n", "        html_out = self.template.render(template_vars)\n", "        return html_out"]}, {"cell_type": "markdown", "id": "8f5821a1", "metadata": {}, "source": ["### Pdf Generator\n", "Generate a pdf in bytes from a given latex string. "]}, {"cell_type": "code", "execution_count": 42, "id": "956bd2ed", "metadata": {}, "outputs": [], "source": ["class PdfGenerator:\n", "    ''' returns pdf bytes '''\n", "    def pdf(self,html_str:str)->bytes:\n", "        pdf = pdfkit.from_string(html_str,False,options={'--quiet':'','--dpi':200})\n", "        return pdf\n", "    \n", "    ''' return list of pdf bytes'''\n", "    def pdfs(self,html_strs:List[str])->List[bytes]:\n", "        pdfs = []\n", "        for html_str in html_strs:\n", "            pdf = self.pdf(html_str)\n", "            pdfs.append(pdf)\n", "        return pdfs"]}, {"cell_type": "markdown", "id": "0d4f3def", "metadata": {}, "source": ["### Pdf 2 Imgs\n", "Convert pdf to images, so we can convert to images and generate masks."]}, {"cell_type": "code", "execution_count": 43, "id": "e0a05728", "metadata": {}, "outputs": [], "source": ["class PdfToImg:\n", "    ''' pdf to img '''\n", "    def pdf_to_img(self,pdf_bytes:bytes):\n", "        img = convert_from_bytes(pdf_bytes,dpi=200)[0]\n", "        return np.asarray(img,dtype=np.uint8)\n", "    \n", "    ''' pdfs to imgs '''\n", "    def pdfs_to_imgs(self,pdfs:List[bytes]):\n", "        pdf_imgs = []\n", "        for pdf in pdfs:\n", "            img_pdf = self.pdf_to_img(pdf)\n", "            pdf_imgs.append(img_pdf)\n", "        return pdf_imgs"]}, {"cell_type": "markdown", "id": "6d00bfe4", "metadata": {}, "source": ["### Transformer\n", "Transform a given image , to try and mimic real world data of scanned images. The following transforms applicable\n", "- Gaussian Blur $k$ (kernel size), $(k,k)$ \n", "- Scale $(sx,sy)$ \n", "- Rotate $\\theta$"]}, {"cell_type": "code", "execution_count": 44, "id": "ae8bac15", "metadata": {}, "outputs": [], "source": ["class Transformer:\n", "    ''' blur (! later must investigate scan effect)'''\n", "    def blur(self,img,kernel):\n", "        sigma_x,sigma_y = 2,2\n", "        blurred_img = cv.<PERSON><PERSON><PERSON>(img,kernel,sigma_x,sigma_y)\n", "        return blurred_img\n", "    \n", "    ''' rotate '''\n", "    def rotate(self,img,theta:float,border=(255,255,255)):\n", "        height, width = img.shape[:2]\n", "        center = (width/2, height/2)\n", "        rotate_matrix = cv.getRotationMatrix2D(center=center, angle=theta, scale=1)\n", "        rotated_img = cv.warpAffine(src=img, M=rotate_matrix, dsize=(width, height),borderValue=border)\n", "        return rotated_img\n", "    \n", "    ''' dirtify data by applying sequence of transformations'''\n", "    def dirtify(self,img,k:int,s_x:int,s_y:int,theta:float,mask:bool):\n", "        m,n = img.shape[:2]\n", "        m,n = int(s_y*m),int(s_x*n)\n", "        dim = (n,m)\n", "        if not mask:\n", "            x = self.blur(img,kernel=(k,k))\n", "            y = cv.resize(x, dim, interpolation = cv.INTER_AREA)\n", "            z = self.rotate(y,theta=theta)\n", "        else:\n", "            x = img\n", "            y = cv.resize(x, dim, interpolation = cv.INTER_AREA)\n", "            z = self.rotate(y,theta=theta,border=(0,0,0))\n", "        return z"]}, {"cell_type": "markdown", "id": "a4610266", "metadata": {}, "source": ["### Mask Generator\n", "From the given pdf image generate table mask (label). This is done as follows.\n", "- Compute absolute difference between raw pdf img and outlined img\n", "- Apply adaptive thresholding to resulting image to obtain binary image\n", "- Detect external contours in binary image and fill bounding box regions of contours"]}, {"cell_type": "code", "execution_count": 45, "id": "be93493e", "metadata": {}, "outputs": [], "source": ["class PreProcessor:\n", "    ''' grayscale the image '''\n", "    def grayscale(self,img):\n", "        grayscaled = cv.cvtColor(img, cv.COLOR_RGB2GRAY)\n", "        return grayscaled\n", "    \n", "    ''' thresholding the image to a binary image '''\n", "    def threshold(self,img,mode='adaptive'):\n", "        if mode == 'adaptive':\n", "            thresh = cv.adaptiveThreshold(img, 255, 1, 1, 11, 2)\n", "            return thresh\n", "        elif mode=='otsu':\n", "            _,thresh = cv.threshold(img,128,255,cv.THRESH_BINARY |cv.THRESH_OTSU)\n", "            return thresh\n", "\n", "    ''' apply preprocessing steps ''' \n", "    def preprocess(self,img):\n", "        grayscaled = self.grayscale(img)\n", "        thresholded = self.threshold(grayscaled)\n", "        return thresholded\n", "    \n", "class MaskGenerator:\n", "    def __init__(self):\n", "        self.preprocessor = PreProcessor()\n", "       \n", "    ''' fill region with specified contours '''\n", "    def fill(self,shape,contours):\n", "        filled_binary_mask = np.zeros(shape,dtype=np.uint8)\n", "        bounding_rectangles = []\n", "        for contour in contours:\n", "            rect = cv.boundingRect(contour)\n", "            x,y,w,h = rect\n", "            filled_binary_mask[y:y+h,x:x+w] = 255\n", "        return filled_binary_mask\n", "            \n", "    ''' fill the mask '''\n", "    def fill_mask(self,mask):\n", "        binary_mask = self.preprocessor.preprocess(mask)\n", "        contours, _ = cv.findContours(binary_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)\n", "        binary_mask = self.fill(binary_mask.shape,contours)\n", "        return binary_mask\n", "    \n", "    ''' generate table mask taking difference of 2 imgs '''\n", "    def mask(self,raw_img,outlined_img):\n", "        x = raw_img\n", "        y = outlined_img\n", "        mask = abs(x-y)\n", "        filled_mask = self.fill_mask(mask)\n", "        return filled_mask\n", "    \n", "    def masks(self,raw_imgs,outlined_imgs):\n", "        masks = []\n", "        for i in range(len(raw_imgs)):\n", "            x = raw_imgs[i]\n", "            y = outlined_imgs[i]\n", "            mask = self.mask(x,y)\n", "            masks.append(mask)\n", "        return masks"]}, {"cell_type": "markdown", "id": "252bed73", "metadata": {}, "source": ["### Table Structure Generator\n", "Generate xml like string representing table structure. This is done by making system call to latexml."]}, {"cell_type": "code", "execution_count": 46, "id": "2ee7b72b", "metadata": {}, "outputs": [], "source": ["class StructureGenerator:\n", "    def __init__(self):\n", "        self.expression = '<table|<row|<cell|</cell>|</row>|</table>'\n", "        self.regex = re.compile(self.expression)\n", "\n", "    ''' markup representing table '''\n", "    def structure(self,table:str)->str:\n", "        html = (table)\n", "        html = html.replace(\"<thead>\\n\",\"\")\n", "        html = html.replace(\"</thead>\",\"\")\n", "        html = html.replace(\"<tr\",\"<row\")\n", "        html = html.replace(\"/tr>\",\"/row>\")\n", "        html = html.replace(\"<th\",\"<td\")\n", "        html = html.replace(\"/th>\",\"/td>\")\n", "        html = html.replace(\"<td\",\"<cell\")\n", "        html = html.replace(\"/td>\",\"/cell>\")\n", "        lines = re.findall(self.regex, html)\n", "        lines = [lines[i]+'>' if lines[i].find(\">\")==-1 else lines[i] for i in range(len(lines))]\n", "        structure = \"\\n\".join(lines)\n", "        return structure\n", "    \n", "    ''' generate table structures '''\n", "    def structures(self,tables:List[str])->List[str]:\n", "        structures = []\n", "        for table in tables:\n", "            structure = self.structure(table)\n", "            structures.append(structure)\n", "        return structures"]}, {"cell_type": "markdown", "id": "d114446a", "metadata": {}, "source": ["### Metadata Generator\n", "Metadata to be generated includes the following\n", "- Number of tables - This is counted by counting the number of connected components in the mask (table regions should be disjoint regions of white pixels)\n", "- Bounding boxes (of tables) - Generated by detecting external contours and returning nounding box\n", "- XML strings of structure - latex string representation as arg yo structure generator"]}, {"cell_type": "code", "execution_count": 47, "id": "0e0c34ce", "metadata": {}, "outputs": [], "source": ["class MetadataGenerator(StructureGenerator):    \n", "    ''' number of tables from table mask '''\n", "    def number_of_tables(self,table_mask)->int:\n", "        num,_ = cv.connectedComponents(table_mask)\n", "        return num-1\n", "    \n", "    ''' sort the contours top to bottom '''\n", "    def sort(self,contours):\n", "        y_values = []\n", "        for contour in contours:\n", "            x,y,w,h = cv.boundingRect(contour)\n", "            y_values.append(y)\n", "        y_values = np.array(y_values)\n", "        idx = np.argsort(y_values)\n", "        sorted_contours = [contours[i] for i in idx]\n", "        return sorted_contours\n", "    \n", "    ''' bounding boxes '''\n", "    def bounding_boxes(self,table_mask):\n", "        contours , _ = cv.findContours(table_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)\n", "        sorted_contours = self.sort(contours)\n", "        boxes = []\n", "        for contour in sorted_contours:\n", "            box = cv.boundingRect(contour)\n", "            boxes.append(box)\n", "        return boxes\n", "    \n", "    ''' generate metadata '''\n", "    def metadata(self,table_mask,tables:List[str]):\n", "        num_tables = self.number_of_tables(table_mask)\n", "        bboxes = self.bounding_boxes(table_mask)\n", "        bboxes_data = [bbox for bbox in bboxes]\n", "        structures = self.structures(tables)\n", "        result = {'no':num_tables,'bounding_boxes':bboxes_data,'structures':structures}\n", "        return result"]}, {"cell_type": "markdown", "id": "2e6f1e5d", "metadata": {}, "source": ["### Data Source\n", "A pool of dataframes in which table data is pulled from. When data is needed for a table following happens\n", "a call to sample with number of datafframes needed and mode (important for limits). The sample randomly selects dataframes and samples each randomly. Then shuffles dataframes order."]}, {"cell_type": "code", "execution_count": 48, "id": "522aac9b", "metadata": {}, "outputs": [], "source": ["class DataSource:\n", "    def __init__(self,path):\n", "        self.data = self.load(path)\n", "        self.N = len(self.data)\n", "        self.MIN_ROWS = 2\n", "        self.MIN_OUTER_ROWS = 4\n", "        self.MAX_ROWS = 30\n", "        self.MIN_COLS = 2\n", "        self.MAX_COLS = 7\n", "        self.MAX_COLS_INNER = 3\n", "        self.MAX_COLS_OUTER = 3\n", "        self.STATE = 42\n", "        \n", "    ''' prepare dataframe '''\n", "    def prepare(self,df):\n", "        df.columns = self.format_columns(df.columns)\n", "        nan_value = float(\"NaN\")\n", "        df.replace(\"\", \"-\", inplace=True)\n", "        df.replace(\"NaN\",\"-\", inplace=True)\n", "        df.dropna(inplace=True)\n", "        df = df.iloc[:300,:]\n", "        df = df.astype(str)\n", "        return df\n", "    \n", "    ''' load dataframes '''\n", "    def load(self,path):\n", "        fnames = glob.glob(path)\n", "        data = []\n", "        for fname in fnames:\n", "            df = pd.read_csv(fname,encoding=\"utf-8\")\n", "            df = self.prepare(df)\n", "            for c in df.columns:\n", "                df[c] = df[c].apply(self.reduce)\n", "            data.append(df)\n", "        return data\n", "    \n", "    ''' reduce rows '''\n", "    def reduce(self,x:str)->str:\n", "        if type(x) is not str:\n", "            return x\n", "        if len(x)>12:\n", "            return x[:12]\n", "        return x\n", "    \n", "    ''' format column headers '''\n", "    def format_columns(self,columns):\n", "        new_columns = []\n", "        for c in columns:\n", "            c = c.replace(\"_\",\" \")\n", "            c = c.title()\n", "            if len(c)>12:\n", "                c = c.split(' ')[0][:12]\n", "            new_columns.append(c)\n", "        return new_columns\n", "    \n", "    ''' shuffle '''\n", "    def shuffle(self):\n", "        p = np.random.permutation(self.N)\n", "        self.data = [self.data[p[i]] for i in range(len(p))]\n", "    \n", "    ''' select a dataframe '''\n", "    def select(self,i,rows,cols):\n", "        df = self.data[i].copy()\n", "        df = df.sample(frac=1,random_state=self.STATE)\n", "        df = df.iloc[:rows,np.random.permutation(cols)]\n", "        df = df.iloc[:,:cols]\n", "        return df\n", "        \n", "    ''' sample for simple tables'''\n", "    def sample(self,n,mode=0):\n", "        sample = []\n", "        p = np.random.permutation(n)\n", "        max_rows = self.MAX_ROWS//n\n", "        if mode == 0:\n", "            max_cols = 7\n", "        else:\n", "            max_rows = max(self.MIN_OUTER_ROWS,max_rows//2)\n", "            max_cols = 3\n", "        for i in range(n):\n", "            rows = int(np.random.uniform(self.MIN_ROWS,max_rows+1))\n", "            cols = int(np.random.uniform(self.MIN_COLS,max_cols+1))\n", "            df = self.select(p[i],rows,cols)\n", "            sample.append(df)\n", "        self.shuffle()\n", "        return sample"]}, {"cell_type": "markdown", "id": "835154f7", "metadata": {}, "source": ["### Pipeline\n", "Combines all of the above to generate dataset the steps to generate a dataset are.\n", "- Given list of types generate templates (templates func)\n", "- From templates generate resulting pdf,img,mask (datum func)\n", "- Distort datum (applies transforms to img and mask)\n", "- Annotate distort datum (uses metadata generator)"]}, {"cell_type": "code", "execution_count": 49, "id": "810586e1", "metadata": {}, "outputs": [], "source": ["class HtmlGeneratorPipeline:\n", "    def __init__(self,path):\n", "        self.templator = TemplateGenerator()\n", "        self.c_templator = ComplexTemplateGenerator()\n", "        self.table_generator = TableGenerator()\n", "        self.table_writer = TableWriter()\n", "        self.pdf_generator = PdfGenerator()\n", "        self.pdf_to_img = PdfToImg()\n", "        self.mask_generator = MaskGenerator()\n", "        self.transformer = Transformer()\n", "        self.metadata_generator = MetadataGenerator()\n", "        self.data_source = DataSource(path)\n", "        self.template_funcs = [\n", "            self.templator.borderless,\n", "            self.templator.bordered_header,\n", "            self.templator.bordered_header_bottom,\n", "            self.templator.bordered_internal_columns,\n", "            self.templator.bordered_columns,\n", "            self.templator.partialy_bordered,\n", "            self.templator.bordered,\n", "            self.c_templator.embedded\n", "        ]\n", "        \n", "    ''' generate simple templates from given dfs and types'''\n", "    def templates(self,types:List[int])->List[str]:\n", "        n = len(types)\n", "        templates = []\n", "        sample = self.data_source.sample(n,0)\n", "        for i in range(n):\n", "            index = types[i]\n", "            if index!=7:\n", "                df = sample[i]\n", "                func = self.template_funcs[index]\n", "                template = func(df)\n", "                templates.append(template)\n", "            else:\n", "                if n == 1:\n", "                    df_outer,df_inner = self.data_source.sample(2,1)[:2]\n", "                else:\n", "                    df_outer,df_inner = self.data_source.sample(n,1)[:2]\n", "                df_inner = df_inner.iloc[:len(df_outer)//2,:]\n", "                func = self.template_funcs[index]\n", "                func_inner = self.template_funcs[np.random.randint(0,7)]\n", "                template = func(df_outer,df_inner,func_inner)\n", "                templates.append(template)              \n", "        return templates\n", "    \n", "    ''' generate a single datapoint {mask,pdf,tables,img} '''\n", "    def datum(self,types:List[int])->dict:\n", "        templates = self.templates(types)\n", "        # step 1 pdf and outlined pdf\n", "        tables = self.table_generator.tables(templates)\n", "        outlined_tables = self.table_generator.outlined_tables(templates)\n", "        tex = self.table_writer.write(tables)\n", "        pdf = self.pdf_generator.pdf(tex)\n", "        outlined_table = self.table_writer.write(outlined_tables)\n", "        outlined_pdf = self.pdf_generator.pdf(outlined_table)        \n", "        \n", "        # step 2 images and masks \n", "        pdfs = [pdf]\n", "        outlined_pdfs = [outlined_pdf]\n", "        imgs = self.pdf_to_img.pdfs_to_imgs(pdfs)\n", "        outlined_imgs = self.pdf_to_img.pdfs_to_imgs(outlined_pdfs)\n", "        masks = self.mask_generator.masks(imgs,outlined_imgs)\n", "        \n", "        # step 3 make results\n", "        results = {\"mask\":masks[0],\"img\":imgs[0],\"pdf\":pdfs[0],'tables':tables}\n", "        \n", "        return results\n", "    \n", "    ''' apply transformation using params to dirty data (img and mask) '''\n", "    def distort_datum(self,datum:dict,k:int=7,s_x:int=1,s_y:int=1,theta:float=0)->dict:\n", "        img = datum['img']\n", "        mask = datum['mask']\n", "        img = self.transformer.dirtify(img,k,s_x,s_y,theta,False)\n", "        mask = self.transformer.dirtify(mask,k,s_x,s_y,theta,True)\n", "        return img,mask\n", "    \n", "    ''' label a datum '''\n", "    def label(self,datum:dict):\n", "        mask = datum['mask']\n", "        tables = datum['tables']\n", "        metadata = self.metadata_generator.metadata(mask,tables)\n", "        return metadata\n", "    \n", "    ''' combinations of tables '''\n", "    def generate_combinations(self,types:List[str]):\n", "        combinations = []\n", "        counts = {i:0 for i in types}\n", "        for i in range(1,4):\n", "            c = itertools.combinations(types, i)\n", "            for j in c:\n", "                combinations.append(list(j))\n", "                for k in j:\n", "                    counts[k] = counts[k]+1\n", "        return counts,combinations\n", "    \n", "    ''' save datum along with its annotation '''\n", "    def save(self,datum:dict,annotation:dict,config):\n", "        _id = annotation['id']\n", "        img_path = config['img_path']+_id+'.png'\n", "        mask_path = config['mask_path']+_id+'.png'\n", "        annotation_path = config['annotation_path']+_id+'.json'\n", "        \n", "        # save img and mask\n", "        img = datum['img']\n", "        mask = datum['mask']\n", "        \n", "        cv.imwrite(img_path,img)\n", "        cv.imwrite(mask_path,mask)\n", "        \n", "        # save annotation\n", "        with open(annotation_path,'w') as f:\n", "            json.dump(annotation,f)\n", "        \n", "    ''' generate dataset '''    \n", "    def generate_data(self,config):\n", "        sample_size = config[\"sample_size\"]\n", "        types = config[\"types\"]\n", "        N = sample_size\n", "        counts,combinations = self.generate_combinations(types)\n", "        n = len(combinations)\n", "        stats = {i:0 for i in types}\n", "        _id = 0\n", "        for i in range(N):\n", "            idx = int(np.random.uniform(0,n))\n", "            sub_types = combinations[idx]\n", "            for c in sub_types:\n", "                stats[c] = stats[c]+1\n", "            datum = self.datum(sub_types)\n", "            theta = np.random.uniform(-2,2)\n", "            img,mask = self.distort_datum(datum,theta=theta)\n", "            datum['img'] = img\n", "            datum['mask'] = mask\n", "            label = self.label(datum)\n", "            label[\"id\"] = str(_id)\n", "            self.save(datum,label,config)\n", "            _id =_id+1\n", "        return _id,stats"]}, {"cell_type": "markdown", "id": "8625eb4c", "metadata": {}, "source": ["### <font color=\"red\">Check Bottlenecks </font>"]}, {"cell_type": "code", "execution_count": 50, "id": "4cb3403a", "metadata": {}, "outputs": [], "source": ["def test_datum(pipeline,types,output):\n", "    prof = cProfile.Profile()\n", "    prof.enable()\n", "    pipeline.datum(types)\n", "    prof.disable()\n", "    prof.dump_stats(\"\".join(['profiles/',output,'.profile']))\n", "    \n", "def test_distort(pipeline,types,output):\n", "    datum = pipeline.datum(types)\n", "    prof = cProfile.Profile()\n", "    prof.enable()\n", "    theta = np.random.uniform(-2,2)\n", "    pipeline.distort_datum(datum,theta=theta)\n", "    prof.disable()\n", "    prof.dump_stats(\"\".join(['profiles/',output,'.profile']))\n", "    \n", "def test_label(pipeline,types,output):\n", "    datum = pipeline.datum(types)\n", "    prof = cProfile.Profile()\n", "    prof.enable()\n", "    pipeline.label(datum)\n", "    prof.disable()\n", "    prof.dump_stats(\"\".join(['profiles/',output,'.profile']))\n", "    \n", "def test_pipeline(pipeline,config,output):\n", "    prof = cProfile.Profile()\n", "    prof.enable()\n", "    pipeline.generate_data(config)\n", "    prof.disable()\n", "    prof.dump_stats(\"\".join(['profiles/',output,'.profile']))"]}, {"cell_type": "code", "execution_count": 53, "id": "55b989e5", "metadata": {}, "outputs": [], "source": ["config = {\n", "\"sample_size\":1,\n", "\"types\":[0,1,2,3,4,5,6,7],\n", "\"types_map\":{\n", "    \"0\":\"Borderless\",\n", "    \"1\":\"Bordered Header\",\n", "    \"2\":\"Bordered Header Bottom\",\n", "    \"3\":\"Bordered Internal Columns\",\n", "    \"4\":\"Bordered Columns\",\n", "    \"5\":\"Partially Bordered\",\n", "    \"6\":\"Bordered\",\n", "    \"7\":\"Embedded\"\n", "    },\n", "\"img_path\":\"data/html/imgs/\",\n", "\"mask_path\":\"data/html/masks/\",\n", "\"annotation_path\":\"data/html/annotations/\"\n", "}"]}, {"cell_type": "code", "execution_count": 54, "id": "53a735a1", "metadata": {}, "outputs": [], "source": ["path = 'sources/*.csv'\n", "pipeline = HtmlGeneratorPipeline(path)\n", "types = config['types']\n", "_,combinations = pipeline.generate_combinations(types)\n", "sub_types = combinations[-1]"]}, {"cell_type": "code", "execution_count": 55, "id": "1df0b465", "metadata": {}, "outputs": [], "source": ["test_datum(pipeline,sub_types,'html-datum')"]}, {"cell_type": "code", "execution_count": 56, "id": "6dddb042", "metadata": {}, "outputs": [], "source": ["test_distort(pipeline,sub_types,'html-distort')"]}, {"cell_type": "code", "execution_count": 57, "id": "9e1189e6", "metadata": {}, "outputs": [], "source": ["test_label(pipeline,sub_types,'html-label')"]}, {"cell_type": "code", "execution_count": 58, "id": "bfce92d0", "metadata": {}, "outputs": [], "source": ["test_pipeline(pipeline,config,'html-pipeline')"]}, {"cell_type": "code", "execution_count": 60, "id": "c4e2f123", "metadata": {}, "outputs": [{"data": {"text/plain": ["(2339, 1653, 3)"]}, "execution_count": 60, "metadata": {}, "output_type": "execute_result"}], "source": ["datum['img'].shape"]}, {"cell_type": "code", "execution_count": 59, "id": "07228861", "metadata": {}, "outputs": [{"data": {"text/plain": ["<matplotlib.image.AxesImage at 0x7f9b1db0e0d0>"]}, "execution_count": 59, "metadata": {}, "output_type": "execute_result"}, {"data": {"image/png": "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\n", "text/plain": ["<Figure size 432x288 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["datum = pipeline.datum([7])\n", "label = pipeline.label(datum)\n", "plt.imshow(datum['img'])"]}, {"cell_type": "code", "execution_count": null, "id": "0de4955a", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8c4a728b", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}