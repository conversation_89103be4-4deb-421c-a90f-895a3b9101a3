"""
结构生成器

负责根据配置创建表格的逻辑结构。

边框处理采用新方案：
1. 先确定所有网格线的状态（有线/无线）
2. 执行单元格合并
3. 将网格线状态映射到合并后单元格的外轮廓
这种方法确保了边框的一致性，避免了合并单元格导致的边框显示问题。
"""

import logging
import numpy as np
from typing import List, Optional

from ..config import StructureConfig, RangeConfig, ResolvedStructureParams
from ..models import TableModel, RowModel, CellModel, BorderDecisions, CellBorderStyle


class StructureBuilder:
    """
    结构生成器类
    
    根据配置生成表格的逻辑结构，包括行列布局和单元格ID分配。
    """
    
    def __init__(self, seed: int):
        """
        初始化结构生成器
        
        Args:
            seed: 随机种子，用于确保可复现性
        """
        self.random_state = np.random.RandomState(seed)
        self.logger = logging.getLogger(__name__)
        
    def build(self, config: ResolvedStructureParams, border_mode: str = 'full', border_details: dict = None) -> TableModel:
        """
        构建表格结构

        Args:
            config: 解析后的结构参数
            border_mode: 边框模式 ('full', 'none', 'semi')
            border_details: 边框详细配置（用于semi模式）

        Returns:
            包含结构信息和边框决策的表格模型（内容为空）
        """
        # 检查是否使用复杂表头
        if config.complex_header and config.complex_header.enabled:
            self.logger.debug("使用复杂表头构建表格结构")
            return self._build_complex_header_table(config.complex_header, config, border_mode, border_details)

        # V3.1新逻辑：使用头/体分离的标准方式构建表格
        # V3.3修改：直接使用body_rows，避免行数计算问题
        body_rows = self._get_randomized_value(config.body_rows)
        num_cols = self._get_randomized_value(config.cols)
        header_rows = self._get_randomized_value(config.header_rows)
        total_rows = header_rows + body_rows

        self.logger.debug(f"开始构建表格结构: {total_rows}x{num_cols} (表头:{header_rows}, 主体:{body_rows})")

        # 创建表格模型
        table_model = TableModel()

        # 构建表头行
        for row_index in range(header_rows):
            row_model = RowModel(row_index=row_index)
            for col_index in range(num_cols):
                cell_id = f"cell-{row_index}-{col_index}"
                cell_model = CellModel(
                    cell_id=cell_id,
                    row_index=row_index,
                    col_index=col_index,
                    row_span=1,
                    col_span=1,
                    content="",  # 内容将由ContentBuilder填充
                    is_header=True  # 表头单元格
                )
                row_model.cells.append(cell_model)
            table_model.header_rows.append(row_model)

        # 构建主体行
        for row_index in range(header_rows, total_rows):
            row_model = RowModel(row_index=row_index)
            for col_index in range(num_cols):
                cell_id = f"cell-{row_index}-{col_index}"
                cell_model = CellModel(
                    cell_id=cell_id,
                    row_index=row_index,
                    col_index=col_index,
                    row_span=1,
                    col_span=1,
                    content="",  # 内容将由ContentBuilder填充
                    is_header=False  # 主体单元格
                )
                row_model.cells.append(cell_model)
            table_model.body_rows.append(row_model)

        # 如果配置了合并概率，则执行单元格合并逻辑
        if config.merge_probability > 0:
            self._merge_cells_separated(table_model, config)

        # 新增：在合并后直接为每个单元格分配边框
        self._assign_borders_to_cells(table_model, border_mode, border_details)

        # 保持兼容性：创建空的边框决策对象
        table_model.border_decisions = BorderDecisions()

        self.logger.debug(f"表格结构构建完成: {table_model}")
        self.logger.debug("单元格边框分配完成")
        return table_model
    
    def _create_initial_grid(self, num_rows: int, num_cols: int) -> List[List[Optional[CellModel]]]:
        """
        创建初始的二维网格
        
        Args:
            num_rows: 行数
            num_cols: 列数
            
        Returns:
            填充了CellModel的二维网格
        """
        grid = []
        for r in range(num_rows):
            row = []
            for c in range(num_cols):
                cell_id = f"cell-{r}-{c}"
                cell = CellModel(
                    cell_id=cell_id,
                    row_index=r,
                    col_index=c,
                    is_header=(r == 0)  # 第一行作为表头
                )
                row.append(cell)
            grid.append(row)
        return grid
    
    def _convert_grid_to_model(self, grid: List[List[Optional[CellModel]]]) -> TableModel:
        """
        将二维网格转换为TableModel
        
        Args:
            grid: 二维网格
            
        Returns:
            表格模型
        """
        table_model = TableModel()
        
        for r_idx, row_data in enumerate(grid):
            row_model = RowModel(row_index=r_idx)
            for cell in row_data:
                if cell is not None:  # 跳过被合并的None占位符
                    row_model.cells.append(cell)
            table_model.rows.append(row_model)
            
        return table_model

    def _get_randomized_value(self, value) -> int:
        """
        获取随机化的值

        Args:
            value: 可以是int或RangeConfig

        Returns:
            确定的整数值
        """
        if isinstance(value, RangeConfig):
            return self.random_state.randint(value.min, value.max + 1)
        else:
            return value

    def _merge_cells(self, grid: List[List[Optional[CellModel]]], config: StructureConfig):
        """
        执行单元格合并逻辑

        Args:
            grid: 二维网格
            config: 结构配置
        """
        num_rows = len(grid)
        num_cols = len(grid[0]) if num_rows > 0 else 0

        # 遍历网格中的每一个单元格作为潜在的合并起始点
        for r in range(num_rows):
            for c in range(num_cols):
                # 如果当前单元格已经被其他合并操作覆盖，则跳过
                if grid[r][c] is None:
                    continue

                # 根据概率决定是否尝试合并
                if self.random_state.rand() < config.merge_probability:
                    # 随机确定要跨越的行数和列数
                    max_span_r = min(config.max_row_span, num_rows - r)
                    max_span_c = min(config.max_col_span, num_cols - c)

                    row_span = self.random_state.randint(1, max_span_r + 1)
                    col_span = self.random_state.randint(1, max_span_c + 1)

                    # 如果跨度为1x1，则相当于不合并，跳过
                    if row_span == 1 and col_span == 1:
                        continue

                    # 检查目标合并区域内的所有单元格是否都可用
                    is_merge_possible = True
                    for dr in range(row_span):
                        for dc in range(col_span):
                            if grid[r + dr][c + dc] is None:
                                is_merge_possible = False
                                break
                        if not is_merge_possible:
                            break

                    # 如果可以合并，则执行合并操作
                    if is_merge_possible:
                        # 更新起始单元格的 row_span 和 col_span
                        grid[r][c].row_span = row_span
                        grid[r][c].col_span = col_span

                        # 将被合并的单元格在网格中标记为 None
                        for dr in range(row_span):
                            for dc in range(col_span):
                                if dr == 0 and dc == 0:
                                    continue  # 跳过起始单元格本身
                                grid[r + dr][c + dc] = None

                        self.logger.debug(f"合并单元格: ({r},{c}) 跨度 {row_span}x{col_span}")

    def _merge_cells_separated(self, table_model: TableModel, config: ResolvedStructureParams):
        """
        在头/体分离的表格中执行单元格合并

        确保合并逻辑被严格限制在各自的区域内（表头内的合并不能延伸到主体）

        Args:
            table_model: 表格模型
            config: 结构配置
        """
        # 在表头区域内进行合并
        if table_model.header_rows:
            self._merge_cells_in_section(table_model.header_rows, config, "表头")

        # 在主体区域内进行合并
        if table_model.body_rows:
            self._merge_cells_in_section(table_model.body_rows, config, "主体")

    def _merge_cells_in_section(self, rows: List, config: ResolvedStructureParams, section_name: str):
        """
        在指定的行区域内进行单元格合并

        Args:
            rows: 行列表
            config: 结构配置
            section_name: 区域名称（用于日志）
        """
        if not rows:
            return

        num_rows = len(rows)
        num_cols = len(rows[0].cells) if rows else 0

        self.logger.debug(f"开始在{section_name}区域进行单元格合并: {num_rows}x{num_cols}")

        # 创建一个临时网格来跟踪合并状态和需要移除的单元格
        merged = [[False for _ in range(num_cols)] for _ in range(num_rows)]
        cells_to_remove = []  # 存储需要移除的单元格 (row_idx, col_idx)

        for r in range(num_rows):
            for c in range(num_cols):
                if merged[r][c]:
                    continue

                # 根据概率决定是否合并
                if self.random_state.random() < config.merge_probability:
                    # 确定合并的跨度（限制在当前区域内）
                    max_row_span = min(config.max_row_span, num_rows - r)
                    max_col_span = min(config.max_col_span, num_cols - c)

                    row_span = self.random_state.randint(1, max_row_span + 1)
                    col_span = self.random_state.randint(1, max_col_span + 1)

                    # 检查合并区域是否可用
                    can_merge = True
                    for dr in range(row_span):
                        for dc in range(col_span):
                            if merged[r + dr][c + dc]:
                                can_merge = False
                                break
                        if not can_merge:
                            break

                    if can_merge and (row_span > 1 or col_span > 1):
                        # 执行合并
                        cell = rows[r].cells[c]
                        cell.row_span = row_span
                        cell.col_span = col_span

                        # 标记合并区域并记录需要移除的单元格
                        for dr in range(row_span):
                            for dc in range(col_span):
                                merged[r + dr][c + dc] = True
                                # 记录除了主单元格之外需要移除的单元格
                                if not (dr == 0 and dc == 0):
                                    cells_to_remove.append((r + dr, c + dc))

                        self.logger.debug(f"在{section_name}区域合并单元格: ({r},{c}) 跨度 {row_span}x{col_span}")

        # 移除被合并的单元格
        self._remove_merged_cells(rows, cells_to_remove, section_name)

    def _remove_merged_cells(self, rows: List, cells_to_remove: List, section_name: str):
        """
        移除被合并的单元格

        Args:
            rows: 行列表
            cells_to_remove: 需要移除的单元格位置列表 [(row_idx, col_idx), ...]
            section_name: 区域名称（用于日志）
        """
        if not cells_to_remove:
            return

        # 创建一个映射，将逻辑位置映射到实际的单元格
        # 因为单元格的col_index是逻辑位置，而在rows[row_idx].cells中的索引是物理位置
        removed_count = 0

        # 按行处理
        cells_by_row = {}
        for row_idx, col_idx in cells_to_remove:
            if row_idx not in cells_by_row:
                cells_by_row[row_idx] = set()
            cells_by_row[row_idx].add(col_idx)

        for row_idx, col_indices_to_remove in cells_by_row.items():
            if row_idx < len(rows):
                # 找到需要移除的单元格（通过col_index匹配）
                cells_to_keep = []
                for cell in rows[row_idx].cells:
                    if cell.col_index not in col_indices_to_remove:
                        cells_to_keep.append(cell)
                    else:
                        removed_count += 1
                        self.logger.debug(f"移除{section_name}区域被合并的单元格: {cell.cell_id} (逻辑位置: {cell.row_index},{cell.col_index})")

                # 更新行的单元格列表
                rows[row_idx].cells = cells_to_keep

        self.logger.debug(f"在{section_name}区域总共移除了 {removed_count} 个被合并的单元格")

    def _assign_borders_to_cells(self, table_model: TableModel, border_mode: str, border_details: dict = None):
        """
        直接为每个单元格分配边框样式

        这是新的简化方案：在单元格合并后，直接为每个最终单元格计算边框。

        Args:
            table_model: 表格模型（已完成合并）
            border_mode: 边框模式 ('full', 'none', 'semi')
            border_details: 边框详细配置
        """
        self.logger.debug(f"开始为单元格分配边框，模式: {border_mode}")

        if border_mode == 'full':
            # 所有边框都显示
            self._assign_full_borders(table_model)
        elif border_mode == 'none':
            # 所有边框都不显示（默认值，无需操作）
            pass
        elif border_mode == 'semi':
            # 半边框模式：根据概率和配置分配边框
            self._assign_semi_borders(table_model, border_details or {})
        else:
            # 默认使用全边框模式
            self._assign_full_borders(table_model)

        self.logger.debug("单元格边框分配完成")

    def _assign_full_borders(self, table_model: TableModel):
        """
        分配全边框模式：所有单元格的所有边框都显示
        """
        for row in table_model.rows:
            for cell in row.cells:
                cell.border_style = CellBorderStyle(top=1, right=1, bottom=1, left=1)

        self.logger.debug("已分配全边框模式")

    def _assign_semi_borders(self, table_model: TableModel, border_details: dict):
        """
        分配半边框模式：根据概率和配置分配边框
        """
        row_prob = border_details.get('row_line_probability', 0.5)
        col_prob = border_details.get('col_line_probability', 0.5)
        outer_frame = border_details.get('outer_frame', True)
        header_separator = border_details.get('header_separator', True)

        self.logger.debug(f"半边框配置: row_prob={row_prob}, col_prob={col_prob}, outer_frame={outer_frame}, header_separator={header_separator}")

        # 1. 处理外框
        if outer_frame:
            self._apply_outer_frame(table_model)

        # 2. 处理表头分割线
        if header_separator:
            self._apply_header_separator(table_model)

        # 3. 处理内部边框（核心逻辑）
        self._apply_internal_borders(table_model, row_prob, col_prob)

    def _apply_outer_frame(self, table_model: TableModel):
        """
        应用外框边框：表格边缘的所有单元格都有相应的外边框
        """
        for row in table_model.rows:
            for cell in row.cells:
                # 顶边框：第一行
                if cell.row_index == 0:
                    cell.border_style.top = 1

                # 底边框：最后一行
                if cell.row_index + cell.row_span - 1 == table_model.num_rows - 1:
                    cell.border_style.bottom = 1

                # 左边框：第一列
                if cell.col_index == 0:
                    cell.border_style.left = 1

                # 右边框：最后一列
                if cell.col_index + cell.col_span - 1 == table_model.num_cols - 1:
                    cell.border_style.right = 1

        self.logger.debug("已应用外框边框")

    def _apply_header_separator(self, table_model: TableModel):
        """
        应用表头分割线：表头最后一行的底边框
        """
        if not table_model.header_rows:
            return

        # 找到表头的最后一行索引
        last_header_row_idx = table_model.header_rows[-1].row_index

        # 为表头最后一行的所有单元格添加底边框
        for row in table_model.header_rows:
            if row.row_index == last_header_row_idx:
                for cell in row.cells:
                    cell.border_style.bottom = 1

        self.logger.debug(f"已应用表头分割线，表头最后一行: {last_header_row_idx}")

    def _apply_internal_borders(self, table_model: TableModel, row_prob: float, col_prob: float):
        """
        应用内部边框：基于网格线的概率判断

        新方案：先确定网格线状态，再应用到合并后的单元格
        这种方法确保了边框的一致性，避免了合并单元格导致的边框不连续问题。

        算法步骤：
        1. 为每条内部网格线进行独立的概率判断（有线/无线）
        2. 将网格线状态直接映射到合并后单元格的外轮廓边框

        Args:
            table_model: 已完成合并的表格模型
            row_prob: 水平网格线显示概率
            col_prob: 垂直网格线显示概率
        """
        # 1. 确定内部水平网格线状态（行间的边界线）
        total_rows = table_model.num_rows
        total_cols = table_model.num_cols

        # 内部水平网格线：total_rows - 1 条
        # 索引i表示行i和行i+1之间的边界线
        h_grid_lines = []
        for i in range(total_rows - 1):
            h_grid_lines.append(self.random_state.random() < row_prob)

        # 2. 确定内部垂直网格线状态（列间的边界线）
        # 索引j表示列j和列j+1之间的边界线
        v_grid_lines = []
        for j in range(total_cols - 1):
            v_grid_lines.append(self.random_state.random() < col_prob)

        self.logger.debug(f"确定网格线状态: {len(h_grid_lines)} 条水平线，{len(v_grid_lines)} 条垂直线")

        # 3. 将网格线状态应用到所有单元格
        self._apply_grid_lines_to_cells(table_model, h_grid_lines, v_grid_lines)

    def _apply_grid_lines_to_cells(self, table_model: TableModel, h_grid_lines: list, v_grid_lines: list):
        """
        将网格线状态应用到合并后的单元格

        这个方法将预先确定的网格线状态映射到每个单元格的外轮廓边框。
        对于合并单元格，只考虑其外轮廓对应的网格线，内部的网格线自然被忽略。

        Args:
            table_model: 表格模型（已完成合并）
            h_grid_lines: 水平网格线状态列表，索引i表示行i和行i+1之间的边界线
            v_grid_lines: 垂直网格线状态列表，索引j表示列j和列j+1之间的边界线
        """
        total_rows = table_model.num_rows
        total_cols = table_model.num_cols

        self.logger.debug(f"开始应用网格线到单元格: 表格尺寸 {total_rows}x{total_cols}")

        for row in table_model.rows:
            for cell in row.cells:
                # 应用顶边框：如果不是第一行，检查对应的水平网格线
                if cell.row_index > 0:
                    h_line_idx = cell.row_index - 1
                    if 0 <= h_line_idx < len(h_grid_lines) and h_grid_lines[h_line_idx]:
                        cell.border_style.top = 1

                # 应用底边框：如果单元格底部不是表格边缘，检查对应的水平网格线
                bottom_row = cell.row_index + cell.row_span - 1
                if bottom_row < total_rows - 1:
                    h_line_idx = bottom_row
                    if 0 <= h_line_idx < len(h_grid_lines) and h_grid_lines[h_line_idx]:
                        cell.border_style.bottom = 1

                # 应用左边框：如果不是第一列，检查对应的垂直网格线
                if cell.col_index > 0:
                    v_line_idx = cell.col_index - 1
                    if 0 <= v_line_idx < len(v_grid_lines) and v_grid_lines[v_line_idx]:
                        cell.border_style.left = 1

                # 应用右边框：如果单元格右侧不是表格边缘，检查对应的垂直网格线
                right_col = cell.col_index + cell.col_span - 1
                if right_col < total_cols - 1:
                    v_line_idx = right_col
                    if 0 <= v_line_idx < len(v_grid_lines) and v_grid_lines[v_line_idx]:
                        cell.border_style.right = 1

        self.logger.debug("已将网格线状态应用到所有单元格")



    def _build_complex_header_table(self, complex_header_config, config, border_mode: str = 'full', border_details: dict = None) -> TableModel:
        """
        构建具有复杂表头的表格

        Args:
            complex_header_config: 复杂表头配置
            config: 完整的结构配置
            border_mode: 边框模式
            border_details: 边框详细配置

        Returns:
            包含复杂表头和数据行的表格模型
        """
        if not complex_header_config.structure:
            self.logger.warning("复杂表头配置为空，使用默认结构")
            return self._build_default_complex_header(config, border_mode, border_details)

        table_model = TableModel()

        # 解析复杂表头结构
        max_cols = 0  # 跟踪最大列数
        for row_index, row_structure in enumerate(complex_header_config.structure):
            row_model = RowModel(row_index=row_index)

            col_index = 0  # 跟踪当前列位置
            for cell_config in row_structure:
                cell_id = f"cell-{row_index}-{col_index}"

                # 从配置中提取单元格属性
                content = cell_config.get('content', f'Header {row_index}-{col_index}')
                row_span = cell_config.get('row_span', 1)
                col_span = cell_config.get('col_span', 1)

                cell_model = CellModel(
                    cell_id=cell_id,
                    row_index=row_index,
                    col_index=col_index,
                    row_span=row_span,
                    col_span=col_span,
                    content=content,
                    is_header=True  # 复杂表头中的所有单元格都是表头
                )

                row_model.cells.append(cell_model)

                # 更新列索引，考虑col_span
                col_index += col_span

            # 更新最大列数
            max_cols = max(max_cols, col_index)
            table_model.header_rows.append(row_model)  # V3.1: 添加到header_rows而不是rows

        # 添加数据行
        header_rows = len(complex_header_config.structure)
        data_rows = self._get_randomized_value(config.body_rows)  # 直接使用body_rows

        for row_index in range(header_rows, header_rows + data_rows):
            row_model = RowModel(row_index=row_index)

            for col_index in range(max_cols):
                cell_id = f"cell-{row_index}-{col_index}"

                cell_model = CellModel(
                    cell_id=cell_id,
                    row_index=row_index,
                    col_index=col_index,
                    row_span=1,
                    col_span=1,
                    content="",  # 内容将由ContentBuilder填充
                    is_header=False  # 数据行不是表头
                )

                row_model.cells.append(cell_model)

            table_model.body_rows.append(row_model)  # V3.1: 添加到body_rows而不是rows

        # 使用新的边框分配逻辑
        self._assign_borders_to_cells(table_model, border_mode, border_details)

        # 保持兼容性：创建空的边框决策对象
        table_model.border_decisions = BorderDecisions()

        return table_model

    def _build_default_complex_header(self, config, border_mode: str = 'full', border_details: dict = None) -> TableModel:
        """
        构建默认的复杂表头

        Args:
            config: 结构配置
            border_mode: 边框模式
            border_details: 边框详细配置

        Returns:
            默认复杂表头的表格模型
        """
        table_model = TableModel()

        # 创建一个简单的两级表头示例
        # 第一行：跨列的主标题
        row1 = RowModel(row_index=0)
        row1.cells.append(CellModel(
            cell_id="cell-0-0",
            row_index=0,
            col_index=0,
            col_span=2,
            content="主标题A",
            is_header=True
        ))
        row1.cells.append(CellModel(
            cell_id="cell-0-1",
            row_index=0,
            col_index=2,
            col_span=2,
            content="主标题B",
            is_header=True
        ))

        # 第二行：子标题
        row2 = RowModel(row_index=1)
        for i in range(4):
            row2.cells.append(CellModel(
                cell_id=f"cell-1-{i}",
                row_index=1,
                col_index=i,
                content=f"子标题{i+1}",
                is_header=True
            ))

        table_model.header_rows.extend([row1, row2])  # V3.1: 添加到header_rows

        # 添加数据行
        data_rows = self._get_randomized_value(config.body_rows)  # 直接使用body_rows

        for row_index in range(2, 2 + data_rows):
            row_model = RowModel(row_index=row_index)

            for col_index in range(4):
                cell_id = f"cell-{row_index}-{col_index}"

                cell_model = CellModel(
                    cell_id=cell_id,
                    row_index=row_index,
                    col_index=col_index,
                    row_span=1,
                    col_span=1,
                    content="",  # 内容将由ContentBuilder填充
                    is_header=False  # 数据行不是表头
                )

                row_model.cells.append(cell_model)

            table_model.body_rows.append(row_model)  # V3.1: 添加到body_rows

        # 使用新的边框分配逻辑
        self._assign_borders_to_cells(table_model, border_mode, border_details)

        # 保持兼容性：创建空的边框决策对象
        table_model.border_decisions = BorderDecisions()

        return table_model


