# TableRender 后处理降质功能集成 - 渐进式开发计划

## 1. 项目概述

### 1.1 开发目标
将 `doc_degradation` 模块的8种降质效果集成到 `ImageAugmentor` 后处理模块中，通过配置文件控制各种降质效果的触发概率，保持现有架构完全兼容。

### 1.2 受影响的现有模块
- **核心模块**：`table_render/postprocessors/image_augmentor.py`
- **配置模块**：`table_render/config.py`
- **配置文件**：`configs/v4_postprocess_background_test.yaml`
- **依赖模块**：`third_parties/doc_degradation/`

### 1.3 代码目录结构
```
table_render/
├── postprocessors/
│   ├── image_augmentor.py          # 主要修改：集成降质功能
│   ├── degradation_processor.py   # 新增：降质处理器封装
│   └── base_augmentor.py          # 保持不变
├── config.py                      # 修改：扩展配置模型
└── ...
configs/
└── v4_postprocess_background_test.yaml  # 修改：新增降质配置
third_parties/
└── doc_degradation/               # 依赖：现有降质模块
```

## 2. 渐进式开发步骤

### 步骤1：配置模型扩展
**目标**：扩展配置系统以支持8种降质效果的概率配置

**修改文件**：
- `table_render/config.py`
- `configs/v4_postprocess_background_test.yaml`

**具体任务**：
1. 在 `PostprocessingConfig` 类中新增8个降质效果的配置字段
2. 在 `ResolvedPostprocessingParams` 类中新增对应的解析后参数
3. 在配置文件中新增8个降质配置项，默认概率0.2，包含中文注释
4. 扩展 `Resolver._resolve_postprocessing_params` 方法解析新配置

**验收标准**：
- 配置文件能正确加载，包含所有8个新配置项
- 程序能正常启动，现有功能不受影响
- 新配置项能正确解析到 `ResolvedPostprocessingParams`

### 步骤2：降质处理器封装
**目标**：创建独立的降质处理器模块，封装 `doc_degradation` 功能

**新增文件**：
- `table_render/postprocessors/degradation_processor.py`

**具体任务**：
1. 创建 `DegradationProcessor` 类，封装 `doc_degradation` 的调用逻辑
2. 实现8种降质效果的独立处理方法
3. 实现模糊组内互斥逻辑（高斯/运动/均值模糊三选一）
4. 实现错误处理和日志记录机制
5. 提供统一的处理接口 `apply_degradations(image, annotations, params)`

**验收标准**：
- 降质处理器能独立运行，不依赖 `ImageAugmentor`
- 8种降质效果能正确应用到测试图像
- 模糊组内互斥逻辑正确工作
- 错误处理机制正常，失败时能跳过并记录日志

### 步骤3：ImageAugmentor集成
**目标**：将降质处理器集成到现有的 `ImageAugmentor` 流程中

**修改文件**：
- `table_render/postprocessors/image_augmentor.py`

**具体任务**：
1. 在 `ImageAugmentor.__init__` 中初始化 `DegradationProcessor`
2. 在 `augment` 方法的最后阶段调用降质处理
3. 集成到现有的调试系统，保存最终处理结果
4. 确保降质处理在透视变换和背景合成之后执行
5. 保持现有的错误处理和日志记录机制

**验收标准**：
- 完整的后处理流程正常工作：透视变换 → 背景合成 → 降质处理
- 调试模式能正确输出最终结果
- 现有功能完全不受影响
- 降质效果能根据配置概率正确触发

### 步骤4：端到端测试验证
**目标**：验证完整的集成功能，确保所有需求得到满足

**测试文件**：
- 使用 `configs/v4_postprocess_background_test.yaml` 进行测试

**具体任务**：
1. 测试各种概率配置下的降质效果
2. 验证模糊组内互斥逻辑
3. 测试错误处理机制（模拟降质失败场景）
4. 验证调试模式输出
5. 性能测试，确保处理时间可接受

**验收标准**：
- 所有8种降质效果能正确应用
- 配置文件能正确控制效果触发概率
- 错误处理机制正常工作
- 调试输出包含降质处理结果
- 程序稳定运行，无崩溃或异常

## 3. 实现流程图

```mermaid
flowchart TD
    A[步骤1: 配置模型扩展] --> B[步骤2: 降质处理器封装]
    B --> C[步骤3: ImageAugmentor集成]
    C --> D[步骤4: 端到端测试验证]
    
    subgraph "步骤1详细流程"
        A1[扩展PostprocessingConfig] --> A2[扩展ResolvedPostprocessingParams]
        A2 --> A3[更新配置文件] --> A4[扩展Resolver解析逻辑]
    end
    
    subgraph "步骤2详细流程"
        B1[创建DegradationProcessor类] --> B2[实现8种降质方法]
        B2 --> B3[实现模糊组互斥逻辑] --> B4[实现错误处理]
    end
    
    subgraph "步骤3详细流程"
        C1[初始化DegradationProcessor] --> C2[集成到augment方法]
        C2 --> C3[集成调试系统] --> C4[确保执行顺序]
    end
    
    subgraph "步骤4详细流程"
        D1[概率配置测试] --> D2[互斥逻辑测试]
        D2 --> D3[错误处理测试] --> D4[性能测试]
    end
```

## 4. 关键技术要点

### 4.1 模块化设计
- **DegradationProcessor**：独立封装，便于测试和维护
- **配置扩展**：保持向后兼容，新增配置项有默认值
- **错误隔离**：单个降质效果失败不影响其他效果

### 4.2 集成策略
- **最小侵入**：复用现有架构，最小化对现有代码的修改
- **执行顺序**：降质处理在所有现有后处理之后执行
- **调试集成**：复用现有调试机制，不增加额外复杂度

### 4.3 质量保证
- **渐进验证**：每步完成后立即验证，确保程序可运行
- **功能隔离**：新功能不影响现有功能
- **错误处理**：完善的错误处理和日志记录

## 5. 风险控制

### 5.1 技术风险
- **依赖风险**：`doc_degradation` 模块稳定性
- **性能风险**：8种降质效果的处理时间
- **兼容风险**：新配置与现有配置的兼容性

### 5.2 缓解措施
- **独立封装**：通过 `DegradationProcessor` 隔离依赖
- **错误处理**：失败时跳过，不影响整体流程
- **渐进集成**：分步验证，确保每步稳定

---

**文档版本**：v1.0  
**创建日期**：2025-01-17  
**预计开发时间**：4-6小时（按步骤分配：1h + 2h + 1.5h + 1.5h）
