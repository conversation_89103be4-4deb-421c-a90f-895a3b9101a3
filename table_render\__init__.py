"""
TableRender: 高度可控的表格图像合成工具

V4.3版本：表格透明度融合
- 表格背景透明化，自然融入背景图像
- CSS阶段透明度控制，确保渲染一致性
- 智能文字可读性优化（颜色调整、阴影效果）
- 统一透明度配置，向后兼容

V4.0版本：鲁棒性与图像增强
- 内容溢出处理（截断/换行策略）
- 图像后处理（模糊、噪声、透视变换）
- 样式冲突检测与警告日志
- 性能优化（DOM查询优化）

V3.x版本特性：
- 全新的Resolver架构（泛化配置→具体元数据）
- 表头/主体结构分离（<thead>和<tbody>原生支持）
- 概率化配置系统（单配置文件多样化输出）
- 精确可复现性（100%像素级复现）

这是一个用于生成表格图像数据集的工具，支持精确的结构化标注。
"""

__version__ = "4.3.0"
__author__ = "TableRender Team"

# 导出主要组件
from .config import RenderConfig, ResolvedParams, PostprocessingConfig, TableBlendingConfig
from .resolver import Resolver
from .main_generator import MainGenerator

# V4.0新增：图像后处理模块
from .postprocessors import ImageAugmentor

# V4.3新增：透明度处理工具
from .utils.transparency_utils import TransparencyUtils
