# TableRender V4.3 透明度功能测试配置
# 测试表格背景透明化，使表格自然融入背景图像

output:
  output_dir: "./output/"

structure:
  body_rows: 4
  cols: 5
  header_rows: 1
  merge_probability: 0.2
  max_row_span: 2
  max_col_span: 2

content:
  source_type: "programmatic"
  programmatic_types: ["date", "currency", "percentage", "text"]

style:
  overflow_strategy: "wrap"
  
  common:
    font:
      font_dirs: "./assets/fonts/"
      default_family: "Arial"
      default_size: 14
      bold_probability: 0.1
      italic_probability: 0.1
      fallback_font: "Microsoft YaHei"
    
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: 8
    randomize_color_probability: 0.3
    merged_cell_center_probability: 0.5
    
    color_contrast:
      min_contrast_ratio: 4.5
      use_soft_colors_probability: 0.7
  
  inheritance:
    font_family_change_probability: 0.2
    font_size_change_probability: 0.3
    alignment_change_probability: 0.4
    padding_change_probability: 0.3
    text_color_change_probability: 0.3
    background_color_change_probability: 0.2
  
  border_mode:
    mode: "full"
  
  zebra_stripes: 0.3
  
  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# V4.3新增：表格透明度配置
postprocessing:
  # 表格融合配置
  table_blending:
    enable_transparency: true      # 启用表格透明度
    overall_transparency: 0.1      # 统一透明度级别（0.0-1.0）
  
  # 透视变换（可选）
  perspective:
    probability: 0.6
    max_offset_ratio: 0.03
  
  # 背景图合成配置
  background:
    background_dirs: ["./assets/backgrounds/"]
    background_dir_probabilities: [1.0]
    max_scale_factor: 2.0
    
    # 智能边距控制
    margin_control:
      range_list:
        - [40, 80]    # 中等边距
        - [80, 120]   # 宽松边距
      probability_list: [0.7, 0.3]
  
  # 可选的图像后处理（模糊、噪声）
  blur:
    probability: 0.0
    radius_range: [1.0, 2.0]
  
  noise:
    probability: 0.0
    intensity_range: [5, 10]

seed: 42
