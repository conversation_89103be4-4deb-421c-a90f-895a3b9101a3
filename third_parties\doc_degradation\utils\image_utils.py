# Original <EMAIL>

import cv2
import numpy as np
from pathlib import Path
from typing import Union, Optional

def load_image(path: Union[str, Path]) -> Optional[np.ndarray]:
    """Load image from path"""
    path = Path(path)
    if not path.exists():
        return None
    return cv2.imread(str(path))

def save_image(img: np.ndarray, path: Union[str, Path]) -> bool:
    """Save image to path"""
    path = Path(path)
    path.parent.mkdir(parents=True, exist_ok=True)
    return cv2.imwrite(str(path), img)


def ensure_rgb(image: np.ndarray) -> np.ndarray:
    """
    确保图像为RGB格式, 当前传入BGR格式，返回RGB格式
    TODO: 支持BGR通道入参，BGR通道出参
    Args:
        image: 输入图像,可以是灰度图、BGR图或RGBA图
    Returns:
        RGB格式的图像
    """
    if image.ndim == 2:  
        return cv2.cvtColor(image, cv2.COLOR_GRAY2RGB)
        
    if image.shape[2] == 4:
        # 将透明通道混合到RGB上
        alpha = image[:, :, 3:] / 255.0
        # rgb = image[:, :, :3]
        rgb = cv2.cvtColor(image[:, :, :3], cv2.COLOR_BGR2RGB)
        return (rgb * alpha + 255 * (1 - alpha)).astype(np.uint8)
        
    if image.shape[2] == 3:  # BGR图
        return cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
    raise ValueError(f"Unsupported image format with shape {image.shape}")

def to_uint8(image: Union[np.ndarray, float]) -> np.ndarray:
    """
    将图像转换为uint8格式
    Args:
        image: 输入图像,可以是浮点型或整型数组
    Returns:
        uint8格式的图像
    """
    if isinstance(image, (int, float)):
        return np.uint8(np.clip(image, 0, 255))
        
    if image.dtype == np.uint8:
        return image
        
    if image.dtype in [np.float32, np.float64]:
        if image.max() <= 1.0:
            image = image * 255
            
    return np.uint8(np.clip(image, 0, 255))