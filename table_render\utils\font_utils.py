"""
字体管理工具

提供字体检测、选择和容错功能，支持多字体文件夹和默认字体回退。
"""

import logging
import os
from pathlib import Path
from typing import List, Optional, Set, Union
import unicodedata
import numpy as np


class FontManager:
    """
    字体管理器类
    
    负责扫描字体文件、检测字体支持和提供字体回退机制。
    """
    
    def __init__(self):
        """初始化字体管理器"""
        self.logger = logging.getLogger(__name__)
        self._font_cache = {}  # 字体文件缓存
        self._support_cache = {}  # 字体支持缓存
        
    def scan_font_directories(self, font_dirs: List[str]) -> List[str]:
        """
        扫描指定文件夹中的字体文件
        
        Args:
            font_dirs: 字体文件夹路径列表
            
        Returns:
            可用字体名称列表
        """
        available_fonts = []
        
        for font_dir in font_dirs:
            font_path = Path(font_dir)
            if not font_path.exists():
                self.logger.warning(f"字体目录不存在: {font_path}")
                continue
                
            self.logger.debug(f"扫描字体目录: {font_path}")
            
            # 扫描TTF和OTF字体文件
            for ext in ['*.ttf', '*.otf', '*.TTF', '*.OTF']:
                for font_file in font_path.glob(ext):
                    font_name = font_file.stem
                    if font_name not in available_fonts:
                        available_fonts.append(font_name)
                        # 缓存字体文件路径
                        self._font_cache[font_name] = str(font_file)
                        
        self.logger.info(f"发现 {len(available_fonts)} 个字体文件")
        return available_fonts
    
    def check_font_support(self, font_name: str, text_sample: str = "中文English123") -> bool:
        """
        检测字体是否支持给定字符集
        
        Args:
            font_name: 字体名称
            text_sample: 测试文本样本
            
        Returns:
            是否支持该字符集
        """
        # 使用缓存避免重复检测
        cache_key = f"{font_name}:{text_sample}"
        if cache_key in self._support_cache:
            return self._support_cache[cache_key]
        
        # 简化的字体支持检测：基于字体名称和字符类型
        supported = self._simple_font_support_check(font_name, text_sample)
        
        # 缓存结果
        self._support_cache[cache_key] = supported
        return supported
    
    def _simple_font_support_check(self, font_name: str, text_sample: str) -> bool:
        """
        简化的字体支持检测
        
        Args:
            font_name: 字体名称
            text_sample: 测试文本样本
            
        Returns:
            是否支持该字符集
        """
        # 检测文本中的字符类型
        has_chinese = any('\u4e00' <= char <= '\u9fff' for char in text_sample)
        has_english = any(char.isascii() and char.isalpha() for char in text_sample)
        
        font_lower = font_name.lower()
        
        # 中文字体通常支持中英文
        chinese_fonts = ['yahei', 'simhei', 'simsun', 'kaiti', 'fangsong', 'microsoft', '微软', '宋体', '黑体', '楷体']
        if any(cf in font_lower for cf in chinese_fonts):
            return True
            
        # 英文字体通常只支持英文
        if has_chinese and not any(cf in font_lower for cf in chinese_fonts):
            return False
            
        # 其他情况默认支持
        return True
    
    def get_fallback_font(self) -> str:
        """
        获取默认回退字体
        
        Returns:
            默认字体名称
        """
        return "Microsoft YaHei"
    
    def select_safe_font(self, font_candidates: List[str], text_characters: str = "中文English123") -> str:
        """
        选择安全的字体
        
        Args:
            font_candidates: 候选字体列表
            text_characters: 需要支持的字符
            
        Returns:
            选择的字体名称
        """
        # 首先尝试候选字体
        for font in font_candidates:
            if self.check_font_support(font, text_characters):
                self.logger.debug(f"选择字体: {font}")
                return font
        
        # 如果都不支持，返回回退字体
        fallback = self.get_fallback_font()
        self.logger.warning(f"所有候选字体都不支持，使用回退字体: {fallback}")
        return fallback
    
    def get_font_file_path(self, font_name: str) -> Optional[str]:
        """
        获取字体文件路径
        
        Args:
            font_name: 字体名称
            
        Returns:
            字体文件路径，如果不存在则返回None
        """
        return self._font_cache.get(font_name)
    
    def get_weighted_random_directory(self, font_dirs_config, probabilities=None, random_state=None) -> str:
        """
        V3.3新增：根据概率权重选择字体目录

        Args:
            font_dirs_config: 字体目录配置，可以是字符串、列表或带概率的配置列表
            probabilities: 概率列表，与font_dirs_config对应
            random_state: 随机状态，如果为None则使用numpy.random

        Returns:
            选择的字体目录路径
        """
        if random_state is None:
            random_state = np.random

        # 处理不同类型的配置
        if isinstance(font_dirs_config, str):
            # 单个目录
            return font_dirs_config
        elif isinstance(font_dirs_config, list):
            if len(font_dirs_config) == 0:
                return "./assets/fonts/"  # 默认目录

            # 检查第一个元素的类型
            first_item = font_dirs_config[0]
            if isinstance(first_item, str):
                # 字符串列表，检查是否有概率配置
                if probabilities and len(probabilities) == len(font_dirs_config):
                    # 使用概率选择
                    total_prob = sum(probabilities)
                    if total_prob > 0:
                        normalized_probs = [p / total_prob for p in probabilities]
                        return random_state.choice(font_dirs_config, p=normalized_probs)
                    else:
                        return random_state.choice(font_dirs_config)
                else:
                    # 等概率选择
                    return random_state.choice(font_dirs_config)
            elif hasattr(first_item, 'path') and hasattr(first_item, 'probability'):
                # 带概率的配置列表（FontDirectoryConfig对象）
                paths = [item.path for item in font_dirs_config]
                probabilities = [item.probability for item in font_dirs_config]

                # 归一化概率
                total_prob = sum(probabilities)
                if total_prob > 0:
                    normalized_probs = [p / total_prob for p in probabilities]
                    return random_state.choice(paths, p=normalized_probs)
                else:
                    # 如果所有概率都是0，等概率选择
                    return random_state.choice(paths)
            elif isinstance(first_item, dict):
                # 字典列表，直接处理概率选择
                try:
                    paths = []
                    probabilities = []

                    for item in font_dirs_config:
                        if isinstance(item, dict):
                            path = item.get('path', str(item))
                            prob = item.get('probability', 1.0)
                            paths.append(path)
                            probabilities.append(prob)
                        else:
                            paths.append(str(item))
                            probabilities.append(1.0)

                    # 归一化概率
                    total_prob = sum(probabilities)
                    if total_prob > 0:
                        normalized_probs = [p / total_prob for p in probabilities]
                        return random_state.choice(paths, p=normalized_probs)
                    else:
                        # 如果所有概率都是0，等概率选择
                        return random_state.choice(paths)

                except Exception as e:
                    self.logger.warning(f"无法解析字体目录配置字典: {e}")
                    # 回退到字符串处理
                    str_dirs = [str(item.get('path', str(item))) if isinstance(item, dict) else str(item)
                               for item in font_dirs_config]
                    return random_state.choice(str_dirs)
            else:
                # 混合列表，转换为字符串后等概率选择
                str_dirs = [str(item) for item in font_dirs_config]
                return random_state.choice(str_dirs)
        else:
            # 其他类型，转换为字符串
            return str(font_dirs_config)

    def clear_cache(self):
        """清空缓存"""
        self._font_cache.clear()
        self._support_cache.clear()
        self.logger.debug("字体缓存已清空")
