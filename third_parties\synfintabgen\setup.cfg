[metadata]
name = synfintabgen
version = file: VERSION.txt
author = <PERSON>
author_email = <EMAIL>
description = A package for generating synthetic financial tables.
license = MIT
license_file = LICENSE.txt
long_description = file: README.md
long_description_content_type = text/markdown
project_urls =
    Homepage = https://ethanbradley.co.uk/research/synfintabs
    Issues = https://github.com/ethanbradley/synfintabgen/issues
    Source = https://github.com/ethanbradley/synfintabgen

[options]
python_requires = >=3.10
packages = find:
include_package_data = True
zip_safe = false
install_requires =
    easyocr >= 1.7.1
    htmltree >= 0.7.6
    nanoid >= 2.0.0
    nltk >= 3.9.1
    selenium >= 4.19.0
    tqdm >= 4.66.2

[options.entry_points]
console_scripts =
    synfintabgen = synfintabgen.execute_from_command_line:execute_from_command_line
