# TableRender V3: 高级样式与真实感 - 开发计划

**目标:** 引入全面的样式控制功能和对复杂表头的支持，使用户能够生成外观高度多样化和视觉上逼真的表格。

---

## 1. 项目结构与受影响模块

本次开发将主要集中在对现有核心模块的扩展，不会新增文件，但会对以下文件进行较大修改：

```
TableRender/
└── table_render/           # 核心Python包
    ├── config.py           # (受影响) 扩展StyleConfig和StructureConfig以支持新功能
    ├── builders/           # (受影响)
    │   ├── style_builder.py    # (主要修改) 实现所有新样式逻辑
    │   └── structure_builder.py# (主要修改) 实现多级表头逻辑
    └── renderers/          # (受影响)
        └── html_renderer.py  # (轻微修改) 确保能为新样式和结构添加正确的CSS类
```

## 2. 渐进式开发与集成步骤

我们将版本3的开发分为7个独立、可验证的小步骤。

### 步骤 1: 扩展配置模型 (`config.py`)

**目标:** 为所有V3新功能在Pydantic配置模型中奠定基础。这是后续所有步骤的前置条件。

**操作:**
1.  **修改 `table_render/config.py`**:
    -   **`StyleConfig`**:
        -   新增 `RowColumnSizeConfig` 用于定义行高和列宽的随机范围。
        -   新增 `BorderConfig` 来分别控制单元格的四条边框。
        -   新增 `HierarchicalStyleConfig` 用于支持分层样式（全局、列、行、单元格）。
        -   将原有的扁平化 `StyleConfig` 重构，使其包含上述新的子模型。
    -   **`StructureConfig`**:
        -   新增 `ComplexHeaderConfig` 用于定义多级表头的结构。

**伪代码 (`config.py`):**
```python
# table_render/config.py

class BorderConfig(BaseModel):
    top: bool = True
    right: bool = True
    bottom: bool = True
    left: bool = True

class RowColumnSizeConfig(BaseModel):
    row_height: Union[int, RangeConfig] = 'auto'
    col_width: Union[int, RangeConfig] = 'auto'

# ... 其他类似的新子模型 ...

class StyleConfig(BaseModel):
    # 将原有字段重构到新的子模型中
    font: FontConfig
    border: BorderConfig
    sizing: RowColumnSizeConfig
    zebra_stripes: bool = False
    # ... 其他样式配置

class StructureConfig(BaseModel):
    # ... 保留原有字段
    complex_header: Optional[List[List[Dict]]] = None # 定义复杂表头结构
```

**验证:**
-   修改 `configs/default.yaml` 以匹配新的 `StyleConfig` 结构。
-   运行程序 `python -m table_render.main --config configs/default.yaml --num-samples 1`。
-   程序应能成功运行并生成一个与V2版本看起来完全相同的表格。这证明了配置模型的向后兼容性。

### 步骤 2: 实现自定义字体加载与应用

**目标:** 让表格可以使用用户在 `assets/fonts` 目录中提供的任意字体。

**操作:**
1.  **修改 `table_render/builders/style_builder.py`**:
    -   在 `build` 方法中，读取 `FontConfig`。
    -   扫描 `assets/fonts` 目录，为每个字体文件生成一个 `@font-face` CSS规则。
    -   根据配置中的字体族、大小、字重和字形概率，生成应用这些字体的CSS类。

**伪代码 (`style_builder.py`):**
```python
# table_render/builders/style_builder.py

def build(self, style_config: StyleConfig) -> str:
    css_rules = []
    # 1. 生成 @font-face 规则
    font_dir = Path(style_config.font.font_dir)
    for font_file in font_dir.glob('*.ttf'):
        font_name = font_file.stem
        rule = f"""@font-face {{
            font-family: '{font_name}';
            src: url('{font_file.absolute().as_uri()}');
        }}"""
        css_rules.append(rule)

    # 2. 生成应用字体的规则
    # ... (例如 .cell { font-family: 'MyCustomFont'; font-size: 16px; } ...)

    return "\n".join(css_rules)
```

**验证:**
-   在 `assets/fonts/` 放入一个自定义字体 (例如 `Alibaba-PuHuiTi-Regular.ttf`)。
-   创建一个新的配置文件，在 `style.font` 中指定使用该字体。
-   运行生成器，目视检查输出的表格图像是否正确应用了新字体。

### 步骤 3: 实现颜色、对齐与内边距样式

**目标:** 为表格添加基础但效果显著的视觉多样性。

**操作:**
1.  **修改 `table_render/builders/style_builder.py`**:
    -   扩展 `build` 方法，使其能够处理 `StyleConfig` 中的文本颜色、背景色、水平/垂直对齐、内边距配置。
    -   为每个单元格随机选择（如果提供的是列表）或直接应用这些样式属性，并生成对应的CSS。

**验证:**
-   创建一个配置文件，其中包含多种颜色、对齐方式和内边距范围。
-   运行生成器，检查输出的表格是否展现了多样化的颜色、对齐和内边距效果。

### 步骤 4: 实现高级边框控制与斑马纹

**目标:** 实现对表格线条的精细控制和经典的斑马条纹（交替行背景色）效果。

**操作:**
1.  **修改 `table_render/builders/style_builder.py`**:
    -   根据 `BorderConfig`，为单元格生成 `border-top`, `border-right` 等独立的CSS规则。
    -   如果 `zebra_stripes` 为 `True`，则添加一个 `tr:nth-child(even)` 的CSS选择器来设置交替行的背景色。

**验证:**
-   创建配置文件，将 `zebra_stripes` 设为 `true`，并设置 `border` 只开启 `top` 和 `bottom`。
-   运行生成器，检查输出的表格是否带有斑马条纹，并且单元格只有水平方向的边框。

### 步骤 5: 实现分层样式应用逻辑

**目标:** 实现一个强大的样式覆盖系统（全局 -> 列 -> 行 -> 单元格），允许用户进行精细的样式定制。

**操作:**
1.  **修改 `table_render/config.py`**：确保 `HierarchicalStyleConfig` 结构清晰，可以定义针对列、行和单元格的样式覆盖。
2.  **修改 `table_render/builders/style_builder.py`**:
    -   重构 `build` 方法，使其按“全局 -> 列 -> 行 -> 单元格”的顺序生成一系列CSS规则。
    -   为每个需要特殊样式的列、行或单元格生成一个唯一的CSS类 (例如 `.col-1`, `.row-3`, `.cell-3-1`)。
3.  **修改 `table_render/renderers/html_renderer.py`**:
    -   在将 `TableModel` 转换为HTML时，为对应的 `<td>`, `<tr>` 元素添加由 `StyleBuilder` 定义的CSS类。

**验证:**
-   创建一个配置文件，定义一个全局字体大小，但为第2列指定一个更大的字体大小。
-   运行生成器，检查输出表格的第2列字体是否明显大于其他列。

### 步骤 6: 实现多级（复杂）表头生成

**目标:** 这是V3的另一个核心功能，使工具能生成结构更复杂的表格。

**操作:**
1.  **修改 `table_render/builders/structure_builder.py`**:
    -   在 `build` 方法中添加新的逻辑分支：如果 `config.structure.complex_header` 被定义，则忽略 `rows`, `cols`, `header_rows` 等简单配置。
    -   解析 `complex_header` 结构，创建 `CellModel` 实例，并正确设置其 `row_span` 和 `col_span` 属性。
    -   确保所有表头单元格的 `is_header` 属性被设为 `True`。
2.  **修改 `table_render/renderers/html_renderer.py`**:
    -   验证 `render` 方法能够正确地将带有 `rowspan` 和 `colspan` 的 `CellModel` 转换为正确的 `<th>` 标签。

**验证:**
-   创建一个配置文件，定义一个两级的复杂表头结构。
-   运行生成器，目视检查输出的表格是否具有正确合并的、多层次的表头。同时检查 `annotations.json` 文件，确认对应单元格的 `row_span` 和 `col_span` 值是否正确。

### 步骤 7: 实现随机行高与列宽

**目标:** 通过引入尺寸变化，进一步提升表格的真实感和视觉多样性。

**操作:**
1.  **修改 `table_render/builders/style_builder.py`**:
    -   根据 `RowColumnSizeConfig` 中的配置，为每一行和每一列生成独立的CSS类来控制其 `height` 和 `width`。
2.  **修改 `table_render/renderers/html_renderer.py`**:
    -   在生成HTML时，为 `<tr>` 和 `<col>` (或 `<td>`) 元素应用这些尺寸类。

**验证:**
-   创建一个配置文件，为行高和列宽指定一个随机范围。
-   运行生成器，检查输出的表格是否具有不同高度的行和不同宽度的列。
