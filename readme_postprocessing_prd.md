# TableRender 后处理降质功能集成 PRD

## 1. 项目概述

### 1.1 项目背景
TableRender 是一个表格图像生成系统，当前具有完整的后处理管道（模糊、噪声、透视变换、背景合成）。为了增强表格识别模型的鲁棒性，需要将 `doc_degradation` 模块的降质功能集成到现有的后处理流程中。

### 1.2 项目目标
- 将 `doc_degradation` 的8种降质效果集成到 `ImageAugmentor` 后处理模块
- 通过配置文件控制各种降质效果的触发概率
- 保持现有架构和配置的完全兼容性
- 提供数据增强能力，提升模型对低质量文档的识别能力

## 2. 功能需求

### 2.1 核心功能
**集成8种降质效果：**
1. **模糊效果** (`degradation_blur`) - 高斯/运动/均值模糊随机选择
2. **噪声效果** (`degradation_noise`) - 高斯噪声
3. **全局褪色** (`degradation_fade_global`) - 全局文字淡化
4. **局部褪色** (`degradation_fade_local`) - 局部区域文字淡化
5. **不均匀光照** (`degradation_uneven_lighting`) - 模拟光照不均
6. **JPEG压缩** (`degradation_jpeg`) - 压缩降质
7. **亮度/对比度调整** (`degradation_darker_brighter`) - 明暗调整
8. **伽马校正** (`degradation_gamma_correction`) - 伽马值调整

### 2.2 执行逻辑
- **独立概率判断**：8种效果各自独立判断触发概率，可同时触发多种
- **模糊组内互斥**：模糊效果内部的高斯/运动/均值模糊三选一
- **处理顺序**：按配置文件中的顺序依次执行
- **错误处理**：某个效果失败时跳过并记录错误，继续处理其他效果

### 2.3 集成架构
- **集成层级**：在 `ImageAugmentor` 层面集成
- **处理流程**：现有后处理（透视变换、背景合成）→ 降质效果处理
- **配置结构**：在 `postprocessing` 配置段下新增降质配置

## 3. 配置需求

### 3.1 配置文件结构
在 `v4_postprocess_background_test.yaml` 中新增以下配置：

```yaml
postprocessing:
  # 现有配置保持不变
  perspective: {...}
  background: {...}
  blur: {probability: 0}
  noise: {probability: 0}
  
  # 新增降质效果配置
  degradation_blur: 
    probability: 0.2              # 模糊效果（高斯/运动/均值模糊随机选择）
  degradation_noise: 
    probability: 0.2              # 高斯噪声
  degradation_fade_global: 
    probability: 0.2              # 全局褪色
  degradation_fade_local: 
    probability: 0.2              # 局部褪色
  degradation_uneven_lighting: 
    probability: 0.2              # 不均匀光照
  degradation_jpeg: 
    probability: 0.2              # JPEG压缩
  degradation_darker_brighter: 
    probability: 0.2              # 亮度/对比度调整
  degradation_gamma_correction: 
    probability: 0.2              # 伽马校正
```

### 3.2 配置说明
- **默认概率值**：所有降质效果默认概率为 0.2
- **概率范围**：0-1 之间的浮点数
- **配置注释**：每个配置项包含中文注释说明效果作用
- **向后兼容**：现有配置保持完全不变

## 4. 技术需求

### 4.1 架构设计
- **集成位置**：`table_render/postprocessors/image_augmentor.py`
- **依赖模块**：`third_parties/doc_degradation/`
- **配置解析**：扩展现有的 `ResolvedPostprocessingParams`
- **处理顺序**：在 `augment()` 方法的最后阶段执行

### 4.2 互斥逻辑实现
基于 `doc_degradation` 中的现有实现：
- **模糊组互斥**：使用 `random.choices(['gaussian', 'motion', 'average'], weights=[1, 2, 1])`
- **其他效果独立**：各自独立执行，无互斥关系

### 4.3 错误处理
- **失败跳过**：单个效果失败时跳过，继续处理后续效果
- **错误日志**：记录失败时的具体错误信息
- **日志级别**：使用 ERROR 级别记录失败信息

### 4.4 调试支持
- **集成现有调试系统**：复用 `ImageAugmentor` 的调试机制
- **最终结果输出**：在现有调试输出中体现降质处理结果
- **不保存中间图像**：不为每种降质效果单独保存调试图像

## 5. 兼容性需求

### 5.1 向后兼容
- **现有配置不变**：所有现有配置项保持原有格式和默认值
- **现有功能不变**：原有后处理功能的行为保持不变
- **配置文件兼容**：只使用包含新配置项的配置文件，无需考虑旧配置文件兼容

### 5.2 性能要求
- **无性能优化要求**：不需要特别的性能优化
- **处理效率**：降质效果作为数据增强，处理时间可接受

## 6. 验收标准

### 6.1 功能验收
- [ ] 8种降质效果能够正确集成到后处理流程
- [ ] 配置文件能够正确控制各效果的触发概率
- [ ] 模糊组内的互斥逻辑正确实现
- [ ] 错误处理机制正常工作
- [ ] 调试模式正常输出最终结果

### 6.2 配置验收
- [ ] `v4_postprocess_background_test.yaml` 包含所有8个新配置项
- [ ] 每个配置项都有正确的中文注释
- [ ] 默认概率值为 0.2
- [ ] 现有配置保持不变

### 6.3 质量验收
- [ ] 代码集成不破坏现有架构
- [ ] 日志输出清晰明确
- [ ] 错误情况下系统稳定运行
- [ ] 降质效果符合预期

## 7. 实施计划

### 7.1 开发阶段
1. **配置扩展**：扩展配置解析逻辑，支持8个新配置项
2. **模块集成**：在 `ImageAugmentor` 中集成 `doc_degradation` 功能
3. **互斥逻辑**：实现模糊组内的互斥选择逻辑
4. **错误处理**：实现错误跳过和日志记录机制
5. **调试集成**：集成到现有调试系统

### 7.2 测试阶段
1. **单元测试**：测试各降质效果的独立功能
2. **集成测试**：测试完整的后处理流程
3. **配置测试**：测试各种概率配置的效果
4. **错误测试**：测试错误处理机制

### 7.3 验收阶段
1. **功能验收**：验证所有功能需求
2. **性能验收**：确认处理性能可接受
3. **文档验收**：确认配置文件和注释完整

## 8. 风险与依赖

### 8.1 技术风险
- **模块依赖**：依赖 `doc_degradation` 模块的稳定性
- **配置复杂度**：新增配置项可能增加配置复杂度

### 8.2 依赖关系
- **核心依赖**：`third_parties/doc_degradation/` 模块
- **配置依赖**：`configs/v4_postprocess_background_test.yaml`
- **代码依赖**：`table_render/postprocessors/image_augmentor.py`

---

**文档版本**：v1.0  
**创建日期**：2025-01-17  
**最后更新**：2025-01-17
