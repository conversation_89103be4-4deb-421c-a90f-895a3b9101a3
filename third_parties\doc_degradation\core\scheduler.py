# Original <EMAIL>

from typing import Dict, Any, Optional
from ..configs.config import DegradationConfig
from .strategy import DegradationType, DegradationStrategy


class StrategyScheduler:
    """策略调度器"""
    def __init__(self, config_path: Optional[str] = None):
        """
        初始化策略调度器
        Args:
            config_path: 配置文件路径，如果提供则从文件加载配置
        """
        self.strategies: Dict[DegradationType, DegradationStrategy] = {}
        self.presets = {}
        
        if config_path:
            self.load_config(config_path)  # TODO
        else:
            self._init_default_strategies()  # 当前只支持默认策略

    def load_config(self, config_path: str):
        """从YAML配置文件加载策略"""
        raise NotImplementedError()
    
    def _init_default_strategies(self):
        """初始化默认降质策略, 包含调用函数的参数, 具体调用在pipe的_init_processors中, 基础实现在effects中"""
        config = DegradationConfig()
        
        # TODO 自动化,需考虑NotImplementError case 或去除dict
        strategy_configs = {
            DegradationType.BLUR: {
                'gaussian_blur_kernel_range': config.gaussian_blur_kernel_range,
                'gaussian_blur_sigma_range': config.gaussian_blur_sigma_range,
                'motion_blur_kernel_range': config.motion_blur_kernel_range,
                'average_blur_kernel_range': config.average_blur_kernel_range,
                'blur_prob': config.blur_prob
            },
            DegradationType.NOISE: {
                'gaussian_noise_std_range': config.gaussian_noise_std_range,
                'poisson_noise_scale_range': config.poisson_noise_scale_range,
                'salt_pepper_noise_prob_range': config.salt_pepper_noise_prob_range,
                'noise_prob': config.noise_prob
            },
            DegradationType.JPEG: {
                'jpeg_quality_range': config.jpeg_quality_range,
                'jpeg_prob': config.jpeg_prob
            },
            DegradationType.FADE_GLOBAL: {
                'fade_global_kernel_range': config.fade_global_kernel_range,
                'fade_global_prob': config.fade_global_prob
            },
            DegradationType.FADE_LOCAL: {
                'fade_local_brightness_range': config.fade_local_brightness_range,
                'fade_local_saturation_range': config.fade_local_saturation_range,
                'fade_local_num_blocks_range': config.fade_local_num_blocks_range,
                'fade_local_prob': config.fade_local_prob
            },
            DegradationType.LINE_BROKEN: {
                'line_broken_alpha_range': config.line_broken_alpha_range,
                'line_broken_beta_range': config.line_broken_beta_range,
                'line_broken_prob': config.line_broken_prob
            },
            DegradationType.UNEVEN_LIGHTING: {
                'uneven_lighting_range': config.uneven_lighting_range,
                'uneven_lighting_prob': config.uneven_lighting_prob
            },
            DegradationType.ARTISTIC_INK: {
                'artistic_ink_num_clusters_range': config.artistic_ink_num_clusters_range,
                'artistic_ink_drop_density_range': config.artistic_ink_drop_density_range,
                'artistic_ink_cluster_size_range': config.artistic_ink_cluster_size_range,
                'artistic_ink_intensity_range': config.artistic_ink_intensity_range,
                'artistic_ink_noise_scale': config.artistic_ink_noise_scale,
                'artistic_ink_blur_sigma_range': config.artistic_ink_blur_sigma_range,
                'artistic_ink_drops_prob': config.artistic_ink_drops_prob
            },
            DegradationType.SINC: {
                'sinc_kernel_size_range': config.sinc_kernel_size_range,
                'sinc_cutoff_range': config.sinc_cutoff_range,
                'sinc_prob': config.sinc_prob
            },
            
            DegradationType.USM_SHARPEN:{
                'usm_amount_range': config.usm_amount_range,
                'usm_sharpen_prob': config.usm_sharpen_prob
            },

            DegradationType.RANDOM_RESIZE: config,
            DegradationType.ESRGAN_BLUR: config,
            
            DegradationType.DARKER_BRIGHTER: {
                'darker_bright_brightness_range': config.darker_bright_brightness_range,
                'darker_bright_contrast_range': config.darker_bright_contrast_range,
                'darker_brighter_prob': config.darker_bright_prob
            },
            DegradationType.GAMMA_CORRECTION: {
                'gamma_correction_gamma_range': config.gamma_range,
                'gamma_correction_prob': config.gamma_prob
            },
            DegradationType.IRIS_BLUR_LARGE: config,
        }
        
        for deg_type, config_dict in strategy_configs.items():
            self.register_strategy(DegradationStrategy(
                degradation_type=deg_type,
                enabled=True,
                config=config_dict
            ))

    def _get_strategy_config(self, deg_type: DegradationType, base_params: dict) -> Dict[str, Any]:
        """从基础参数中获取特定策略的配置"""
        raise NotImplementedError()

    def save_config(self, config_path: str):
        """保存当前配置到YAML文件"""
        raise NotImplementedError()

    def register_strategy(self, strategy: DegradationStrategy):
        """注册降质策略"""
        self.strategies[strategy.degradation_type] = strategy
        
    def get_strategy(self, degradation_type: DegradationType) -> Optional[DegradationStrategy]:
        """获取指定策略"""
        return self.strategies.get(degradation_type)
            
    def get_enabled_strategies(self) -> Dict[DegradationType, DegradationStrategy]:
        """获取所有已启用的策略"""
        return {deg_type: strategy 
                for deg_type, strategy in self.strategies.items() 
                if strategy.enabled}
        
    def set_strategy_enabled(self, degradation_type: DegradationType, enabled: bool):
        """设置策略的启用状态"""
        if strategy := self.get_strategy(degradation_type):
            strategy.enabled = enabled

    def is_strategy_enabled(self, degradation_type: DegradationType) -> bool:
        """检查策略是否启用"""
        if strategy := self.get_strategy(degradation_type):
            return strategy.enabled
        return False