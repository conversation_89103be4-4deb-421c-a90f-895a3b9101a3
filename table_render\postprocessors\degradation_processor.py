"""
降质处理器

V4.5新增：封装doc_degradation模块功能，提供8种降质效果的统一处理接口。
"""

import logging
import numpy as np
from PIL import Image
from typing import Optional, Dict, Any, List
import io

# 导入doc_degradation模块
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..', 'third_parties'))

from doc_degradation.core.degradation_pipe import DegradationPipe
from doc_degradation.core.scheduler import StrategyScheduler
from doc_degradation.core.strategy import DegradationType, DegradationStrategy
from doc_degradation.configs.config import DegradationConfig

from ..config import ResolvedPostprocessingParams


class DegradationProcessor:
    """
    降质处理器
    
    封装doc_degradation模块功能，提供8种降质效果的统一处理接口。
    支持模糊组内互斥逻辑和错误处理机制。
    """
    
    def __init__(self, seed: int):
        """
        初始化降质处理器

        Args:
            seed: 随机种子，用于确保可复现性
        """
        self.logger = logging.getLogger(__name__)
        self.logger.info("DegradationProcessor.__init__ 开始执行")
        self.seed = seed
        
        # 初始化降质管道
        self.logger.info("开始初始化降质管道")
        self._init_degradation_pipe()
        self.logger.info("降质管道初始化完成")
        
        # 定义8种目标降质效果的映射关系
        self.effect_mapping = {
            'blur': DegradationType.BLUR,
            'noise': DegradationType.NOISE,
            'fade_global': DegradationType.FADE_GLOBAL,
            'fade_local': DegradationType.FADE_LOCAL,
            'uneven_lighting': DegradationType.UNEVEN_LIGHTING,
            'jpeg': DegradationType.JPEG,
            'darker_brighter': DegradationType.DARKER_BRIGHTER,
            'gamma_correction': DegradationType.GAMMA_CORRECTION
        }
    
    def _init_degradation_pipe(self):
        """初始化降质处理管道"""
        try:
            self.logger.info("开始创建 StrategyScheduler")
            # 创建策略调度器（使用默认策略）
            scheduler = StrategyScheduler()  # 不传递参数，使用默认策略
            self.logger.info(f"StrategyScheduler 创建成功: {scheduler}")

            # 检查策略是否正确初始化
            if hasattr(scheduler, 'strategies') and scheduler.strategies:
                self.logger.info(f"strategies 初始化成功: {list(scheduler.strategies.keys())}")
            else:
                self.logger.error("strategies 初始化失败或为空")

            self.logger.info("开始创建 DegradationPipe")
            # 创建降质管道
            self.degradation_pipe = DegradationPipe(scheduler)
            self.logger.info(f"DegradationPipe 创建成功: {self.degradation_pipe}")

            # 检查 processors 是否正确初始化
            if hasattr(self.degradation_pipe, 'processors') and self.degradation_pipe.processors:
                self.logger.info(f"processors 初始化成功: {list(self.degradation_pipe.processors.keys())}")
            else:
                self.logger.error("processors 初始化失败或为空")
                self.degradation_pipe = None

        except Exception as e:
            import traceback
            self.logger.error(f"降质处理管道初始化失败: {e}")
            self.logger.error(f"详细错误: {traceback.format_exc()}")
            self.degradation_pipe = None
    
    def apply_degradations(self, 
                          image: Image.Image, 
                          annotations: Optional[Dict[str, Any]], 
                          params: ResolvedPostprocessingParams) -> tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        应用降质效果
        
        Args:
            image: PIL图像对象
            annotations: 标注数据
            params: 解析后的后处理参数
            
        Returns:
            (处理后的PIL图像对象, 处理后的标注数据)
        """
        if self.degradation_pipe is None:
            self.logger.error("降质处理管道未初始化，跳过降质处理")
            return image, annotations
        
        # 获取需要应用的降质效果
        enabled_effects = self._get_enabled_effects(params)
        
        if not enabled_effects:
            self.logger.debug("没有启用的降质效果")
            return image, annotations
        
        self.logger.info(f"开始应用降质效果: {enabled_effects}")
        
        # 转换PIL图像为numpy数组
        image_array = np.array(image)
        
        # 按顺序应用降质效果
        processed_image_array = image_array
        for effect_name in enabled_effects:
            try:
                processed_image_array = self._apply_single_effect(
                    processed_image_array, effect_name
                )
                self.logger.debug(f"降质效果 {effect_name} 应用成功")
                
            except Exception as e:
                self.logger.error(f"降质效果 {effect_name} 应用失败: {e}")
                # 继续处理其他效果，不中断整个流程
                continue
        
        # 转换回PIL图像
        processed_image = Image.fromarray(processed_image_array)
        
        self.logger.info("降质效果应用完成")
        
        # 注意：降质效果通常不会改变图像尺寸，所以标注坐标保持不变
        return processed_image, annotations
    
    def _get_enabled_effects(self, params: ResolvedPostprocessingParams) -> List[str]:
        """
        获取启用的降质效果列表
        
        Args:
            params: 解析后的后处理参数
            
        Returns:
            启用的降质效果名称列表
        """
        enabled_effects = []
        
        # 按照PRD中定义的顺序检查各种降质效果
        effect_checks = [
            ('blur', params.apply_degradation_blur),
            ('noise', params.apply_degradation_noise),
            ('fade_global', params.apply_degradation_fade_global),
            ('fade_local', params.apply_degradation_fade_local),
            ('uneven_lighting', params.apply_degradation_uneven_lighting),
            ('jpeg', params.apply_degradation_jpeg),
            ('darker_brighter', params.apply_degradation_darker_brighter),
            ('gamma_correction', params.apply_degradation_gamma_correction)
        ]
        
        for effect_name, is_enabled in effect_checks:
            if is_enabled:
                enabled_effects.append(effect_name)
        
        return enabled_effects
    
    def _apply_single_effect(self, image_array: np.ndarray, effect_name: str) -> np.ndarray:
        """
        应用单个降质效果

        Args:
            image_array: 图像numpy数组
            effect_name: 降质效果名称

        Returns:
            处理后的图像numpy数组
        """
        if effect_name not in self.effect_mapping:
            raise ValueError(f"不支持的降质效果: {effect_name}")

        degradation_type = self.effect_mapping[effect_name]

        # 调试：检查映射和查找过程
        self.logger.info(f"effect_name: {effect_name}")
        self.logger.info(f"degradation_type: {degradation_type}")
        self.logger.info(f"degradation_type type: {type(degradation_type)}")
        self.logger.info(f"processors keys: {list(self.degradation_pipe.processors.keys())}")
        self.logger.info(f"processors keys types: {[type(k) for k in self.degradation_pipe.processors.keys()]}")

        # 直接调用降质管道的处理器
        processor = self.degradation_pipe.processors.get(degradation_type)
        self.logger.info(f"processor result: {processor}")

        if processor is None:
            raise ValueError(f"降质效果 {effect_name} 的处理器未找到")

        # 获取对应的策略配置
        strategy = self.degradation_pipe.scheduler.get_strategy(degradation_type)
        if strategy is None:
            raise ValueError(f"降质效果 {effect_name} 的策略未找到")

        # 直接调用处理器
        if degradation_type in {
            DegradationType.BLUR,
            DegradationType.SINC,
            DegradationType.ESRGAN_BLUR,
            DegradationType.IRIS_BLUR_LARGE,
            DegradationType.RANDOM_RESIZE,
            DegradationType.JPEG
        }:
            # 需要choose_flag参数的处理器
            processed_image = processor(image_array, strategy.config, choose_flag=None)
        else:
            # 不需要choose_flag参数的处理器
            processed_image = processor(image_array, strategy.config)

        return processed_image
