"""
标注转换器

将原始的浏览器标注数据转换为符合PRD规范的标注格式。
"""

import logging
from typing import Dict, List, Any

from ..models import TableModel


class AnnotationConverter:
    """
    标注转换器类
    
    负责将浏览器获取的原始标注数据转换为最终的JSON格式。
    """
    
    def __init__(self):
        """初始化标注转换器"""
        self.logger = logging.getLogger(__name__)
    
    def convert_to_final_format(
        self,
        raw_annotations: Dict[str, Any],
        table_model: TableModel,
        image_filename: str
    ) -> Dict[str, Any]:
        """
        将原始标注转换为最终格式
        
        Args:
            raw_annotations: 从浏览器获取的原始标注数据
            table_model: 表格模型，用于获取逻辑位置信息
            image_filename: 图像文件名
            
        Returns:
            符合PRD规范的标注字典
        """
        self.logger.debug("开始转换标注格式")
        
        # 创建单元格ID到模型的映射
        cell_id_to_model = self._create_cell_mapping(table_model)
        
        # 构建最终标注格式
        final_annotations = {
            "table_ind": image_filename.replace('.png', ''),
            "image_path": image_filename,
            "type": 2,  # 表格类型，固定为2
            "cells": []
        }
        
        # 转换每个单元格的标注
        for i, cell_data in enumerate(raw_annotations.get('cells', [])):
            cell_id = cell_data['id']
            cell_model = cell_id_to_model.get(cell_id)

            if cell_model is None:
                self.logger.warning(f"未找到单元格模型: {cell_id}")
                continue

            # 转换bbox格式：从[x_min, y_min, x_max, y_max]到四个角点
            bbox_rect = cell_data['bbox']
            self.logger.debug(f"转换单元格{i} ({cell_id}) bbox: {bbox_rect}")
            bbox_corners = self._convert_bbox_to_corners(bbox_rect)
            self.logger.debug(f"转换后的四角点: {bbox_corners}")
            
            # 构建单元格标注
            cell_annotation = {
                "cell_ind": i,
                "header": cell_model.is_header,
                "content": self._create_content_annotation(cell_data['content']),
                "bbox": bbox_corners,
                "border": {
                    "style": {
                        "top": cell_model.border_style.top,
                        "right": cell_model.border_style.right,
                        "bottom": cell_model.border_style.bottom,
                        "left": cell_model.border_style.left
                    }
                },
                "lloc": {
                    "start_row": cell_model.row_index,
                    "end_row": cell_model.row_index + cell_model.row_span - 1,
                    "start_col": cell_model.col_index,
                    "end_col": cell_model.col_index + cell_model.col_span - 1
                }
            }
            
            final_annotations["cells"].append(cell_annotation)
        
        self.logger.debug(f"标注转换完成，共{len(final_annotations['cells'])}个单元格")
        return final_annotations
    
    def _create_cell_mapping(self, table_model: TableModel) -> Dict[str, Any]:
        """
        创建单元格ID到模型的映射
        
        Args:
            table_model: 表格模型
            
        Returns:
            单元格ID到CellModel的映射字典
        """
        mapping = {}
        for row in table_model.rows:
            for cell in row.cells:
                mapping[cell.cell_id] = cell
        return mapping
    
    def _convert_bbox_to_corners(self, bbox_rect: List[float]) -> Dict[str, List[float]]:
        """
        将矩形bbox转换为四个角点格式
        
        Args:
            bbox_rect: [x_min, y_min, x_max, y_max]格式的边界框
            
        Returns:
            包含p1-p4四个角点的字典
        """
        x_min, y_min, x_max, y_max = bbox_rect
        return {
            "p1": [x_min, y_min],      # 左上角
            "p2": [x_max, y_min],      # 右上角
            "p3": [x_max, y_max],      # 右下角
            "p4": [x_min, y_max]       # 左下角
        }
    
    def _create_content_annotation(self, content_text: str) -> List[Dict[str, Any]]:
        """
        创建内容标注
        
        Args:
            content_text: 单元格的文本内容
            
        Returns:
            内容标注列表（V1版本简化处理）
        """
        if not content_text.strip():
            return []
        
        # V1版本简化处理：将整个内容作为一个文本块
        # 在实际应用中，这里应该进行更精细的文本分割和定位
        return [
            {
                "bbox": {
                    "p1": [0, 0],
                    "p2": [0, 0], 
                    "p3": [0, 0],
                    "p4": [0, 0]
                },
                "direction": 0,
                "text": content_text,
                "score": 1.0
            }
        ]
