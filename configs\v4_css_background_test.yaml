# TableRender V4.0 CSS背景图渲染测试配置
# 使用CSS直接渲染背景图和透视变换，避免坐标变换误差

output:
  output_dir: "./output/"

structure:
  body_rows: 3
  cols: 4
  header_rows: 1
  merge_probability: 0.1
  max_row_span: 2
  max_col_span: 2

content:
  source_type: "programmatic"
  programmatic_types: ["date", "currency", "percentage", "text"]

style:
  overflow_strategy: "wrap"
  
  common:
    font:
      font_dirs: "./assets/fonts/"
      default_family: "Arial"
      default_size: 14
      bold_probability: 0.1
      italic_probability: 0.1
      fallback_font: "Microsoft YaHei"
    
    horizontal_align: ["left", "center", "right"]
    vertical_align: ["top", "middle", "bottom"]
    padding: 8
    randomize_color_probability: 0.3
    merged_cell_center_probability: 0.5
    
    color_contrast:
      min_contrast_ratio: 4.5
      use_soft_colors_probability: 0.7
  
  inheritance:
    font_family_change_probability: 0.2
    font_size_change_probability: 0.3
    alignment_change_probability: 0.4
    padding_change_probability: 0.3
    text_color_change_probability: 0.3
    background_color_change_probability: 0.2
  
  border_mode:
    mode: "full"
  
  zebra_stripes: 0.3
  
  sizing:
    default_row_height: "auto"
    default_col_width: "auto"

# V4.0新增：CSS背景图渲染模式
postprocessing:
  # 透视变换（V4.5修改：统一使用OpenCV实现，确保坐标变换精确性）
  perspective:
    probability: 0.8
    max_offset_ratio: 0.03
  
  # 背景图合成配置
  background:
    background_dirs: ["/aipdf-mlp/jiacheng/code/text_render/example_data/bg/bg_resource/paper/"]
    background_dir_probabilities: [1.0]
    max_scale_factor: 2.0

    # V4.2新增：概率化边距控制（使用v3.4统一格式）
    margin_control:
      # 边距范围列表：定义不同的边距选项
      range_list:
        - [60, 200]    # 紧凑边距：适合密集布局
#        - [20, 40]    # 较紧凑边距：平衡紧凑性和可读性
#        - [40, 80]    # 中等边距：标准文档样式
#        - [60, 120]   # 宽松边距：强调表格重要性
#        - [100, 200]  # 很宽松边距：海报或展示用途

      # 对应的概率分布：偏向中等边距，保持多样性
      probability_list: [1.0]
#      probability_list: [0.1, 0.2, 0.3, 0.25, 0.15]
  
  # 可选的图像后处理（模糊、噪声）
  blur:
    probability: 0.0
    radius_range: [1.0, 2.0]
  
  noise:
    probability: 0.0              # V4.5修改：避免与degradation_noise重复
    intensity_range: [5, 10]

  # V4.5新增：降质效果配置（CSS模式）
  # 处理顺序：CSS渲染 → 透视变换 → 背景合成 → 降质效果
  degradation_blur:
    probability: 0              # 模糊效果（高斯/运动/均值模糊随机选择）
  degradation_noise:
    probability: 0              # 高斯噪声
  degradation_fade_global:
    probability: 0              # 全局褪色
  degradation_fade_local:
    probability: 0                # 局部褪色
  degradation_uneven_lighting:
    probability: 1             # 不均匀光照
  degradation_jpeg:
    probability: 0              # JPEG压缩
  degradation_darker_brighter:
    probability: 0              # 亮度/对比度调整
  degradation_gamma_correction:
    probability: 0              # 伽马校正

seed: 42
