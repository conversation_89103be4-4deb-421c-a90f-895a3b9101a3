#!/usr/bin/env python3
"""
透视变换效果对比脚本

测试不同库和方法实现的透视变换效果，包括：
1. OpenCV透视变换
2. PIL/Pillow变换
3. scikit-image变换
4. Wand (ImageMagick) 变换
5. 自定义数学实现
6. CSS模拟（通过浏览器渲染）

用法：
python tests/perspective_transform_comparison.py --input <image_path> --output_dir <output_dir>
"""

import os
import sys
import argparse
import numpy as np
from pathlib import Path
from typing import Tuple, List, Optional
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class PerspectiveTransformComparison:
    """透视变换对比测试类"""
    
    def __init__(self, input_image_path: str, output_dir: str):
        """
        初始化对比测试
        
        Args:
            input_image_path: 输入图像路径
            output_dir: 输出目录
        """
        self.input_path = Path(input_image_path)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 透视变换参数
        self.transform_configs = [
            {"name": "light", "offset_ratio": 0.01, "description": "轻微透视变换"},
            {"name": "medium", "offset_ratio": 0.03, "description": "中等透视变换"},
            {"name": "strong", "offset_ratio": 0.05, "description": "强烈透视变换"},
        ]
        
        logger.info(f"输入图像: {self.input_path}")
        logger.info(f"输出目录: {self.output_dir}")
    
    def load_image(self):
        """加载输入图像"""
        try:
            from PIL import Image
            self.pil_image = Image.open(self.input_path).convert('RGB')
            self.image_size = self.pil_image.size
            logger.info(f"图像尺寸: {self.image_size}")
            return True
        except Exception as e:
            logger.error(f"加载图像失败: {e}")
            return False
    
    def generate_transform_points(self, width: int, height: int, offset_ratio: float) -> Tuple[np.ndarray, np.ndarray]:
        """
        生成透视变换的源点和目标点
        
        Args:
            width: 图像宽度
            height: 图像高度
            offset_ratio: 偏移比例
            
        Returns:
            (源点数组, 目标点数组)
        """
        # 计算最大偏移量
        max_offset = min(width, height) * offset_ratio
        
        # 源点（图像四个角）
        src_points = np.float32([
            [0, 0],                 # 左上
            [width - 1, 0],         # 右上
            [0, height - 1],        # 左下
            [width - 1, height - 1] # 右下
        ])
        
        # 目标点（添加随机偏移）
        np.random.seed(42)  # 固定种子确保可重现
        dst_points = np.float32([
            [
                np.random.uniform(-max_offset, max_offset),
                np.random.uniform(-max_offset, max_offset)
            ],  # 左上
            [
                width - 1 + np.random.uniform(-max_offset, max_offset),
                np.random.uniform(-max_offset, max_offset)
            ],  # 右上
            [
                np.random.uniform(-max_offset, max_offset),
                height - 1 + np.random.uniform(-max_offset, max_offset)
            ],  # 左下
            [
                width - 1 + np.random.uniform(-max_offset, max_offset),
                height - 1 + np.random.uniform(-max_offset, max_offset)
            ]   # 右下
        ])
        
        return src_points, dst_points
    
    def opencv_transform(self, config: dict) -> bool:
        """OpenCV透视变换"""
        try:
            import cv2
            from PIL import Image
            
            # 转换为OpenCV格式
            img_cv = cv2.cvtColor(np.array(self.pil_image), cv2.COLOR_RGB2BGR)
            h, w = img_cv.shape[:2]
            
            # 生成变换点
            src_points, dst_points = self.generate_transform_points(w, h, config["offset_ratio"])
            
            # 计算透视变换矩阵
            matrix = cv2.getPerspectiveTransform(src_points, dst_points)
            
            # 应用透视变换
            warped = cv2.warpPerspective(img_cv, matrix, (w, h))
            
            # 转换回PIL格式并保存
            warped_rgb = cv2.cvtColor(warped, cv2.COLOR_BGR2RGB)
            result_image = Image.fromarray(warped_rgb)
            
            output_path = self.output_dir / f"opencv_{config['name']}.png"
            result_image.save(output_path)
            
            logger.info(f"OpenCV {config['name']} 变换完成: {output_path}")
            return True
            
        except ImportError:
            logger.warning("OpenCV未安装，跳过OpenCV变换")
            return False
        except Exception as e:
            logger.error(f"OpenCV {config['name']} 变换失败: {e}")
            return False
    
    def pillow_transform(self, config: dict) -> bool:
        """PIL/Pillow透视变换"""
        try:
            from PIL import Image
            
            w, h = self.image_size
            
            # 生成变换点
            src_points, dst_points = self.generate_transform_points(w, h, config["offset_ratio"])
            
            # PIL需要8个系数的变换矩阵
            # 使用最小二乘法计算变换系数
            coeffs = self._calculate_perspective_coefficients(src_points, dst_points)
            
            # 应用透视变换
            result_image = self.pil_image.transform(
                (w, h),
                Image.PERSPECTIVE,
                coeffs,
                Image.BICUBIC
            )
            
            output_path = self.output_dir / f"pillow_{config['name']}.png"
            result_image.save(output_path)
            
            logger.info(f"Pillow {config['name']} 变换完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Pillow {config['name']} 变换失败: {e}")
            return False
    
    def _calculate_perspective_coefficients(self, src_points: np.ndarray, dst_points: np.ndarray) -> List[float]:
        """计算PIL透视变换系数"""
        # 构建线性方程组 Ax = b
        A = []
        b = []
        
        for i in range(4):
            x, y = src_points[i]
            u, v = dst_points[i]
            
            A.append([x, y, 1, 0, 0, 0, -u*x, -u*y])
            A.append([0, 0, 0, x, y, 1, -v*x, -v*y])
            b.extend([u, v])
        
        A = np.array(A)
        b = np.array(b)
        
        # 求解线性方程组
        coeffs = np.linalg.solve(A, b)
        return coeffs.tolist()
    
    def skimage_transform(self, config: dict) -> bool:
        """scikit-image透视变换"""
        try:
            from skimage import transform
            from PIL import Image
            import numpy as np
            
            w, h = self.image_size
            
            # 生成变换点
            src_points, dst_points = self.generate_transform_points(w, h, config["offset_ratio"])
            
            # 转换为numpy数组
            img_array = np.array(self.pil_image)
            
            # 创建投影变换
            tform = transform.ProjectiveTransform()
            tform.estimate(src_points, dst_points)
            
            # 应用变换
            warped = transform.warp(img_array, tform.inverse, output_shape=(h, w))
            
            # 转换回PIL格式
            warped_uint8 = (warped * 255).astype(np.uint8)
            result_image = Image.fromarray(warped_uint8)
            
            output_path = self.output_dir / f"skimage_{config['name']}.png"
            result_image.save(output_path)
            
            logger.info(f"scikit-image {config['name']} 变换完成: {output_path}")
            return True
            
        except ImportError:
            logger.warning("scikit-image未安装，跳过scikit-image变换")
            return False
        except Exception as e:
            logger.error(f"scikit-image {config['name']} 变换失败: {e}")
            return False

    def wand_transform(self, config: dict) -> bool:
        """Wand (ImageMagick) 透视变换"""
        try:
            from wand.image import Image as WandImage
            from PIL import Image
            import io

            w, h = self.image_size

            # 生成变换点
            src_points, dst_points = self.generate_transform_points(w, h, config["offset_ratio"])

            # 将PIL图像转换为字节流
            img_bytes = io.BytesIO()
            self.pil_image.save(img_bytes, format='PNG')
            img_bytes.seek(0)

            # 使用Wand进行透视变换
            with WandImage(blob=img_bytes.getvalue()) as img:
                # 构建变换参数
                # ImageMagick的distort需要源点和目标点对
                distort_args = []
                for i in range(4):
                    distort_args.extend([src_points[i][0], src_points[i][1],
                                       dst_points[i][0], dst_points[i][1]])

                # 应用透视变换
                img.distort('perspective', distort_args)

                # 转换回PIL格式
                img.format = 'png'
                result_bytes = io.BytesIO(img.make_blob())
                result_image = Image.open(result_bytes)

            output_path = self.output_dir / f"wand_{config['name']}.png"
            result_image.save(output_path)

            logger.info(f"Wand {config['name']} 变换完成: {output_path}")
            return True

        except ImportError:
            logger.warning("Wand未安装，跳过Wand变换")
            return False
        except Exception as e:
            logger.error(f"Wand {config['name']} 变换失败: {e}")
            return False

    def css_browser_transform(self, config: dict) -> bool:
        """CSS浏览器透视变换"""
        try:
            from playwright.async_api import async_playwright
            import asyncio
            import base64
            import io
            from PIL import Image

            async def render_css_transform():
                async with async_playwright() as p:
                    browser = await p.chromium.launch()
                    page = await browser.new_page()

                    # 将图像转换为base64
                    img_bytes = io.BytesIO()
                    self.pil_image.save(img_bytes, format='PNG')
                    img_base64 = base64.b64encode(img_bytes.getvalue()).decode()

                    # 计算CSS变换参数
                    offset_ratio = config["offset_ratio"]
                    rotate_x = offset_ratio * 200  # 映射到旋转角度
                    rotate_y = offset_ratio * 100
                    perspective_value = 1000

                    # 生成HTML内容
                    html_content = f"""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <style>
                            body {{
                                margin: 0;
                                padding: 50px;
                                background: white;
                            }}
                            .transform-container {{
                                display: inline-block;
                                transform: perspective({perspective_value}px) rotateX({rotate_x}deg) rotateY({rotate_y}deg);
                                transform-origin: center center;
                                transform-style: preserve-3d;
                                backface-visibility: hidden;
                                -webkit-font-smoothing: antialiased;
                                -moz-osx-font-smoothing: grayscale;
                            }}
                            img {{
                                display: block;
                                max-width: none;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class="transform-container">
                            <img src="data:image/png;base64,{img_base64}" />
                        </div>
                    </body>
                    </html>
                    """

                    # 设置页面内容
                    await page.set_content(html_content)

                    # 等待渲染完成
                    await page.wait_for_load_state('networkidle')

                    # 截图
                    screenshot_bytes = await page.screenshot(type='png', scale='device')

                    await browser.close()
                    return screenshot_bytes

            # 运行异步函数
            screenshot_bytes = asyncio.run(render_css_transform())

            # 保存结果
            result_image = Image.open(io.BytesIO(screenshot_bytes))
            output_path = self.output_dir / f"css_{config['name']}.png"
            result_image.save(output_path)

            logger.info(f"CSS {config['name']} 变换完成: {output_path}")
            return True

        except ImportError:
            logger.warning("Playwright未安装，跳过CSS变换")
            return False
        except Exception as e:
            logger.error(f"CSS {config['name']} 变换失败: {e}")
            return False

    def custom_math_transform(self, config: dict) -> bool:
        """自定义数学透视变换实现"""
        try:
            from PIL import Image
            import numpy as np

            w, h = self.image_size

            # 生成变换点
            src_points, dst_points = self.generate_transform_points(w, h, config["offset_ratio"])

            # 计算透视变换矩阵（3x3齐次坐标）
            matrix = self._compute_perspective_matrix(src_points, dst_points)

            # 应用变换
            img_array = np.array(self.pil_image)
            warped_array = self._apply_perspective_transform(img_array, matrix)

            # 转换回PIL格式
            result_image = Image.fromarray(warped_array.astype(np.uint8))

            output_path = self.output_dir / f"custom_{config['name']}.png"
            result_image.save(output_path)

            logger.info(f"自定义数学 {config['name']} 变换完成: {output_path}")
            return True

        except Exception as e:
            logger.error(f"自定义数学 {config['name']} 变换失败: {e}")
            return False

    def _compute_perspective_matrix(self, src_points: np.ndarray, dst_points: np.ndarray) -> np.ndarray:
        """计算透视变换矩阵"""
        # 构建线性方程组
        A = []
        b = []

        for i in range(4):
            x, y = src_points[i]
            u, v = dst_points[i]

            A.append([x, y, 1, 0, 0, 0, -u*x, -u*y])
            A.append([0, 0, 0, x, y, 1, -v*x, -v*y])
            b.extend([u, v])

        A = np.array(A, dtype=np.float64)
        b = np.array(b, dtype=np.float64)

        # 求解
        h = np.linalg.solve(A, b)

        # 重构3x3矩阵
        matrix = np.array([
            [h[0], h[1], h[2]],
            [h[3], h[4], h[5]],
            [h[6], h[7], 1.0]
        ])

        return matrix

    def _apply_perspective_transform(self, img_array: np.ndarray, matrix: np.ndarray) -> np.ndarray:
        """应用透视变换"""
        h, w, c = img_array.shape
        result = np.zeros_like(img_array)

        # 计算逆矩阵用于反向映射
        inv_matrix = np.linalg.inv(matrix)

        # 对每个像素进行变换
        for y in range(h):
            for x in range(w):
                # 齐次坐标
                src_coord = np.array([x, y, 1.0])

                # 应用逆变换
                dst_coord = inv_matrix @ src_coord

                # 归一化
                if dst_coord[2] != 0:
                    dst_x = dst_coord[0] / dst_coord[2]
                    dst_y = dst_coord[1] / dst_coord[2]

                    # 双线性插值
                    if 0 <= dst_x < w-1 and 0 <= dst_y < h-1:
                        result[y, x] = self._bilinear_interpolate(img_array, dst_x, dst_y)

        return result

    def _bilinear_interpolate(self, img: np.ndarray, x: float, y: float) -> np.ndarray:
        """双线性插值"""
        x1, y1 = int(x), int(y)
        x2, y2 = x1 + 1, y1 + 1

        # 权重
        wx = x - x1
        wy = y - y1

        # 插值
        result = (1-wx)*(1-wy)*img[y1, x1] + wx*(1-wy)*img[y1, x2] + \
                (1-wx)*wy*img[y2, x1] + wx*wy*img[y2, x2]

        return result

    def run_all_transforms(self) -> dict:
        """运行所有透视变换方法"""
        results = {}

        # 定义所有变换方法
        transform_methods = [
            ("OpenCV", self.opencv_transform),
            ("Pillow", self.pillow_transform),
            ("scikit-image", self.skimage_transform),
            ("Wand", self.wand_transform),
            ("CSS浏览器", self.css_browser_transform),
            ("自定义数学", self.custom_math_transform),
        ]

        logger.info("开始透视变换对比测试...")

        for method_name, method_func in transform_methods:
            logger.info(f"\n=== 测试 {method_name} ===")
            method_results = {}

            for config in self.transform_configs:
                logger.info(f"应用 {config['description']} (offset_ratio={config['offset_ratio']})")
                success = method_func(config)
                method_results[config['name']] = success

                if success:
                    logger.info(f"✓ {method_name} {config['name']} 成功")
                else:
                    logger.warning(f"✗ {method_name} {config['name']} 失败")

            results[method_name] = method_results

        return results

    def generate_comparison_html(self, results: dict):
        """生成对比结果的HTML页面"""
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>透视变换效果对比</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .comparison-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; }
                .method-section { border: 1px solid #ddd; padding: 15px; border-radius: 8px; }
                .method-title { font-size: 18px; font-weight: bold; margin-bottom: 10px; color: #333; }
                .transform-row { display: flex; gap: 10px; margin-bottom: 15px; align-items: center; }
                .transform-image { max-width: 200px; max-height: 150px; border: 1px solid #ccc; }
                .transform-label { font-weight: bold; min-width: 80px; }
                .original-section { text-align: center; margin-bottom: 30px; }
                .original-image { max-width: 400px; border: 2px solid #333; }
                .status-success { color: green; }
                .status-failed { color: red; }
            </style>
        </head>
        <body>
            <h1>透视变换效果对比</h1>

            <div class="original-section">
                <h2>原始图像</h2>
                <img src="original.png" alt="原始图像" class="original-image">
            </div>

            <div class="comparison-grid">
        """

        # 保存原始图像
        original_path = self.output_dir / "original.png"
        self.pil_image.save(original_path)

        # 为每个方法生成HTML部分
        for method_name, method_results in results.items():
            html_content += f"""
                <div class="method-section">
                    <div class="method-title">{method_name}</div>
            """

            for config in self.transform_configs:
                config_name = config['name']
                success = method_results.get(config_name, False)

                if success:
                    # 根据方法名生成文件名
                    method_prefix = {
                        "OpenCV": "opencv",
                        "Pillow": "pillow",
                        "scikit-image": "skimage",
                        "Wand": "wand",
                        "CSS浏览器": "css",
                        "自定义数学": "custom"
                    }.get(method_name, "unknown")

                    image_filename = f"{method_prefix}_{config_name}.png"
                    status_class = "status-success"
                    status_text = "成功"
                else:
                    image_filename = ""
                    status_class = "status-failed"
                    status_text = "失败"

                html_content += f"""
                    <div class="transform-row">
                        <span class="transform-label">{config['description']}:</span>
                """

                if success:
                    html_content += f'<img src="{image_filename}" alt="{config_name}" class="transform-image">'
                else:
                    html_content += f'<span class="{status_class}">{status_text}</span>'

                html_content += "</div>"

            html_content += "</div>"

        html_content += """
            </div>

            <div style="margin-top: 30px; padding: 15px; background-color: #f5f5f5; border-radius: 8px;">
                <h3>说明</h3>
                <ul>
                    <li><strong>轻微透视变换</strong>: offset_ratio = 0.01，适合文档扫描效果</li>
                    <li><strong>中等透视变换</strong>: offset_ratio = 0.03，平衡效果和质量</li>
                    <li><strong>强烈透视变换</strong>: offset_ratio = 0.05，明显的3D效果</li>
                </ul>
                <p>对比重点：边缘平滑度、锯齿程度、整体视觉质量</p>
            </div>
        </body>
        </html>
        """

        # 保存HTML文件
        html_path = self.output_dir / "comparison.html"
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"对比结果HTML已生成: {html_path}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="透视变换效果对比测试")
    parser.add_argument("--input", "-i", required=True, help="输入图像路径")
    parser.add_argument("--output_dir", "-o", default="./perspective_comparison_output", help="输出目录")

    args = parser.parse_args()

    # 检查输入文件
    if not os.path.exists(args.input):
        logger.error(f"输入文件不存在: {args.input}")
        sys.exit(1)

    # 创建对比测试实例
    comparison = PerspectiveTransformComparison(args.input, args.output_dir)

    # 加载图像
    if not comparison.load_image():
        logger.error("加载图像失败")
        sys.exit(1)

    # 运行所有变换
    results = comparison.run_all_transforms()

    # 生成对比HTML
    comparison.generate_comparison_html(results)

    # 输出总结
    logger.info("\n=== 测试总结 ===")
    for method_name, method_results in results.items():
        success_count = sum(1 for success in method_results.values() if success)
        total_count = len(method_results)
        logger.info(f"{method_name}: {success_count}/{total_count} 成功")

    logger.info(f"\n所有结果已保存到: {args.output_dir}")
    logger.info(f"打开 {args.output_dir}/comparison.html 查看对比结果")


if __name__ == "__main__":
    main()
