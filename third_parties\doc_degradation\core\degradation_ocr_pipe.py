# Original <EMAIL>

import copy
import random
import cv2
import numpy as np
from enum import Enum
from typing import List, Optional
from .effects import ImageEffects
from .scheduler import StrategyScheduler
from ..configs.config import DegradationConfig
from ..utils.image_utils import ensure_rgb, to_uint8
from .strategy import DegradationType, DegradationStrategy

class DegradationOCRPipe:
    """降质处理管道"""
    def __init__(self, scheduler: StrategyScheduler):
        """
        初始化降质处理管道
        Args:
            scheduler: 策略调度器实例
        """
        self.scheduler = scheduler
        self.effects = ImageEffects()
        self._init_processors()

    def _init_processors(self):
        """初始化各种降质处理器"""
        self.processors = {
            DegradationType.BLUR: self._apply_blur,
            DegradationType.NOISE: self._apply_noise,
            DegradationType.JPEG: self._apply_jpeg_compression,
            DegradationType.FADE_GLOBAL: self._apply_global_fade,
            DegradationType.FADE_LOCAL: self._apply_local_fade,
            DegradationType.LINE_BROKEN: self._apply_line_broken,
            DegradationType.UNEVEN_LIGHTING: self._apply_uneven_lighting,
            DegradationType.ARTISTIC_INK: self._apply_artistic_ink,
            # DegradationType.GEOMETRIC: self._apply_geometric,
            DegradationType.SINC: self._apply_sinc,
            DegradationType.USM_SHARPEN: self._apply_usm_sharpen,
            DegradationType.RANDOM_RESIZE: self._apply_random_resize,
            DegradationType.ESRGAN_BLUR: self._apply_esrgan_blur,
            # DegradationType.BLUR_MIX: self._apply_blur_mix,
            # DegradationType.NOISE_MIX: self._apply_noise_mix,
            DegradationType.DARKER_BRIGHTER: self._apply_darker_brighter,
            DegradationType.GAMMA_CORRECTION: self._apply_gamma_correction,
            DegradationType.IRIS_BLUR_LARGE: self._apply_iris_blur_large,
        }

    def process(self,
                image: np.ndarray,
                preset_name: Optional[str] = None,
                custom_strategies: Optional[List[DegradationType]] = None,
                probabilities: Optional[List[float]] = None,
                seed: Optional[int] = None,
                choose_flag: Optional[dict] = None) -> np.ndarray:
        """
        执行降质处理
        Args:
            image: 输入图像
            preset_name: 预设配置名称
            custom_strategies: 自定义策略列表
            probabilities: 每个策略对应的使用概率(0-1之间)，长度需要与策略列表相同
            seed: 随机种子
        Returns:
            处理后的图像
        """
        if seed is not None:
            random.seed(seed)
            np.random.seed(seed)
        # image = ensure_rgb(image)

        if custom_strategies and isinstance(custom_strategies[0], (list, tuple)):
            custom_strategy_configs = [config for _, config in custom_strategies]
            custom_strategies = [strategy for strategy, _ in custom_strategies]
        else:
            custom_strategy_configs = None
        strategies_to_apply = self._get_strategies_to_apply(preset_name, custom_strategies)

        if probabilities is not None:
            if len(probabilities) != len(strategies_to_apply):
                raise ValueError("概率列表长度必须与策略列表长度相同")
            if not all(0 <= p <= 1 for p in probabilities):
                raise ValueError("所有概率值必须在0-1之间")
        else:
            # 如果未提供概率，则所有策略都100%执行
            probabilities = [1.0] * len(strategies_to_apply)

        degraded_image = image.copy()
        for i, (strategy, prob) in enumerate(zip(strategies_to_apply, probabilities)):
            # 根据概率决定是否执行该策略
            if random.random() < prob:
                processor = self.processors.get(strategy.degradation_type)
                if processor:
                    update_config = custom_strategy_configs[i] if custom_strategy_configs else {}
                    real_confg = copy.deepcopy(strategy.config)
                    if update_config:
                        if isinstance(real_confg, dict):
                            real_confg.update(update_config)
                        else:
                            for key, value in update_config.items():
                                setattr(real_confg, key, value)
                    # degraded_image = processor(degraded_image, real_confg)
                    # 判断processor是否属于指定的类型
                    if strategy.degradation_type in {
                        DegradationType.BLUR,
                        DegradationType.SINC,
                        DegradationType.ESRGAN_BLUR,
                        DegradationType.IRIS_BLUR_LARGE,
                        DegradationType.RANDOM_RESIZE,
                        DegradationType.JPEG
                    }:
                        # 将choose_flag传入processor
                        degraded_image = processor(degraded_image, real_confg, choose_flag=choose_flag)
                    else:
                        # 不传入choose_flag
                        degraded_image = processor(degraded_image, real_confg)

        return to_uint8(degraded_image)

    def _get_strategies_to_apply(
        self,
        preset_name: Optional[str],
        custom_strategies: Optional[List[DegradationType]]
    ) -> List[DegradationStrategy]:
        """确定要应用的策略列表,从测试入口函数设置中获取"""
        if preset_name:
            raise NotImplementedError('Preset degradation is not supported yet.')
        elif custom_strategies:
            return [
                self.scheduler.get_strategy(deg_type) for deg_type in custom_strategies
                if deg_type in self.scheduler.get_enabled_strategies()
            ]
        else:
            return list(self.scheduler.get_enabled_strategies().values())

    def _apply_blur(self, image: np.ndarray, config: dict, choose_flag: Optional[dict]) -> np.ndarray:
        """应用模糊效果"""
        # TODO 依概率选模糊类型
        # blur_type = random.choice(['motion'])
        blur_type = random.choices(['gaussian', 'motion', 'average'], weights=[1, 2, 1], k=1)[0]

        if blur_type == 'gaussian':
            if random.random() < 0.9:
                kernel_size = random.choice([1, 3, 5])
            else:
                kernel_size = random.choice([7, 9])

            kernel_size = kernel_size if kernel_size % 2 == 1 else kernel_size + 1
            sigma = random.uniform(*config['gaussian_blur_sigma_range'])
            return self.effects.gaussian_blur(image, kernel_size, sigma)

        elif blur_type == 'motion':
            if random.random() < 0.9:
                kernel_size = random.choice([1, 3, 5])
            else:
                kernel_size = random.choice([7, 9])

            kernel_size = kernel_size if kernel_size % 2 == 1 else kernel_size + 1
            angle = random.uniform(0, 360)
            return self.effects.motion_blur(image, kernel_size, angle)

        else:  # average blur
            if random.random() < 0.9:
                kernel_size = random.choice([1, 3, 5])
            else:
                kernel_size = random.choice([7, 9])

            kernel_size = kernel_size if kernel_size % 2 == 1 else kernel_size + 1
            return self.effects.average_blur(image, kernel_size)

    def _apply_noise(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用噪声效果"""
        # TODO 依概率选噪声类型
        # noise_type = random.choice(['gaussian', 'poisson', 'salt_pepper'])
        noise_type = random.choice(['gaussian'])

        if noise_type == 'gaussian':
            std = random.uniform(*config['gaussian_noise_std_range'])
            return self.effects.add_gaussian_noise(image, std)

        elif noise_type == 'poisson':
            scale = random.uniform(*config['poisson_noise_scale_range'])
            return self.effects.add_poisson_noise(image, scale)

        else:  # salt_pepper
            prob = random.uniform(*config['salt_pepper_noise_prob_range'])
            return self.effects.add_salt_pepper_noise(image, prob)

    def _apply_jpeg_compression(self, image: np.ndarray, config: dict, choose_flag: Optional[dict]) -> np.ndarray:
        """应用JPEG压缩"""
        quality = random.randint(5, 15)

        num_compressions = 1
        return self.effects.jpeg_compression(image, quality, num_compressions)

    def _apply_global_fade(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用全局淡化效果"""
        alpha = random.randrange(*config.get('fade_global_kernel_range', [2, 3]))
        return self.effects.fade_global(image, alpha)

    def _apply_local_fade(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用局部淡化效果"""
        brightness_factor = random.uniform(*config.get('fade_local_brightness_range', [3, 8]))
        saturation_factor = random.uniform(*config.get('fade_local_saturation_range', [0.1, 0.3]))
        num_blocks = random.randint(*config.get('fade_local_num_blocks_range', [3, 6]))
        return self.effects.fade_local(image, brightness_factor, saturation_factor, num_blocks)

    def _apply_line_broken(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用文字断裂效果"""
        alpha = random.uniform(*config.get('line_broken_alpha_range', [2.0, 2.6]))
        beta = random.uniform(*config.get('line_broken_beta_range', [1.1, 1.5]))
        return self.effects.contrast_meet_line_broken(image, alpha, beta)

    def _apply_uneven_lighting(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用不均匀光照效果"""
        light_variation = random.uniform(*config.get('uneven_lighting_range', [0.1, 0.3]))
        return self.effects.uneven_lighting(image, light_variation)

    def _apply_artistic_ink(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用艺术点墨效果"""
        num_clusters = random.randint(*config.get('artistic_ink_num_clusters_range', [2, 4]))
        drop_density = random.uniform(*config.get('artistic_ink_drop_density_range', [0.01, 0.15]))
        cluster_size = random.randint(*config.get('artistic_ink_cluster_size_range', [10, 40]))
        intensity = random.uniform(*config.get('artistic_ink_intensity_range', [0.2, 0.8]))
        noise_scale = random.uniform(*config.get('artistic_ink_noise_scale_range', [0.01, 0.1]))
        blur = random.uniform(*config.get('artistic_ink_blur_sigma_range', [0.3, 1.0]))

        return self.effects.add_artistic_ink_drops(image, num_clusters, drop_density, cluster_size, intensity, noise_scale, blur)

    def _apply_geometric(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用几何变形效果"""
        perspective_scale = random.uniform(*config.get('perspective_scale_range', [0.01, 0.1]))
        return self.effects.geometric_distortion(image, perspective_scale)

    def _apply_sinc(self, image: np.ndarray, config: dict, choose_flag: Optional[dict]) -> np.ndarray:
        """应用Sinc滤波效果"""
        if random.random() < 0.9:
            kernel_size = random.choice([1, 3, 5])
        else:
            kernel_size = random.choice([7, 9])

        cutoff = random.uniform(*config.get('sinc_cutoff_range', [0.1, 0.5]))
        return self.effects.sinc_blur(image, kernel_size, cutoff)

    def _apply_usm_sharpen(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用USM锐化效果"""
        amount = random.uniform(*config.get('usm_amount_range', [0.5, 1.0]))
        return self.effects.usm_sharpen(image, amount)

    def _apply_random_resize(self, image: np.ndarray, config: DegradationConfig, choose_flag: Optional[dict]) -> np.ndarray:
        """应用随机缩放效果"""

        return self.effects.random_resize(image, config, choose_flag)

    def _apply_esrgan_blur(self, image: np.ndarray, config: DegradationConfig, choose_flag: Optional[dict]) -> np.ndarray:
        """应用ESRGAN模糊效果"""

        return self.effects.esrgan_blur(image, config, choose_flag)

    def _apply_blur_mix(self, image: np.ndarray, config: DegradationConfig) -> np.ndarray:
        """应用模糊混合效果"""
        return self.effects.basic_blur_mixes(image, config)

    def _apply_noise_mix(self, image: np.ndarray, config: DegradationConfig) -> np.ndarray:
        """应用噪声混合效果"""
        return self.effects.basic_noise_mixes(image, config)

    # GT 强化
    def _apply_darker_brighter(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用暗淡效果"""
        contrast = random.uniform(*config.get('darker_bright_contrast_range', [1.5, 2.0]))
        brightness = random.uniform(*config.get('darker_bright_brightness_range', [1.1, 1.3]))
        return self.effects.darker_bright(image, contrast, brightness)

    def _apply_gamma_correction(self, image: np.ndarray, config: dict) -> np.ndarray:
        """应用伽马校正效果"""
        gamma = random.uniform(*config.get('gamma_range', [1.8, 2.5]))
        return self.effects.gamma_correction(image, gamma)

    def _apply_iris_blur_large(self, image: np.ndarray, config: DegradationConfig, choose_flag: Optional[dict]) -> np.ndarray:
        max_size = config.iris_blur_max_size
        clear_ratio = random.uniform(*config.clear_ratio_range)
        # blur_ratio = random.uniform(*config.blur_ratio_range)
        rand_val = random.random()
        blur_param_1 = choose_flag.get("blur_param_1", 1)
        if blur_param_1 == 1:
            if rand_val < 0.3-0.28:
                blur_ratio = 0.002
            elif rand_val < 0.8+0.1:
                blur_ratio = 0.003
            else:
                blur_ratio = 0.004
        elif blur_param_1 == 2:
            if rand_val < 0.3-0.25:
                blur_ratio = 0.002
            else:
                blur_ratio = 0.003
        elif blur_param_1 == 4:
            if rand_val < 0.3-0.28:
                blur_ratio = random.uniform(0.007, 0.008)
            elif rand_val < 0.8+0.1:
                blur_ratio = random.uniform(0.008, 0.009)
            else:
                blur_ratio = random.uniform(0.008, 0.010)
        else:
            blur_ratio = 0.002+0.001

        gradient_ratio = random.uniform(*config.gradient_ratio_range)
        return self.effects.iris_blur_large(image, max_size, clear_ratio, blur_ratio, gradient_ratio)