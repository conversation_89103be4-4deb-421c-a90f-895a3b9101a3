import cv2
import math
import scipy
import random
import numpy as np
from scipy import special
from typing import <PERSON>ple, Optional
from scipy.ndimage import gaussian_filter
from scipy.stats import multivariate_normal
from ..configs.config import DegradationConfig
from PIL import Image, ImageEnhance, ImageDraw


def estimate_text_size(img: np.ndarray) -> float:
    """
    估计图像中文字的大小（平均连通域高度）。
    Args:
        img: 输入图像
    Returns:
        avg_text_size: 估计的平均文字大小
    """
    # 转灰度图并二值化
    gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
    _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

    # 进行连通域分析，统计文字块高度
    num_labels, labels, stats, _ = cv2.connectedComponentsWithStats(binary)
    text_heights = stats[1:, cv2.CC_STAT_HEIGHT]  # 跳过背景
    avg_text_size = np.mean(text_heights) if len(text_heights) > 0 else 0

    return avg_text_size




def sigma_matrix2(sig_x, sig_y, theta):
    d_matrix = np.array([[sig_x ** 2, 0], [0, sig_y ** 2]])
    u_matrix = np.array([[np.cos(theta), -np.sin(theta)], [np.sin(theta), np.cos(theta)]])
    return np.dot(u_matrix, np.dot(d_matrix, u_matrix.T))


def mesh_grid(kernel_size):
    """Generate the mesh grid, centering at zero.
    """
    ax = np.arange(-kernel_size // 2 + 1., kernel_size // 2 + 1.)
    xx, yy = np.meshgrid(ax, ax)
    xy = np.hstack((xx.reshape((kernel_size * kernel_size, 1)), yy.reshape(kernel_size * kernel_size,
                                                                           1))).reshape(kernel_size, kernel_size, 2)
    return xy, xx, yy


def pdf2(sigma_matrix, grid):
    """Calculate PDF of the bivariate Gaussian distribution.
    Args:
        sigma_matrix (ndarray): with the shape (2, 2)
        grid (ndarray): generated by :func:`mesh_grid`,
            with the shape (K, K, 2), K is the kernel size.
    Returns:
        kernel (ndarrray): un-normalized kernel.
    """
    inverse_sigma = np.linalg.inv(sigma_matrix)
    kernel = np.exp(-0.5 * np.sum(np.dot(grid, inverse_sigma) * grid, 2))
    return kernel


def cdf2(d_matrix, grid):
    """Calculate the CDF of the standard bivariate Gaussian distribution.
        Used in skewed Gaussian distribution.
    Returns:
        cdf (ndarray): skewed cdf.
    """
    rv = multivariate_normal([0, 0], [[1, 0], [0, 1]])
    grid = np.dot(grid, d_matrix)
    cdf = rv.cdf(grid)
    return cdf


def bivariate_Gaussian(kernel_size, sig_x, sig_y, theta, grid=None, isotropic=True):
    """Generate a bivariate isotropic or anisotropic Gaussian kernel.
    Returns:
        kernel (ndarray): normalized kernel.
    """
    if grid is None:
        grid, _, _ = mesh_grid(kernel_size)
    if isotropic:
        sigma_matrix = np.array([[sig_x ** 2, 0], [0, sig_x ** 2]])
    else:
        sigma_matrix = sigma_matrix2(sig_x, sig_y, theta)
    kernel = pdf2(sigma_matrix, grid)
    kernel = kernel / np.sum(kernel)
    return kernel


def bivariate_generalized_Gaussian(kernel_size, sig_x, sig_y, theta, beta, grid=None, isotropic=True):
    """
    Returns:
        kernel (ndarray): normalized kernel.

    .. _Parameter Estimation For Multivariate Generalized Gaussian
    Distributions: https://arxiv.org/abs/1302.6498
    """
    if grid is None:
        grid, _, _ = mesh_grid(kernel_size)
    if isotropic:
        sigma_matrix = np.array([[sig_x ** 2, 0], [0, sig_x ** 2]])
    else:
        sigma_matrix = sigma_matrix2(sig_x, sig_y, theta)
    inverse_sigma = np.linalg.inv(sigma_matrix)
    kernel = np.exp(-0.5 * np.power(np.sum(np.dot(grid, inverse_sigma) * grid, 2), beta))
    kernel = kernel / np.sum(kernel)
    return kernel


def bivariate_plateau(kernel_size, sig_x, sig_y, theta, beta, grid=None, isotropic=True):
    """Generate a plateau-like anisotropic kernel.
    1 / (1+x^(beta))
    Returns:
        kernel (ndarray): normalized kernel.
    """
    if grid is None:
        grid, _, _ = mesh_grid(kernel_size)
    if isotropic:
        sigma_matrix = np.array([[sig_x ** 2, 0], [0, sig_x ** 2]])
    else:
        sigma_matrix = sigma_matrix2(sig_x, sig_y, theta)
    inverse_sigma = np.linalg.inv(sigma_matrix)
    kernel = np.reciprocal(np.power(np.sum(np.dot(grid, inverse_sigma) * grid, 2), beta) + 1)
    kernel = kernel / np.sum(kernel)
    return kernel


def random_bivariate_Gaussian(kernel_size,
                              sigma_x_range,
                              sigma_y_range,
                              rotation_range,
                              noise_range=None,
                              isotropic=True):
    """Randomly generate bivariate isotropic or anisotropic Gaussian kernels.
    Returns:
        kernel (ndarray):
    """
    assert kernel_size % 2 == 1, 'Kernel size must be an odd number.'
    assert sigma_x_range[0] < sigma_x_range[1], 'Wrong sigma_x_range.'
    sigma_x = np.random.uniform(sigma_x_range[0], sigma_x_range[1])
    if isotropic is False:
        assert sigma_y_range[0] < sigma_y_range[1], 'Wrong sigma_y_range.'
        assert rotation_range[0] < rotation_range[1], 'Wrong rotation_range.'
        sigma_y = np.random.uniform(sigma_y_range[0], sigma_y_range[1])
        rotation = np.random.uniform(rotation_range[0], rotation_range[1])
    else:
        sigma_y = sigma_x
        rotation = 0

    kernel = bivariate_Gaussian(kernel_size, sigma_x, sigma_y, rotation, isotropic=isotropic)

    # add multiplicative noise
    if noise_range is not None:
        assert noise_range[0] < noise_range[1], 'Wrong noise range.'
        noise = np.random.uniform(noise_range[0], noise_range[1], size=kernel.shape)
        kernel = kernel * noise
    kernel = kernel / np.sum(kernel)
    return kernel


def random_bivariate_generalized_Gaussian(kernel_size,
                                          sigma_x_range,
                                          sigma_y_range,
                                          rotation_range,
                                          beta_range,
                                          noise_range=None,
                                          isotropic=True):
    """Randomly generate bivariate generalized Gaussian kernels.
    Returns:
        kernel (ndarray):
    """
    assert kernel_size % 2 == 1, 'Kernel size must be an odd number.'
    assert sigma_x_range[0] < sigma_x_range[1], 'Wrong sigma_x_range.'
    sigma_x = np.random.uniform(sigma_x_range[0], sigma_x_range[1])
    if isotropic is False:
        assert sigma_y_range[0] < sigma_y_range[1], 'Wrong sigma_y_range.'
        assert rotation_range[0] < rotation_range[1], 'Wrong rotation_range.'
        sigma_y = np.random.uniform(sigma_y_range[0], sigma_y_range[1])
        rotation = np.random.uniform(rotation_range[0], rotation_range[1])
    else:
        sigma_y = sigma_x
        rotation = 0

    # assume beta_range[0] < 1 < beta_range[1]
    if np.random.uniform() < 0.5:
        beta = np.random.uniform(beta_range[0], 1)
    else:
        beta = np.random.uniform(1, beta_range[1])

    kernel = bivariate_generalized_Gaussian(kernel_size, sigma_x, sigma_y, rotation, beta, isotropic=isotropic)

    # add multiplicative noise
    if noise_range is not None:
        assert noise_range[0] < noise_range[1], 'Wrong noise range.'
        noise = np.random.uniform(noise_range[0], noise_range[1], size=kernel.shape)
        kernel = kernel * noise
    kernel = kernel / np.sum(kernel)
    return kernel


def random_bivariate_plateau(kernel_size,
                             sigma_x_range,
                             sigma_y_range,
                             rotation_range,
                             beta_range,
                             noise_range=None,
                             isotropic=True):
    """Randomly generate bivariate plateau kernels.
    Returns:
        kernel (ndarray):
    """
    assert kernel_size % 2 == 1, 'Kernel size must be an odd number.'
    assert sigma_x_range[0] < sigma_x_range[1], 'Wrong sigma_x_range.'
    sigma_x = np.random.uniform(sigma_x_range[0], sigma_x_range[1])
    if isotropic is False:
        assert sigma_y_range[0] < sigma_y_range[1], 'Wrong sigma_y_range.'
        assert rotation_range[0] < rotation_range[1], 'Wrong rotation_range.'
        sigma_y = np.random.uniform(sigma_y_range[0], sigma_y_range[1])
        rotation = np.random.uniform(rotation_range[0], rotation_range[1])
    else:
        sigma_y = sigma_x
        rotation = 0

    # TODO: this may be not proper
    if np.random.uniform() < 0.5:
        beta = np.random.uniform(beta_range[0], 1)
    else:
        beta = np.random.uniform(1, beta_range[1])

    kernel = bivariate_plateau(kernel_size, sigma_x, sigma_y, rotation, beta, isotropic=isotropic)
    # add multiplicative noise
    if noise_range is not None:
        assert noise_range[0] < noise_range[1], 'Wrong noise range.'
        noise = np.random.uniform(noise_range[0], noise_range[1], size=kernel.shape)
        kernel = kernel * noise
    kernel = kernel / np.sum(kernel)

    return kernel


def random_mixed_kernels(
    kernel_list: Tuple[str],
    kernel_prob: Tuple[float],
    kernel_size: int,
    sigma_x_range: Tuple[float, float],
    sigma_y_range: Tuple[float, float],
    rotation_range: Tuple[float, float],
    betag_range: Tuple[float, float],
    betap_range: Tuple[float, float],
    noise_range: Optional[Tuple[float, float]] = None
) -> np.ndarray:
    """Generate random mixed kernels"""
    kernel_type = random.choices(kernel_list, kernel_prob)[0]
    if kernel_type == 'iso':
        kernel = random_bivariate_Gaussian(
            kernel_size, sigma_x_range, sigma_y_range, rotation_range, noise_range=noise_range, isotropic=True
        )
    elif kernel_type == 'aniso':
        kernel = random_bivariate_Gaussian(
            kernel_size, sigma_x_range, sigma_y_range, rotation_range, noise_range=noise_range, isotropic=False
        )
    elif kernel_type == 'generalized_iso':
        kernel = random_bivariate_generalized_Gaussian(
            kernel_size,
            sigma_x_range,
            sigma_y_range,
            rotation_range,
            betag_range,
            noise_range=noise_range,
            isotropic=True
        )
    elif kernel_type == 'generalized_aniso':
        kernel = random_bivariate_generalized_Gaussian(
            kernel_size,
            sigma_x_range,
            sigma_y_range,
            rotation_range,
            betag_range,
            noise_range=noise_range,
            isotropic=False
        )
    elif kernel_type == 'plateau_iso':
        kernel = random_bivariate_plateau(
            kernel_size, sigma_x_range, sigma_y_range, rotation_range, betap_range, noise_range=None, isotropic=True)
    elif kernel_type == 'plateau_aniso':
        kernel = random_bivariate_plateau(
            kernel_size, sigma_x_range, sigma_y_range, rotation_range, betap_range, noise_range=None, isotropic=False)
    return kernel


def circular_lowpass_kernel(cutoff: float, kernel_size: int, pad_to: bool = False) -> np.ndarray:
    """2D sinc filter
    """
    assert kernel_size % 2 == 1, 'Kernel size must be an odd number.'
    kernel = np.fromfunction(
        lambda x, y: cutoff * special.j1(cutoff * np.sqrt(
            (x - (kernel_size - 1) / 2) ** 2 + (y - (kernel_size - 1) / 2) ** 2)) / (2 * np.pi * np.sqrt(
            (x - (kernel_size - 1) / 2) ** 2 + (y - (kernel_size - 1) / 2) ** 2)), [kernel_size, kernel_size])
    kernel[(kernel_size - 1) // 2, (kernel_size - 1) // 2] = cutoff ** 2 / (4 * np.pi)
    kernel = kernel / np.sum(kernel)
    if pad_to > kernel_size:
        pad_size = (pad_to - kernel_size) // 2
        kernel = np.pad(kernel, ((pad_size, pad_size), (pad_size, pad_size)))
    return kernel


def process_pil(image, contrast=1.8, brightness=1.0):
    if isinstance(image, Image.Image):
        pass
    elif isinstance(image, np.ndarray):
        image = Image.fromarray(image)
    else:
        raise TypeError("image should be PIL.Image or numpy.ndarray")
    contrast_enhancer = ImageEnhance.Contrast(image)
    image_contrasted = contrast_enhancer.enhance(contrast)
    
    brightness_enhancer = ImageEnhance.Brightness(image_contrasted)
    image_enhanced = brightness_enhancer.enhance(brightness)
    
    return image_enhanced


def process_cv2_with_local_enhancement(image, kernel_size=7):
    image_np = np.array(image)
    mean = cv2.blur(image_np, (kernel_size, kernel_size))

    enhanced = cv2.addWeighted(image_np, 1.5, mean, -0.5, 3.5)
    return enhanced


def generate_random_shape(mask, img_width, img_height, shape_type="rectangle", min_size=0.2, max_size=0.4):
    """
    生成一个随机形状区域，形状可以是矩形、圆形或椭圆形，区域的大小根据图像尺寸来调整。
    :param mask: 要在其上绘制形状的掩码图像
    :param img_width: 图像宽度
    :param img_height: 图像高度
    :param shape_type: 形状类型，可以是 'rectangle', 'circle', 'ellipse'
    :param min_size: 最小尺寸比例（相对于图像尺寸）
    :param max_size: 最大尺寸比例（相对于图像尺寸）
    """
    draw = ImageDraw.Draw(mask)
    
    # 定义形状的大小范围
    min_block_size = int(min(img_width, img_height) * min_size)
    max_block_size = int(min(img_width, img_height) * max_size)

    # 随机生成形状的大小
    block_width = np.random.randint(min_block_size, max_block_size)
    block_height = np.random.randint(min_block_size, max_block_size)

    # 随机生成形状的位置，确保不会超出边界
    top_left_x = np.random.randint(0, img_width - block_width)
    top_left_y = np.random.randint(0, img_height - block_height)
    bottom_right_x = top_left_x + block_width
    bottom_right_y = top_left_y + block_height

    if shape_type == "rectangle":
        # 绘制矩形
        draw.rectangle([top_left_x, top_left_y, bottom_right_x, bottom_right_y], fill=1)
    elif shape_type == "circle":
        # 绘制圆形
        radius = min(block_width, block_height) // 2
        draw.ellipse([top_left_x, top_left_y, top_left_x + 2 * radius, top_left_y + 2 * radius], fill=1)
    elif shape_type == "ellipse":
        # 绘制椭圆形
        draw.ellipse([top_left_x, top_left_y, bottom_right_x, bottom_right_y], fill=1)


def adjust_text_color(image, brightness_factor=2.0, saturation_factor=0.5, num_blocks=3):
    """
    调整文档图像中的文字或表格线段，使其变得浅，同时保持颜色准确性，并随机生成多个弱化区域。
    :param image: 输入的PIL图像对象，假设背景接近白色
    :param brightness_factor: 亮度调整因子，用于调整随机生成区域内的非背景区域亮度
    :param contrast_factor: 对比度调整因子，用于降低随机生成区域内的非背景区域对比度
    :param saturation_factor: 饱和度调整因子，用于降低随机生成区域内的非背景区域饱和度
    :param num_blocks: 随机弱化区块数量
    :param seed: 随机种子，用于生成相同的随机区域
    :return: 调整后的图像和背景掩码图像
    """
    
    if isinstance(image, Image.Image):
        image = image.convert("RGB")
        img_np = np.array(image)
    else:
        img_np = image
        image = Image.fromarray(img_np)
    
    # 对背景进行过滤，假设背景接近白色
    background_mask = np.all(img_np > 200, axis=-1)  # 背景近似白色的区域
    non_background_mask = ~background_mask  # 非背景区域
    background_mask_img = Image.fromarray(np.uint8(background_mask * 255))  # 可视化背景掩码
    # background_mask_img = np.uint8(background_mask * 255)  
    

    # 获取图像尺寸
    img_height, img_width = img_np.shape[:2]

    # 初始化随机区域掩码
    mask = Image.new('1', (img_width, img_height), 0)

    # 生成随机的形状区域，并在这些区域内进行减弱处理
    shape_types = ["rectangle", "circle", "ellipse"]
    for _ in range(num_blocks):
        # 随机选择形状类型
        shape_type = np.random.choice(shape_types)
        generate_random_shape(mask, img_width, img_height, shape_type, min_size=0.20, max_size=0.30)

    mask = np.array(mask)

    # 将 PIL 图像转换为 HSV 色彩空间
    hsv_img = image.convert("HSV")
    hsv_np = np.array(hsv_img)

    # 创建全局掩码：只对随机区域内的非背景部分做处理
    region_non_background_mask = non_background_mask & mask

    # 对随机生成区域的非背景部分进行处理
    # 1. 处理亮度
    v_channel = hsv_np[..., 2]
    v_channel[region_non_background_mask] = np.clip(v_channel[region_non_background_mask] * brightness_factor, 0, 255)
    hsv_np[..., 2] = v_channel

    # 2. 处理饱和度
    s_channel = hsv_np[..., 1]
    s_channel[region_non_background_mask] = np.clip(s_channel[region_non_background_mask] * saturation_factor, 0, 255)
    hsv_np[..., 1] = s_channel

    # 将处理后的 HSV 转回 RGB 色彩空间
    result_img = Image.fromarray(hsv_np, 'HSV').convert('RGB')

    # 将调整结果应用回非背景区域，背景保持不变
    result_np = np.array(result_img)
    result_np[background_mask] = img_np[background_mask]  # 保持背景区域不变

    # 转换回 PIL 图像
    # final_img = Image.fromarray(result_np)

    return result_np, background_mask_img


class ImageEffects(object):
    """
    单独功能和组合功能
    
    TODO 
    0. 0-255, 0-1的统一
    1. 增加更多糊化效果
    2. 其他效果:背景脏污合成等
    3. crop 功能
    """

    @staticmethod
    def gaussian_blur(img: np.ndarray, kernel_size: int, sigma: float) -> np.ndarray:
        """
        kernel_size = random.randrange(*config.gaussian_blur_kernel_range, 2)
        """
        h, w = img.shape[:2]
        img = ImageEffects.reisze_scale(img, power=0.1)
        img = cv2.GaussianBlur(img, (kernel_size, kernel_size), sigma)
        img = cv2.resize(img, (w, h), interpolation=cv2.INTER_LINEAR)
        return img
    
    @staticmethod
    def motion_blur(img: np.ndarray, kernel_size: int, angle: float) -> np.ndarray:
        """
        对图像应用运动模糊
        Args:
            img: 输入图像
            kernel_size: 模糊核大小，必须是奇数
            angle: 运动方向角度（度），0度表示水平方向
        Returns:
            模糊后的图像
        """
        kernel_size = kernel_size + 1 if kernel_size % 2 == 0 else kernel_size
        
        kernel = np.zeros((kernel_size, kernel_size))
        center = kernel_size // 2
        
        angle_rad = np.deg2rad(angle)
        
        # 计算直线的方向向量
        dx = np.cos(angle_rad)
        dy = np.sin(angle_rad)
        
        # 在核上画一条线
        for i in range(kernel_size):
            offset = i - center
            x = center + dx * offset
            y = center + dy * offset
            
            if 0 <= int(y) < kernel_size and 0 <= int(x) < kernel_size:
                kernel[int(y), int(x)] = 1
        
        kernel = kernel / np.sum(kernel)
        
        return cv2.filter2D(img, -1, kernel)
    
    @staticmethod
    def average_blur(img: np.ndarray, kernel_size: int):
        """
        kernel_size = random.randrange(*config.average_blur_kernel_range, 2)
        """
        return cv2.blur(img, (kernel_size, kernel_size))
    
    @staticmethod
    def add_gaussian_noise(img: np.ndarray, std: float) -> np.ndarray:
        """
        std = random.uniform(*config.gaussian_noise_std_range)
        """
        noise = np.random.normal(0, std, img.shape).astype(np.uint8)
        return cv2.add(img, noise)

    @staticmethod
    def add_poisson_noise(img: np.ndarray, scale: float) -> np.ndarray:
        '''
        scale = random.uniform(*config.poisson_noise_scale_range)
        '''
        noise = np.random.poisson(img).astype(np.uint8)
        noisy = np.clip(img + (noise - np.mean(noise)) * scale, 0, 255)
        return noisy.astype(np.uint8)

    @staticmethod
    def add_black_noise_gaussian(image: np.ndarray,
                                 density: float = 0.1) -> np.ndarray:  # 0-1之间，直接控制噪点密度
        """
        使用高斯分布生成纯黑色噪点
        Args:
            image: 输入图像
            density: 噪点密度 (0-1)，直接控制多少比例的像素会变成黑点 0.01~0.02
        Returns:
            添加噪点后的图像
        """
        result = image.copy()
        height, width = image.shape[:2]

        gauss = np.random.normal(0, 1, (height, width))

        # 根据需要的密度，计算对应的阈值
        # 使用标准正态分布的分位点函数（ppf）的反函数（inverse）
        # 即累积分布函数（cdf）求得对应阈值
        threshold = -scipy.stats.norm.ppf(density / 2)  # 由于取绝对值，所以是density/2

        # 根据阈值确定噪点位置
        noise = np.abs(gauss) > threshold

        # 将噪声点变为纯黑色
        result[noise] = 0

        return result

    @staticmethod
    def add_salt_pepper_noise(img: np.ndarray, prob: float) -> np.ndarray:
        '''
        改造墨点版本
        prob = random.uniform(*config.salt_pepper_noise_prob_range)
        '''
        output = np.copy(img)
        noise_mask = np.random.random(img.shape[:2])
        output[noise_mask < prob / 2] = 0
        # output[noise_mask > 1 - prob/2] = 255
        return output

    @staticmethod
    def fade_global(img: np.ndarray, kernel_size: float) -> np.ndarray:
        """
        超过3文字消失,0,2,3均有变淡效果
        kernel_size = random.randrange(*config.fade_global_kernel_range)
        """
        kernel = np.ones((kernel_size, kernel_size), np.uint8)
        img = cv2.dilate(img, kernel)
        return img 
    
    @staticmethod
    def fade_local(img: np.ndarray, brightness_factor: float, saturation_factor: float, num_blocks: int) -> np.ndarray:
        """
        brightness_factor = random.uniform(*config.fade_local_brightness_range)
        saturation_factor = random.uniform(*config.fade_local_saturation_range)
        num_blocks = random.randint(*config.fade_local_num_blocks_range)
        """
        result_image, background_mask_image = adjust_text_color(
            img, brightness_factor,  saturation_factor, num_blocks
        )
        
        return result_image
    
    @staticmethod
    def contrast_meet_line_broken(img: np.ndarray, alpha: float, beta: float) -> np.ndarray:
        """
        pixel = alpha * pixel + beta
        pixel = |pixel|
        pixel = min(max(pixel, 0), 255)
        模拟文档字局部断线效果
        alpha = random.uniform(*config.line_broken_alpha_range)
        beta = random.uniform(*config.line_broken_beta_range)
        
        alpha :增强对比度; beta:调整亮度
        """
        enhanced_image = cv2.convertScaleAbs(img, alpha=alpha, beta=beta)
        return enhanced_image
    
    @staticmethod
    def uneven_lighting(img: np.ndarray, light_variation: float) -> np.ndarray:
        # """
        # 不均匀光照
        # light_variation = random.uniform(*config.uneven_lighting_range)
        # """
        # h, w = img.shape[:2]
        # gradient = np.linspace(1 - light_variation, 1, h)
        # gradient = np.tile(gradient.reshape(-1, 1), (1, w))
        # img = (img.astype(float) * gradient[:, :, np.newaxis]).astype(np.uint8)
        #
        # return img
        """
            应用不均匀光照效果，支持随机方向的渐变。

            参数:
            - img: 输入图像，numpy数组。
            - light_variation: 光照变化范围 (0到1之间)。

            返回:
            - 加入随机方向渐变光照效果的图像。
        """
        h, w = img.shape[:2]

        # 随机生成角度，并转为弧度
        angle = random.uniform(0, 360)
        angle_rad = np.deg2rad(angle)

        # 创建坐标网格
        y, x = np.meshgrid(np.arange(h), np.arange(w), indexing='ij')

        # 计算方向上的权重
        gradient = np.cos(angle_rad) * x + np.sin(angle_rad) * y

        # 归一化渐变
        gradient = gradient - gradient.min()  # 使最小值为0
        gradient = gradient / gradient.max()  # 归一化到0~1
        gradient = 1 - light_variation + light_variation * gradient  # 应用光照变化范围

        # 将渐变应用到图像
        img = (img.astype(float) * gradient[:, :, np.newaxis]).astype(np.uint8)

        return img

    @staticmethod
    def add_artistic_ink_drops(img: np.ndarray,
                               num_clusters: int = 3,
                               drop_density: float = 0.011,
                               cluster_size: int = 20,
                               intensity: float = 0.3,
                               noise_scale: float = 0.1,
                               blur: float = 0.5, ) -> np.ndarray:
        """
        添加艺术墨点
        num_clusters = random.randint(*config.artistic_ink_num_clusters_range)
        drop_density = random.uniform(*config.artistic_ink_drop_density_range)
        cluster_size = random.randint(*config.artistic_ink_cluster_size_range)
        intensity = random.uniform(*config.artistic_ink_intensity_range)
        noise_scale = random.uniform(*config.artistic_ink_noise_scale_range)
        blur = random.uniform(*config.artistic_ink_blur_sigma_range)
        """
        H, W = img.shape[:2]
        mask = np.zeros((H, W), dtype=np.float32)

        for _ in range(num_clusters):
            center_x = np.random.randint(0, W)
            center_y = np.random.randint(0, H)
            size = cluster_size
            density = drop_density

            # 生成基础高斯分布
            y, x = np.ogrid[-center_y:H - center_y, -center_x:W - center_x]
            dist = x * x + y * y
            gaussian = np.exp(-dist / (2 * size * size))

            # 添加扰动使形状更自然
            noise = np.random.normal(0, noise_scale, gaussian.shape)
            gaussian += noise
            gaussian = np.clip(gaussian, 0, 1)

            # 生成不规则的墨点分布
            random_mask = np.random.random((H, W)) < (gaussian * density)

            # 随机强度
            mask = np.maximum(mask, random_mask.astype(float) * intensity)

        # 添加细节变化
        detail_noise = np.random.normal(0, noise_scale / 2, mask.shape)
        mask = np.clip(mask + detail_noise, 0, 1)

        # 边缘模糊化
        blur_sigma = blur
        mask = gaussian_filter(mask, sigma=blur_sigma)

        result = img.copy()
        mask = mask[..., np.newaxis]
        result = result * (1 - mask) + np.zeros_like(result) * mask

        return result.astype(np.uint8)

    @staticmethod
    def geometric_distortion(img: np.ndarray, perspective_scale: float) -> np.ndarray:
        """
        应用几何变形
        TODO 待完善两输入, 暂时不考虑
        perspective_scale = random.uniform(*config.perspective_scale_range)
        """
        h, w = img.shape[:2]

        # 随机透视变换
        scale = perspective_scale
        src_points = np.float32([[0, 0], [w - 1, 0], [0, h - 1], [w - 1, h - 1]])
        dst_points = np.float32([
            [0 + random.uniform(-w * scale, w * scale), 0 + random.uniform(-h * scale, h * scale)],
            [w - 1 - random.uniform(-w * scale, w * scale), 0 + random.uniform(-h * scale, h * scale)],
            [0 + random.uniform(-w * scale, w * scale), h - 1 - random.uniform(-h * scale, h * scale)],
            [w - 1 - random.uniform(-w * scale, w * scale), h - 1 - random.uniform(-h * scale, h * scale)]
        ])

        matrix = cv2.getPerspectiveTransform(src_points, dst_points)
        return cv2.warpPerspective(img, matrix, (w, h))

    @staticmethod
    def jpeg_compression(img: np.ndarray, quality: int, num_compressions) -> np.ndarray:
        '''
        jpg, png?
        '''
        for _ in range(num_compressions):
            encode_param = [int(cv2.IMWRITE_JPEG_QUALITY), quality]
            _, enc_img = cv2.imencode('.jpg', img, encode_param)
            img = cv2.imdecode(enc_img, 1)
        # print(img.shape, 'hh')
        # img = ImageEffects.high_pass_filter(img)
        return img

    @staticmethod
    def high_pass_filter(img: np.ndarray) -> np.ndarray:
        img_float = np.float32(img)
        laplacian = cv2.Laplacian(img_float, cv2.CV_32F)
        high_pass = cv2.addWeighted(img_float, 1.5, laplacian, -0.5, 0)
        high_pass = np.clip(high_pass, 0, 255).astype(np.uint8)
        return high_pass

    @staticmethod
    def sinc_blur(img: np.ndarray, kernel_size: int, cutoff: float) -> np.ndarray:
        '''
        应该和circular_lowpass_kernel等价
        kernel_size = random.randrange(*config.sinc_blur_kernel_range, 2)
        cutoff = random.uniform(*config.sinc_blur_cutoff_range)
        '''
        # Create sinc kernel
        h, w = img.shape[:2]
        center = kernel_size // 2
        x = np.linspace(-center, center, kernel_size)
        xx, yy = np.meshgrid(x, x)
        radius = np.sqrt(xx ** 2 + yy ** 2)
        kernel = np.sinc(2 * cutoff * radius / np.pi)
        kernel = kernel / kernel.sum()  # Normalize
        # img = cv2.filter2D(img, -1, kernel)
        # img = ImageEffects.reisze_scale(img, power=0.5)  #
        img = cv2.filter2D(img, -1, kernel)
        img = cv2.resize(img, (w, h), interpolation=cv2.INTER_LINEAR)
        return img

    @staticmethod
    def usm_sharpen(img: np.ndarray, amount: float = 1.0):
        """
        Apply Unsharp Masking to an image.
        amount = random.uniform(*config.usm_amount_range)
        """
        img = cv2.cvtColor(img, cv2.COLOR_RGB2BGR)
        blurred = cv2.GaussianBlur(img, (0, 0), 3)
        sharp = cv2.addWeighted(img, 1 + amount, blurred, -amount, 0)
        sharp = cv2.cvtColor(sharp, cv2.COLOR_BGR2RGB)
        return sharp

    @staticmethod
    def gamma_correction(img: Optional[np.ndarray | Image.Image], gamma: float = 2.5) -> np.ndarray:
        '''
        gamma: 2.5
        darker_bright
        '''
        if isinstance(img, np.ndarray):
            img = img / 255.
        elif isinstance(img, Image.Image):
            img = np.array(img).astype(float) / 255.0
        else:
            raise ValueError(f'type of input img is {type(img)}, is not')
        corrected_img_array = np.power(img, gamma)
        corrected_img = (corrected_img_array * 255).astype(np.uint8)
        return corrected_img

    @staticmethod
    def darker_bright(img: np.ndarray, contrast: float = 1.8, brightness: float = 1.1):
        '''
        contrast=[1.5, 2.0], brightness=[1.1, 1.3]
        '''
        enhanced_image = process_pil(img, contrast=contrast, brightness=brightness)
        enhanced_image = process_cv2_with_local_enhancement(enhanced_image)
        return enhanced_image

    @staticmethod
    def apply_kernel(img: np.ndarray, kernel: np.ndarray) -> np.ndarray:
        return cv2.filter2D(img, -1, kernel)

    @staticmethod
    def rotate_kernel(kernel: np.ndarray, angle: float) -> np.ndarray:
        """旋转核函数"""
        return cv2.warpAffine(kernel,
                              cv2.getRotationMatrix2D((kernel.shape[1] / 2, kernel.shape[0] / 2), angle, 1.0),
                              kernel.shape)

    # 组合
    @staticmethod
    def basic_blur_mixes(img: np.ndarray, config: DegradationConfig) -> np.ndarray:
        """基础模糊叠加效果
            高斯模糊, 运动模糊, 均值模糊, sinc模糊
        Args:
            img: 输入图像
            kernel_type: 模糊核类型 ['gaussian', 'motion', 'average', 'sinc']

        """
        raise NotImplementedError("not implemented")

    @staticmethod
    def basic_noise_mixes(img: np.ndarray, config: DegradationConfig) -> np.ndarray:
        """基础噪声叠加效果"""
        raise NotImplementedError("not implemented")

    # @staticmethod
    # def random_resize(img: np.ndarray, config: DegradationConfig) -> np.ndarray:
    #     """调整图像大小
    #     Args:
    #         img: 输入图像
    #         scale: 缩放比例
    #         interpolation: 插值方法 ['linear', 'cubic', 'area']
    #     """
    #     updown_type = random.choices(['down', 'up'], config.random_resize_prob)[0]
    #     if updown_type == 'up':
    #         scale = np.random.uniform(*config.resize_scale_range[1])
    #     else:
    #         light_heavy_type = 'light' if random.random() <= config.resize_light_heavy_prob[0] else 'heavy'
    #         if light_heavy_type == 'light':
    #             scale = np.random.uniform(*config.resize_scale_range_light_heavy[0])
    #         else:
    #             scale = np.random.uniform(*config.resize_scale_range_light_heavy[1])
    #         # scale = np.random.uniform(*config.resize_scale_range[0])
    #     # interpolation = random.choices(['linear', 'cubic', 'area'])[0]
    #     interpolation = random.choices(['linear'])[0]
    #
    #     h, w = img.shape[:2]
    #     h_adapt = h / 1000   # 4
    #     w_adapt = w / 1000   # 1
    #     light_heavy_prob = random.random()
    #     if light_heavy_prob < config.random_resize_light_heavy_prob:     # 0.5的概率轻、0.5的概率重
    #         scale_adapt = np.power(max(h_adapt, w_adapt), 0.7)  # 0.5太弱
    #     else:
    #         scale_adapt = np.power(max(h_adapt, w_adapt), 0.7) # 1太强
    #     scale = scale / scale_adapt
    #     new_h, new_w = int(h * scale), int(w * scale)
    #
    #     interp_map = {
    #         'linear': cv2.INTER_LINEAR,
    #         'cubic': cv2.INTER_CUBIC,
    #         'area': cv2.INTER_AREA
    #     }
    #     interp_method = interp_map.get(interpolation, cv2.INTER_LINEAR)
    #
    #     degraded_image = cv2.resize(img, (new_w, new_h), interpolation=interp_method)
    #
    #     return cv2.resize(degraded_image, (w, h), interpolation=interp_method)


    @staticmethod
    def random_resize(img: np.ndarray, config: DegradationConfig, choose_flag: Optional[dict]) -> np.ndarray:
        """调整图像大小
        Args:
            img: 输入图像
            scale: 缩放比例
            interpolation: 插值方法 ['linear', 'cubic', 'area']
        """
        power_param = 0.7
        rand_val = random.random()
        resize_param_3 = choose_flag.get("resize_param_3", 1)
        if resize_param_3 == 1:
            if rand_val < 0.3-0.28:
                scale = 0.85
            elif rand_val < 0.8+0.1:
                scale = 0.7
            else:
                scale = 0.65
        elif resize_param_3 == 2:
            if rand_val < 0.3-0.25:
                scale = 0.85
            else:
                scale = 0.7
        else:
            scale = 0.85
            power_param = 0.6+0.1

        interpolation = random.choices(['linear'])[0]
        h, w = img.shape[:2]
        scale_adapt = max(h, w) / 1000
        scale_adapt = np.power(scale_adapt, power_param)
        scale = scale / scale_adapt
        # scale = 0.4
        new_h, new_w = int(h * scale), int(w * scale)

        interp_map = {
            'linear': cv2.INTER_LINEAR,
            'cubic': cv2.INTER_CUBIC,
            'area': cv2.INTER_AREA
        }
        interp_method = interp_map.get(interpolation, cv2.INTER_LINEAR)
        degraded_image = cv2.resize(img, (new_w, new_h), interpolation=interp_method)

        return cv2.resize(degraded_image, (w, h), interpolation=interp_method)

    @staticmethod
    def reisze_scale(img: np.ndarray, scale: float = 0.9, power=0.7) -> np.ndarray:
        h, w = img.shape[:2]
        h_adapt = h / 1000  # 4
        w_adapt = w / 1000  # 1
        scale_adapt = np.power(max(h_adapt, w_adapt), power)
        scale = scale / scale_adapt
        new_h, new_w = int(h * scale), int(w * scale)
        degraded_image = cv2.resize(img, (new_w, new_h), interpolation=cv2.INTER_LINEAR)
        return degraded_image

    @staticmethod
    def esrgan_blur(img: np.ndarray, config: DegradationConfig, choose_flag: Optional[dict]) -> np.ndarray:
        """
        应用EASR-GAN的模糊效果
        """
        h, w = img.shape[:2]
        img = ImageEffects.reisze_scale(img, power=0.5)
        # kernel_size = random.choices(config.esrgan_blur_kernel_size_list)[0]
        rand_val = random.random()
        blur_param_1 = choose_flag.get("blur_param_1", 1)
        if blur_param_1 == 1:
            if rand_val < 0.3-0.28:
                kernel_size = random.choice([3, 5])
            elif rand_val < 0.8+0.1:
                kernel_size = random.choice([7, 9])
            else:
                kernel_size = random.choice([11, 13])
        elif blur_param_1 == 2:
            if rand_val < 0.3-0.25:
                kernel_size = random.choice([3, 5])
            else:
                kernel_size = random.choice([7, 9])
        elif blur_param_1 == 4:
            if rand_val < 0.3 - 0.28:
                kernel_size = random.choice([13])
            elif rand_val < 0.8 + 0.1:
                kernel_size = random.choice([15, 17])
            else:
                kernel_size = random.choice([19])
        else:
            kernel_size = random.choice([3+4, 5+4])

        kernel1 = random_mixed_kernels(
            config.esrgan_blur_kernel_type_list,
            config.esrgan_blur_kernel_prob,
            kernel_size,
            config.esrgan_blur_sigma_range,
            config.esrgan_blur_sigma_range,
            [-math.pi, math.pi],
            config.esrgan_blur_betap_range,
            config.esrgan_blur_betap_range,
            noise_range=None)
        # pad kernel
        pad_size = (21 - kernel_size) // 2
        kernel1 = np.pad(kernel1, ((pad_size, pad_size), (pad_size, pad_size)))
        img = cv2.filter2D(img, -1, kernel1)
        img = cv2.resize(img, (w, h), interpolation=cv2.INTER_LINEAR)

        return img

    # 新增模糊 来源：https://zhuanlan.zhihu.com/p/125744132
    # 除iris_blur_large 验证了，其余待测
    @staticmethod
    def bokeh_blur(img: np.ndarray, blur_radius: int = 15,
                   mask_radius: int = 50) -> np.ndarray:
        h, w = img.shape[:2]
        blurred_img = cv2.GaussianBlur(img, (blur_radius * 2 + 1, blur_radius * 2 + 1), 0)
        mask = np.zeros((h, w), dtype=np.uint8)

        for r in range(mask_radius, 0, -5):
            alpha = int(255 * (r / mask_radius))
            cv2.circle(mask, (w // 2, h // 2), r, alpha, -1)

        mask = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
        return (img * mask + blurred_img * (1 - mask)).astype(np.uint8)

    @staticmethod
    def dual_blur(img: np.ndarray, global_blur_radius: int = 5,
                  local_blur_radius: int = 10) -> np.ndarray:
        global_blurred = cv2.GaussianBlur(img, (global_blur_radius * 2 + 1, global_blur_radius * 2 + 1), 0)
        local_blurred = cv2.boxFilter(img, -1, (local_blur_radius, local_blur_radius))
        return cv2.addWeighted(global_blurred, 0.5, local_blurred, 0.5, 0)

    @staticmethod
    def kawase_blur(img: np.ndarray, iterations: int = 3, blur_radius: int = 3,
                    scale_factor: float = 0.5) -> np.ndarray:
        result = img.copy()
        for _ in range(iterations):
            h, w = result.shape[:2]
            small_img = cv2.resize(result, (int(w * scale_factor), int(h * scale_factor)),
                                   interpolation=cv2.INTER_LANCZOS4)
            blurred = cv2.GaussianBlur(small_img, (blur_radius * 2 + 1, blur_radius * 2 + 1), 0)
            result = cv2.resize(blurred, (w, h), interpolation=cv2.INTER_LANCZOS4)
        return result

    @staticmethod
    def create_tilt_shift_mask(size: Tuple[int, int], start: int, end: int,
                               gradient_size: int = 100) -> np.ndarray:
        h, w = size
        mask = np.zeros((h, w), dtype=np.uint8)

        # 中心清晰区域
        mask[start:end, :] = 255

        # 上方渐变
        for i in range(gradient_size):
            y = start - gradient_size + i
            if 0 <= y < start:
                alpha = int(255 * (i / gradient_size))
                mask[y, :] = alpha

        # 下方渐变
        for i in range(gradient_size):
            y = end + i
            if end < y < h:
                alpha = int(255 * (1 - i / gradient_size))
                mask[y, :] = alpha

        return mask

    @staticmethod
    def tilt_shift_blur(img: np.ndarray, start: int, end: int,
                        blur_radius: int = 10, gradient_size: int = 100) -> np.ndarray:
        blurred = cv2.GaussianBlur(img, (blur_radius * 2 + 1, blur_radius * 2 + 1), 0)
        mask = ImageEffects.create_tilt_shift_mask(img.shape[:2], start, end, gradient_size)
        mask = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0
        return (img * mask + blurred * (1 - mask)).astype(np.uint8)

    @staticmethod
    def create_iris_blur_mask(size: Tuple[int, int], center: Tuple[int, int],
                              radius: int, gradient_size: int) -> np.ndarray:
        """
        创建自适应大小的 Iris Blur 掩码
        """
        height, width = size
        mask = np.zeros((height, width), dtype=np.float32)

        y, x = np.ogrid[:height, :width]
        distances = np.sqrt((x - center[0]) ** 2 + (y - center[1]) ** 2)

        # 核心清晰区域
        mask[distances <= radius] = 255

        # 渐变区域
        gradient_area = (distances > radius) & (distances <= radius + gradient_size)
        mask[gradient_area] = 255 * (1 - (distances[gradient_area] - radius) / gradient_size)

        # 平滑过渡
        mask = cv2.GaussianBlur(mask, (5, 5), 1)

        return mask.astype(np.uint8)

    @staticmethod
    def apply_strong_blur(img: np.ndarray, blur_radius: int) -> np.ndarray:
        """
        应用多层次的模糊效果
        """
        blur_layers = []
        blur_weights = [0.4, 0.6]

        for i, weight in enumerate(blur_weights):
            current_radius = max(1, int(blur_radius * (i + 1)))
            kernel_size = current_radius * 2 + 1
            kernel_size = max(3, min(kernel_size, 99))
            kernel_size = kernel_size if kernel_size % 2 == 1 else kernel_size + 1

            blurred = cv2.GaussianBlur(img, (kernel_size, kernel_size), 0)
            blur_layers.append(blurred)

        result = np.zeros_like(img, dtype=np.float32)
        weight_sum = sum(blur_weights)
        normalized_weights = [w / weight_sum for w in blur_weights]

        for layer, weight in zip(blur_layers, normalized_weights):
            result += layer.astype(np.float32) * weight

        return np.clip(result, 0, 255).astype(np.uint8)

    @staticmethod
    def iris_blur(img: np.ndarray,
                  center: Optional[Tuple[int, int]] = None,
                  radius: Optional[int] = None,
                  blur_radius: Optional[int] = None,
                  gradient_size: Optional[int] = None) -> np.ndarray:
        """
        应用 Iris Blur 效果
        Args:
            img: 输入图像
            center: 模糊中心点 (x, y)
            radius: 清晰区域半径
            blur_radius: 模糊强度
            gradient_size: 渐变区域大小
        Returns:
            处理后的图像
        """
        height, width = img.shape[:2]
        diagonal = np.sqrt(width ** 2 + height ** 2)

        if center is None:
            # center = (width // 2, height // 2)
            # 随机生成中心点
            center = (np.random.randint(0, width), np.random.randint(0, height))
        if blur_radius is None:
            blur_radius = int(diagonal * 0.02)
        if radius is None:
            radius = int(diagonal * 0.15)
        if gradient_size is None:
            gradient_size = int(diagonal * 0.2)

        # 创建模糊效果
        blurred = ImageEffects.apply_strong_blur(img, blur_radius)

        # 创建掩码
        mask = ImageEffects.create_iris_blur_mask((height, width), center, radius, gradient_size)
        mask_3channel = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR).astype(np.float32) / 255.0

        # 合并图像
        result = cv2.convertScaleAbs(
            img.astype(np.float32) * mask_3channel +
            blurred.astype(np.float32) * (1 - mask_3channel)
        )

        return result

    @staticmethod
    def iris_blur_large(img: np.ndarray,
                        max_size: int = 4000,
                        clear_ratio: float = 0.6,  # 清晰区域比例
                        blur_ratio: float = 0.002,  # 模糊强度比例
                        gradient_ratio: float = 0.1  # 渐变区域比例
                        ) -> np.ndarray:
        """
        处理大型图像的 Iris Blur 效果
        Args:
            img: 输入图像 (numpy.ndarray)
            max_size: 图像最大尺寸
            clear_ratio: 清晰区域占图像宽度的比例 (0.0-1.0)
            blur_ratio: 模糊强度占图像宽度的比例 (0.0-1.0)
            gradient_ratio: 渐变区域占图像宽度的比例 (0.0-1.0)

        Returns:
            处理后的图像 (numpy.ndarray)
        """
        if img is None:
            raise ValueError("Input image is None")

        original_height, original_width = img.shape[:2]

        max_dim = max(original_height, original_width)
        if max_dim > max_size:
            scale = max_size / max_dim
            new_width = int(original_width * scale)
            new_height = int(original_height * scale)
            img = cv2.resize(img, (new_width, new_height),
                             interpolation=cv2.INTER_LANCZOS4)

        width = img.shape[1]
        result = ImageEffects.iris_blur(
            img,
            radius=int(width * clear_ratio),  # 清晰区域
            blur_radius=int(width * blur_ratio),  # 模糊强度
            gradient_size=int(width * gradient_ratio)  # 渐变区域
        )

        if img.shape[:2] != (original_height, original_width):
            result = cv2.resize(result, (original_width, original_height),
                                interpolation=cv2.INTER_LANCZOS4)

        return result

    @staticmethod
    def add_noise(image: np.ndarray, noise_level: int = 10) -> np.ndarray:
        noise = np.random.normal(0, noise_level, image.shape).astype(np.int16)
        noisy_image = cv2.add(image, noise)
        return np.clip(noisy_image, 0, 255).astype(np.uint8)

    @staticmethod
    def grainy_blur(img: np.ndarray, blur_radius: int = 10,
                    noise_level: int = 10) -> np.ndarray:
        blurred = cv2.GaussianBlur(img, (blur_radius * 2 + 1, blur_radius * 2 + 1), 0)
        return ImageEffects.add_noise(blurred, noise_level)

    @staticmethod
    def gamma_correction_lookup_table(img: Optional[np.ndarray], gamma: float = 2.5) -> np.ndarray:
        if img is None:
            raise ValueError('Input image is None')

        # 创建查找表
        lookup_table = np.array([((i / 255.0) ** gamma) * 255
                                 for i in np.arange(0, 256)]).astype(np.uint8)

        # 应用查找表
        return cv2.LUT(img, lookup_table)

    @staticmethod
    def radial_blur(img: np.ndarray, center: Tuple[int, int],
                    max_blur_radius: int, blur_strength: int = 10) -> np.ndarray:
        h, w = img.shape[:2]
        y, x = np.ogrid[:h, :w]
        distances = np.sqrt((x - center[0]) ** 2 + (y - center[1]) ** 2)

        mask = np.zeros((h, w), dtype=np.uint8)
        mask[distances < max_blur_radius] = ((distances[distances < max_blur_radius]
                                              / max_blur_radius) * 255).astype(np.uint8)
        mask[distances >= max_blur_radius] = 255

        blurred = cv2.GaussianBlur(img, (blur_strength * 2 + 1, blur_strength * 2 + 1), 0)
        mask = cv2.cvtColor(mask, cv2.COLOR_GRAY2BGR) / 255.0

        return (img * mask + blurred * (1 - mask)).astype(np.uint8)

    @staticmethod
    def directional_blur(img: np.ndarray, direction: Tuple[float, float],
                         length: int, blur_radius: int) -> np.ndarray:
        dx, dy = direction
        kernel = np.zeros((length, length))
        center = length // 2

        for i in range(length):
            x = center + (i - center) * dx
            y = center + (i - center) * dy
            x, y = int(x), int(y)
            if 0 <= x < length and 0 <= y < length:
                kernel[y, x] = 1

        kernel = kernel / np.sum(kernel)
        return cv2.filter2D(img, -1, kernel)





