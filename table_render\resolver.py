"""
配置解析器

负责将泛化配置转换为具体的、确定性的参数。
这是V3.1架构重构的核心组件，实现了"泛化配置 -> 具体元数据"的工作流。
V3.2版本增强：支持样式继承机制、智能字体选择、颜色对比度保证等新特性。
"""

import logging
import os
import numpy as np
from typing import Any, Dict, List, Union

from .config import RenderConfig, ResolvedParams, ProbabilisticRange, ProbabilisticBorderConfig, ProbabilisticOptions, ResolvedSizingParams, PostprocessingConfig
from .utils.font_utils import FontManager
from .utils.color_utils import ColorManager
from .utils.style_utils import StyleInheritanceManager
from .utils.prob_utils import choose_from_list, get_from_range


class Resolver:
    """
    配置解析器类

    将包含概率性和范围性的配置转换为具体的、确定性的参数。
    每次调用resolve方法都会基于随机种子生成一组具体的参数。
    V3.2版本增强：集成字体管理器、颜色管理器和样式继承管理器。
    """

    def __init__(self):
        """初始化解析器"""
        self.logger = logging.getLogger(__name__)

        # V3.2新增：初始化工具管理器
        self.font_manager = FontManager()
        self.color_manager = ColorManager()
        self.style_inheritance_manager = None  # 在resolve时初始化，因为需要random_state
        
    def resolve(self, config: RenderConfig, seed: int) -> ResolvedParams:
        """
        解析配置为具体参数

        Args:
            config: 原始的渲染配置
            seed: 随机种子，用于确保可复现性

        Returns:
            解析后的具体参数
        """
        # 设置随机状态
        random_state = np.random.RandomState(seed)

        # 保存当前配置，供其他方法使用
        self.current_config = config

        # V3.2：初始化样式继承管理器
        self.style_inheritance_manager = StyleInheritanceManager(random_state)

        self.logger.debug(f"开始解析配置，种子: {seed}")

        # 解析结构参数
        resolved_structure = self._resolve_structure_params(config.structure, random_state)

        # 解析内容参数
        resolved_content = self._resolve_content_params(config.content, random_state)

        # V3.2：增强的样式解析
        resolved_style = self._resolve_style_params(config.style, resolved_structure, random_state)

        # 解析输出参数
        resolved_output = self._resolve_output_params(config.output, random_state)

        # V4.0新增：解析后处理参数
        resolved_postprocessing = self._resolve_postprocessing_params(config.postprocessing, random_state)

        # 创建解析后的参数对象
        resolved_params = ResolvedParams(
            structure=resolved_structure,
            content=resolved_content,
            style=resolved_style,
            output=resolved_output,
            seed=seed,
            postprocessing=resolved_postprocessing
        )

        self.logger.debug("配置解析完成")
        return resolved_params

    def _resolve_prob_range(self, prob_range: ProbabilisticRange, random_state) -> Union[int, float]:
        """
        解析一个概率化范围配置，返回一个确定的数值

        Args:
            prob_range: 概率化范围配置
            random_state: 随机状态

        Returns:
            解析后的具体数值
        """
        # 使用概率工具选择一个范围
        chosen_range = choose_from_list(
            prob_range.range_list,
            prob_range.probability_list,
            random_state
        )

        # 从选中的范围中随机选择一个值
        return get_from_range(chosen_range, random_state)

    def _resolve_value_or_prob_range(self, value, random_state) -> Union[int, float]:
        """
        解析值或概率化范围：支持固定值、RangeConfig、FloatRangeConfig和ProbabilisticRange

        Args:
            value: 要解析的值
            random_state: 随机状态

        Returns:
            解析后的具体值
        """
        if isinstance(value, ProbabilisticRange):
            return self._resolve_prob_range(value, random_state)
        else:
            return self._resolve_value(value, random_state)

    def _resolve_prob_options(self, prob_options: ProbabilisticOptions, random_state) -> Any:
        """
        解析概率化选项配置，返回一个确定的选项

        Args:
            prob_options: 概率化选项配置
            random_state: 随机状态

        Returns:
            解析后的具体选项
        """
        return choose_from_list(
            prob_options.option_list,
            prob_options.probability_list,
            random_state
        )

    def _resolve_choice_or_prob_options(self, value, random_state) -> Any:
        """
        解析选择值或概率化选项：支持单个值、列表和ProbabilisticOptions

        Args:
            value: 要解析的值
            random_state: 随机状态

        Returns:
            解析后的具体值
        """
        if isinstance(value, ProbabilisticOptions):
            return self._resolve_prob_options(value, random_state)
        else:
            return self._resolve_choice(value, random_state)

    def _resolve_value_choice_or_prob(self, value, random_state) -> Any:
        """
        解析值、选择或概率化配置：支持固定值、RangeConfig、列表、ProbabilisticRange和ProbabilisticOptions

        Args:
            value: 要解析的值
            random_state: 随机状态

        Returns:
            解析后的具体值
        """
        if isinstance(value, (ProbabilisticRange, ProbabilisticOptions)):
            if isinstance(value, ProbabilisticRange):
                return self._resolve_prob_range(value, random_state)
            else:
                return self._resolve_prob_options(value, random_state)
        else:
            # 处理固定值、RangeConfig、FloatRangeConfig或列表
            return self._resolve_value(value, random_state)
    
    def _resolve_structure_params(self, structure_config, random_state) -> Any:
        """
        解析结构参数

        Args:
            structure_config: 结构配置
            random_state: 随机状态

        Returns:
            解析后的结构参数
        """
        from .config import ResolvedStructureParams

        # V3.4：支持概率化处理
        return ResolvedStructureParams(
            body_rows=self._resolve_value_or_prob_range(structure_config.body_rows, random_state),
            cols=self._resolve_value_or_prob_range(structure_config.cols, random_state),
            header_rows=self._resolve_value_or_prob_range(structure_config.header_rows, random_state),
            merge_probability=self._resolve_value_or_prob_range(structure_config.merge_probability, random_state),
            max_row_span=self._resolve_value_choice_or_prob(structure_config.max_row_span, random_state),
            max_col_span=self._resolve_value_choice_or_prob(structure_config.max_col_span, random_state),
            complex_header=structure_config.complex_header
        )
    
    def _resolve_content_params(self, content_config, random_state) -> Any:
        """
        解析内容参数

        Args:
            content_config: 内容配置
            random_state: 随机状态

        Returns:
            解析后的内容参数
        """
        # 在第一步中，直接复制配置
        from .config import ResolvedContentParams

        # 提取CSV配置信息
        csv_file_path = None
        csv_encoding = None
        csv_mismatch_strategy = None

        if content_config.source_type == 'csv' and content_config.csv_source:
            csv_file_path = content_config.csv_source.file_path
            csv_encoding = content_config.csv_source.encoding
            csv_mismatch_strategy = content_config.csv_source.mismatch_strategy

        return ResolvedContentParams(
            source_type=content_config.source_type,
            csv_file_path=csv_file_path,
            csv_encoding=csv_encoding,
            csv_mismatch_strategy=csv_mismatch_strategy,
            programmatic_types=content_config.programmatic_types
        )
    
    def _resolve_style_params(self, style_config, resolved_structure, random_state) -> Any:
        """
        解析样式参数（V3.4版本）

        Args:
            style_config: 样式配置
            resolved_structure: 解析后的结构参数（用于sizing解析）
            random_state: 随机状态

        Returns:
            解析后的样式参数
        """
        from .config import ResolvedStyleParams, ResolvedBaseStyleParams

        self.logger.debug("使用V3.3样式继承模式")

        # 使用样式继承逻辑
        header_style_dict, body_style_dict = self.style_inheritance_manager.apply_inheritance(
            style_config.common,
            style_config.inheritance
        )

        # V3.3修正：先解析表头得到实际颜色，再解析表体以实现真正的颜色继承
        resolved_header = self._dict_to_resolved_base_style(header_style_dict)

        # 如果表体需要颜色继承，更新表体样式字典中的表头颜色信息
        if body_style_dict.get('_needs_color_inheritance', False):
            body_style_dict['_header_style']['text_color'] = resolved_header.text_color
            body_style_dict['_header_style']['background_color'] = resolved_header.background_color

        resolved_body = self._dict_to_resolved_base_style(body_style_dict)

        # 解析边框模式
        border_mode, border_details = self._resolve_border_mode(style_config.border_mode, random_state)

        # V3.3新增：斑马纹决策和颜色生成
        use_zebra, zebra_colors = self._resolve_zebra_stripes(
            style_config.zebra_stripes, resolved_header, resolved_body, random_state
        )

        # V3.4新增：解析sizing配置
        resolved_sizing = self._resolve_sizing_params(
            style_config.sizing, resolved_structure, random_state
        )

        # V4.0新增：样式冲突检测和日志记录
        self._detect_style_conflicts(style_config, resolved_header, resolved_body)

        return ResolvedStyleParams(
            header=resolved_header,
            body=resolved_body,
            zebra_stripes=use_zebra,
            zebra_colors=zebra_colors,
            sizing=resolved_sizing,
            hierarchical=style_config.hierarchical,
            border_mode=border_mode,
            border_details=border_details,
            merged_cell_center_probability=style_config.common.merged_cell_center_probability,
            overflow_strategy=style_config.overflow_strategy  # V4.0新增
        )

    def _resolve_zebra_stripes(self, zebra_config, resolved_header, resolved_body, random_state):
        """
        解析斑马纹配置并生成颜色

        Args:
            zebra_config: 斑马纹配置（bool或float）
            resolved_header: 解析后的表头样式
            resolved_body: 解析后的表体样式
            random_state: 随机状态

        Returns:
            (是否使用斑马纹, 斑马纹颜色列表) 元组
        """
        # 向后兼容：处理bool类型
        if isinstance(zebra_config, bool):
            if not zebra_config:
                return False, None
            zebra_probability = 1.0  # 如果是True，则100%使用斑马纹
        else:
            zebra_probability = zebra_config

        # 概率决策
        if random_state.random() > zebra_probability:
            return False, None

        # 生成斑马纹颜色：考虑颜色继承
        zebra_colors = self._generate_zebra_colors(resolved_header, resolved_body, random_state)
        return True, zebra_colors

    def _generate_zebra_colors(self, resolved_header, resolved_body, random_state):
        """
        生成斑马纹颜色，考虑颜色继承逻辑

        Args:
            resolved_header: 解析后的表头样式
            resolved_body: 解析后的表体样式
            random_state: 随机状态

        Returns:
            [奇数行背景色, 偶数行背景色] 列表
        """
        # 使用默认的颜色配置
        from .config import ColorContrastConfig
        body_color_contrast = ColorContrastConfig()
        body_randomize_prob = 0.3  # 默认概率

        # 检查表头是否为默认色，用于优化颜色生成
        header_is_default = (resolved_header.text_color == "#000000" and
                           resolved_header.background_color == "#FFFFFF")

        # 如果表头是默认色，强制生成随机颜色避免冲突
        effective_randomize_prob = 1.0 if header_is_default else body_randomize_prob

        # 生成两种背景色用于斑马纹
        color1_text, color1_bg = self.color_manager.get_color_pair(
            effective_randomize_prob, body_color_contrast, random_state
        )
        color2_text, color2_bg = self.color_manager.get_color_pair(
            effective_randomize_prob, body_color_contrast, random_state
        )

        # 返回两种背景色（文本色在斑马纹中统一使用表体的文本色）
        return [color1_bg, color2_bg]

    def _resolve_base_style_params(self, base_style_config, random_state) -> Any:
        """
        解析基础样式参数

        Args:
            base_style_config: 基础样式配置
            random_state: 随机状态

        Returns:
            解析后的基础样式参数
        """
        from .config import ResolvedBaseStyleParams, ResolvedFontParams

        # V3.3：增强的字体解析，支持概率化目录选择
        selected_font_dir = self.font_manager.get_weighted_random_directory(
            base_style_config.font.font_dirs,
            base_style_config.font.font_dir_probabilities,
            random_state
        )

        # 扫描选中目录的可用字体
        available_fonts = self.font_manager.scan_font_directories([selected_font_dir])

        # 智能字体选择
        font_candidates = self._resolve_choice(base_style_config.font.default_family, random_state)
        if isinstance(font_candidates, str):
            font_candidates = [font_candidates]

        selected_font = self.font_manager.select_safe_font(font_candidates)

        # 获取回退字体
        fallback_font = getattr(base_style_config.font, 'fallback_font', 'Microsoft YaHei')

        resolved_font = ResolvedFontParams(
            font_dirs=[selected_font_dir],
            default_family=selected_font,
            default_size=self._resolve_value(base_style_config.font.default_size, random_state),
            bold_probability=self._resolve_value(base_style_config.font.bold_probability, random_state),
            italic_probability=self._resolve_value(base_style_config.font.italic_probability, random_state),
            fallback_font=fallback_font
        )

        # V3.3：概率化颜色生成
        color_probability = getattr(base_style_config, 'randomize_color_probability', 0.3)
        color_contrast_config = getattr(base_style_config, 'color_contrast', None)
        text_color, background_color = self.color_manager.get_color_pair(
            color_probability, color_contrast_config, random_state
        )

        return ResolvedBaseStyleParams(
            font=resolved_font,
            text_color=text_color,
            background_color=background_color,
            horizontal_align=self._resolve_choice(base_style_config.horizontal_align, random_state),
            vertical_align=self._resolve_choice(base_style_config.vertical_align, random_state),
            padding=self._resolve_value(base_style_config.padding, random_state)
        )
    
    def _resolve_output_params(self, output_config, random_state) -> Any:
        """
        解析输出参数

        Args:
            output_config: 输出配置
            random_state: 随机状态

        Returns:
            解析后的输出参数
        """
        # 在第一步中，直接复制配置
        from .config import ResolvedOutputParams

        return ResolvedOutputParams(
            output_dir=output_config.output_dir,
            label_suffix=output_config.label_suffix
        )

    def _resolve_value(self, value, random_state):
        """
        解析单个值：如果是RangeConfig或FloatRangeConfig则从范围中选择，否则返回原值

        Args:
            value: 要解析的值
            random_state: 随机状态

        Returns:
            解析后的具体值
        """
        from .config import RangeConfig, FloatRangeConfig

        if isinstance(value, RangeConfig):
            # 整数范围
            return random_state.randint(value.min, value.max + 1)
        elif isinstance(value, FloatRangeConfig):
            # 浮点数范围
            return random_state.uniform(value.min, value.max)
        else:
            return value

    def _resolve_choice(self, value, random_state):
        """
        解析选择值：如果是列表则随机选择一个，否则返回原值

        Args:
            value: 要解析的值
            random_state: 随机状态

        Returns:
            解析后的具体值
        """
        if isinstance(value, list) and len(value) > 0:
            return random_state.choice(value)
        else:
            return value

    def _dict_to_resolved_base_style(self, style_dict: Dict[str, Any]) -> Any:
        """
        将样式字典转换为ResolvedBaseStyleParams对象

        Args:
            style_dict: 样式字典

        Returns:
            ResolvedBaseStyleParams对象
        """
        from .config import ResolvedBaseStyleParams, ResolvedFontParams

        resolved_font = ResolvedFontParams(
            font_dirs=style_dict['font']['font_dirs'],
            default_family=style_dict['font']['default_family'],
            default_size=style_dict['font']['default_size'],
            bold_probability=style_dict['font']['bold_probability'],
            italic_probability=style_dict['font']['italic_probability'],
            fallback_font=style_dict['font']['fallback_font']
        )

        # V3.3：使用概率化颜色生成或颜色继承
        if style_dict.get('_needs_color_inheritance', False):
            # 表体颜色：使用继承逻辑
            header_style = style_dict['_header_style']
            text_color, background_color = self.color_manager.get_inherited_color_pair(
                header_style.get('text_color', '#000000'),
                header_style.get('background_color', '#FFFFFF'),
                style_dict['_text_color_change_probability'],
                style_dict['_background_color_change_probability'],
                style_dict.get('randomize_color_probability', 0.3),
                style_dict.get('color_contrast', None),
                self.style_inheritance_manager.random_state
            )
        else:
            # 表头颜色：使用标准逻辑
            color_probability = style_dict.get('randomize_color_probability', 0.3)
            color_contrast_config = style_dict.get('color_contrast', None)
            text_color, background_color = self.color_manager.get_color_pair(
                color_probability, color_contrast_config, self.style_inheritance_manager.random_state
            )

        return ResolvedBaseStyleParams(
            font=resolved_font,
            text_color=text_color,
            background_color=background_color,
            horizontal_align=style_dict['horizontal_align'],
            vertical_align=style_dict['vertical_align'],
            padding=style_dict['padding']
        )

    def _resolve_border_mode(self, border_mode_config, random_state) -> tuple:
        """
        解析边框模式配置

        Args:
            border_mode_config: 边框模式配置（固定模式或概率化模式）
            random_state: 随机状态

        Returns:
            (边框模式, 边框详细配置) 元组
        """
        # V3.4：支持概率化边框配置
        if isinstance(border_mode_config, ProbabilisticBorderConfig):
            # 概率化边框配置
            border_options = [opt.config for opt in border_mode_config.mode_options]
            probabilities = [opt.probability for opt in border_mode_config.mode_options]
            chosen_border_config = choose_from_list(border_options, probabilities, random_state)

            # 解析选中的边框配置
            mode = chosen_border_config.get('mode', 'full')
            details = None

            if mode == 'semi' and 'semi_config' in chosen_border_config:
                semi_config = chosen_border_config['semi_config']
                details = {
                    'row_line_probability': semi_config.get('row_line_probability', 0.5),
                    'col_line_probability': semi_config.get('col_line_probability', 0.5),
                    'outer_frame': semi_config.get('outer_frame', True),
                    'header_separator': semi_config.get('header_separator', True)
                }

            return mode, details
        else:
            # 传统的固定边框配置
            mode = border_mode_config.mode
            details = None

            if mode == 'semi' and border_mode_config.semi_config:
                details = {
                    'row_line_probability': border_mode_config.semi_config.row_line_probability,
                    'col_line_probability': border_mode_config.semi_config.col_line_probability,
                    'outer_frame': border_mode_config.semi_config.outer_frame,
                    'header_separator': border_mode_config.semi_config.header_separator
                }

            return mode, details

    def _resolve_sizing_params(self, sizing_config, resolved_structure, random_state) -> ResolvedSizingParams:
        """
        解析sizing配置为具体的行列尺寸

        Args:
            sizing_config: sizing配置
            resolved_structure: 解析后的结构参数（用于获取行列数量）
            random_state: 随机状态

        Returns:
            解析后的sizing参数
        """
        # 计算总行数和列数
        total_rows = resolved_structure.header_rows + resolved_structure.body_rows
        total_cols = resolved_structure.cols

        # 解析默认值
        default_row_height = self._resolve_value(sizing_config.default_row_height, random_state)
        default_col_width = self._resolve_value(sizing_config.default_col_width, random_state)

        # 初始化结果字典
        row_heights = {}
        col_widths = {}

        # 处理行配置
        if sizing_config.row_configs:
            occupied_rows = set()  # 记录已被占用的行

            for config_item in sizing_config.row_configs:
                # 判断配置是否启用 - 修复：确保1.0概率时一定触发
                if random_state.random() <= config_item.probability:
                    self.logger.info(f"启用行配置 '{config_item.name}'")

                    if config_item.type == "specific":
                        # 指定特定行
                        target_rows = self._validate_indices(config_item.target_rows, total_rows, "行")
                        for row_idx in target_rows:
                            if row_idx not in occupied_rows:
                                height = self._get_random_value_from_range(config_item.height_range, random_state)
                                row_heights[row_idx] = height
                                occupied_rows.add(row_idx)
                                self.logger.info(f"应用行配置 '{config_item.name}' 到第{row_idx}行，高度设为{height}px")

                    elif config_item.type == "probabilistic":
                        # 概率触发
                        for row_idx in range(total_rows):
                            if row_idx not in occupied_rows and random_state.random() <= config_item.per_row_probability:
                                height = self._get_random_value_from_range(config_item.height_range, random_state)
                                row_heights[row_idx] = height
                                occupied_rows.add(row_idx)
                                self.logger.info(f"应用行配置 '{config_item.name}' 到第{row_idx}行，高度设为{height}px")

        # 处理列配置
        if sizing_config.col_configs:
            occupied_cols = set()  # 记录已被占用的列

            for config_item in sizing_config.col_configs:
                # 判断配置是否启用 - 修复：确保1.0概率时一定触发
                if random_state.random() <= config_item.probability:
                    self.logger.info(f"启用列配置 '{config_item.name}'")

                    if config_item.type == "specific":
                        # 指定特定列
                        target_cols = self._validate_indices(config_item.target_cols, total_cols, "列")
                        for col_idx in target_cols:
                            if col_idx not in occupied_cols:
                                width = self._get_random_value_from_range(config_item.width_range, random_state)
                                col_widths[col_idx] = width
                                occupied_cols.add(col_idx)
                                self.logger.info(f"应用列配置 '{config_item.name}' 到第{col_idx}列，宽度设为{width}px")

                    elif config_item.type == "probabilistic":
                        # 概率触发
                        for col_idx in range(total_cols):
                            if col_idx not in occupied_cols and random_state.random() <= config_item.per_col_probability:
                                width = self._get_random_value_from_range(config_item.width_range, random_state)
                                col_widths[col_idx] = width
                                occupied_cols.add(col_idx)
                                self.logger.info(f"应用列配置 '{config_item.name}' 到第{col_idx}列，宽度设为{width}px")

        return ResolvedSizingParams(
            row_heights=row_heights,
            col_widths=col_widths,
            default_row_height=default_row_height,
            default_col_width=default_col_width
        )

    def _validate_indices(self, indices, max_count, type_name):
        """
        验证索引列表，过滤无效索引

        Args:
            indices: 索引列表
            max_count: 最大索引数量
            type_name: 类型名称（用于日志）

        Returns:
            有效的索引列表
        """
        if not indices:
            return []

        valid_indices = []
        for idx in indices:
            # 处理负数索引
            if idx < 0:
                idx = max_count + idx

            if 0 <= idx < max_count:
                valid_indices.append(idx)
            else:
                self.logger.warning(f"配置中指定的{type_name}索引{idx}超出范围[0, {max_count-1}]，已忽略")

        return valid_indices

    def _get_random_value_from_range(self, value_range, random_state):
        """
        从范围中获取随机值

        Args:
            value_range: 值范围 [min, max]
            random_state: 随机状态

        Returns:
            随机值
        """
        if not value_range or len(value_range) != 2:
            raise ValueError("值范围必须是包含两个元素的列表 [min, max]")

        min_val, max_val = value_range
        if min_val == max_val:
            return min_val
        else:
            return random_state.randint(min_val, max_val + 1)

    def _resolve_postprocessing_params(self, postprocessing_config, random_state):
        """
        解析图像后处理参数

        Args:
            postprocessing_config: 后处理配置，可能为None
            random_state: 随机状态

        Returns:
            解析后的后处理参数，如果配置为None则返回None
        """
        if postprocessing_config is None:
            return None

        from .config import ResolvedPostprocessingParams

        # 解析模糊效果
        apply_blur = False
        blur_radius = None
        if postprocessing_config.blur is not None:
            if random_state.random() < postprocessing_config.blur.probability:
                apply_blur = True
                blur_radius = random_state.uniform(
                    postprocessing_config.blur.radius_range[0],
                    postprocessing_config.blur.radius_range[1]
                )

        # 解析噪声效果
        apply_noise = False
        noise_intensity = None
        if postprocessing_config.noise is not None:
            if random_state.random() < postprocessing_config.noise.probability:
                apply_noise = True
                noise_intensity = random_state.randint(
                    postprocessing_config.noise.intensity_range[0],
                    postprocessing_config.noise.intensity_range[1] + 1
                )

        # 解析透视变换效果
        apply_perspective = False
        perspective_offset_ratio = None
        if postprocessing_config.perspective is not None:
            if random_state.random() < postprocessing_config.perspective.probability:
                apply_perspective = True
                perspective_offset_ratio = postprocessing_config.perspective.max_offset_ratio

        # 解析背景图合成效果
        apply_background = False
        background_image_path = None
        max_scale_factor = None
        css_background_width = None
        css_background_height = None
        css_table_left = None
        css_table_top = None
        css_crop_width = None
        css_crop_height = None
        css_bg_offset_x = None
        css_bg_offset_y = None

        if postprocessing_config.background is not None:
            apply_background = True
            max_scale_factor = postprocessing_config.background.max_scale_factor

            self.logger.info(f"开始选择背景图，目录: {postprocessing_config.background.background_dirs}")

            # 选择背景图路径
            try:
                background_image_path = self._select_background_image(
                    postprocessing_config.background, random_state
                )
                self.logger.info(f"成功选择背景图: {background_image_path}")
            except Exception as e:
                self.logger.error(f"选择背景图失败: {e}")
                # 不要让背景图选择失败影响整个流程
                apply_background = False
                background_image_path = None

            # 计算CSS背景图参数
            self.logger.info(f"[CSS_DEBUG] 开始计算CSS参数，背景图路径: {background_image_path}")
            try:
                css_params = self._calculate_css_background_params(
                    background_image_path, postprocessing_config.background, random_state
                )
                css_background_width = css_params['background_width']
                css_background_height = css_params['background_height']
                css_table_left = css_params['table_left']
                css_table_top = css_params['table_top']
                css_crop_width = css_params['crop_width']
                css_crop_height = css_params['crop_height']
                css_bg_offset_x = css_params['bg_offset_x']
                css_bg_offset_y = css_params['bg_offset_y']
                self.logger.info(f"[CSS_DEBUG] CSS参数计算成功: {css_params}")
            except Exception as e:
                self.logger.error(f"[CSS_DEBUG] CSS参数计算失败: {e}")
                # 计算失败时设置默认值，但保持CSS模式
                css_background_width = 1200
                css_background_height = 800
                css_table_left = 200
                css_table_top = 150
                css_crop_width = 1000
                css_crop_height = 600
                css_bg_offset_x = 50   # 使用更保守的默认偏移
                css_bg_offset_y = 50   # 使用更保守的默认偏移
                self.logger.info(f"[CSS_DEBUG] 使用默认CSS参数，背景图路径保持: {background_image_path}")

        # V4.2新增：获取边距控制配置
        margin_control_config = None
        if apply_background and postprocessing_config.background and hasattr(postprocessing_config.background, 'margin_control'):
            margin_control_config = postprocessing_config.background.margin_control
            if margin_control_config:
                self.logger.info(f"[MARGIN_CONTROL] 边距控制配置已加载: {len(margin_control_config.range_list) if margin_control_config.range_list else 0} 个选项")

        # V4.3新增：解析表格透明度配置
        enable_transparency = False
        overall_transparency = 1.0
        if postprocessing_config.table_blending is not None:
            enable_transparency = postprocessing_config.table_blending.enable_transparency
            overall_transparency = postprocessing_config.table_blending.overall_transparency
            if enable_transparency:
                self.logger.info(f"[TRANSPARENCY] 表格透明度已启用，透明度级别: {overall_transparency}")

        # V4.5新增：解析降质效果配置
        apply_degradation_blur = False
        apply_degradation_noise = False
        apply_degradation_fade_global = False
        apply_degradation_fade_local = False
        apply_degradation_uneven_lighting = False
        apply_degradation_jpeg = False
        apply_degradation_darker_brighter = False
        apply_degradation_gamma_correction = False

        # 解析各种降质效果
        if postprocessing_config.degradation_blur is not None:
            if random_state.random() < postprocessing_config.degradation_blur.probability:
                apply_degradation_blur = True
                self.logger.info(f"[DEGRADATION] 降质模糊效果已启用")

        if postprocessing_config.degradation_noise is not None:
            if random_state.random() < postprocessing_config.degradation_noise.probability:
                apply_degradation_noise = True
                self.logger.info(f"[DEGRADATION] 降质噪声效果已启用")

        if postprocessing_config.degradation_fade_global is not None:
            if random_state.random() < postprocessing_config.degradation_fade_global.probability:
                apply_degradation_fade_global = True
                self.logger.info(f"[DEGRADATION] 全局褪色效果已启用")

        if postprocessing_config.degradation_fade_local is not None:
            if random_state.random() < postprocessing_config.degradation_fade_local.probability:
                apply_degradation_fade_local = True
                self.logger.info(f"[DEGRADATION] 局部褪色效果已启用")

        if postprocessing_config.degradation_uneven_lighting is not None:
            if random_state.random() < postprocessing_config.degradation_uneven_lighting.probability:
                apply_degradation_uneven_lighting = True
                self.logger.info(f"[DEGRADATION] 不均匀光照效果已启用")

        if postprocessing_config.degradation_jpeg is not None:
            if random_state.random() < postprocessing_config.degradation_jpeg.probability:
                apply_degradation_jpeg = True
                self.logger.info(f"[DEGRADATION] JPEG压缩效果已启用")

        if postprocessing_config.degradation_darker_brighter is not None:
            if random_state.random() < postprocessing_config.degradation_darker_brighter.probability:
                apply_degradation_darker_brighter = True
                self.logger.info(f"[DEGRADATION] 亮度/对比度调整效果已启用")

        if postprocessing_config.degradation_gamma_correction is not None:
            if random_state.random() < postprocessing_config.degradation_gamma_correction.probability:
                apply_degradation_gamma_correction = True
                self.logger.info(f"[DEGRADATION] 伽马校正效果已启用")

        # 创建最终的后处理参数
        resolved_params = ResolvedPostprocessingParams(
            apply_blur=apply_blur,
            blur_radius=blur_radius,
            apply_noise=apply_noise,
            noise_intensity=noise_intensity,
            apply_perspective=apply_perspective,
            perspective_offset_ratio=perspective_offset_ratio,
            apply_background=apply_background,
            background_image_path=background_image_path,
            max_scale_factor=max_scale_factor,
            css_background_width=css_background_width,
            css_background_height=css_background_height,
            css_table_left=css_table_left,
            css_table_top=css_table_top,
            css_crop_width=css_crop_width,
            css_crop_height=css_crop_height,
            css_bg_offset_x=css_bg_offset_x,
            css_bg_offset_y=css_bg_offset_y,
            margin_control=margin_control_config,
            # V4.3新增：透明度参数
            enable_transparency=enable_transparency,
            overall_transparency=overall_transparency,
            # V4.5新增：降质效果参数
            apply_degradation_blur=apply_degradation_blur,
            apply_degradation_noise=apply_degradation_noise,
            apply_degradation_fade_global=apply_degradation_fade_global,
            apply_degradation_fade_local=apply_degradation_fade_local,
            apply_degradation_uneven_lighting=apply_degradation_uneven_lighting,
            apply_degradation_jpeg=apply_degradation_jpeg,
            apply_degradation_darker_brighter=apply_degradation_darker_brighter,
            apply_degradation_gamma_correction=apply_degradation_gamma_correction
        )

        # 记录关键的CSS渲染参数
        if apply_background:
            self.logger.info(f"[CSS_DEBUG] CSS渲染模式参数:")
            self.logger.info(f"[CSS_DEBUG]   背景图: {os.path.basename(background_image_path) if background_image_path else 'None'}")
            self.logger.info(f"[CSS_DEBUG]   输出尺寸: {css_crop_width}x{css_crop_height}")
            self.logger.info(f"[CSS_DEBUG]   表格位置: ({css_table_left}, {css_table_top})")

        return resolved_params

    def _detect_style_conflicts(self, style_config, resolved_header, resolved_body):
        """
        V4.0新增：检测样式冲突并记录警告日志

        Args:
            style_config: 原始样式配置
            resolved_header: 解析后的表头样式
            resolved_body: 解析后的表体样式
        """
        # 检测表头和表体的颜色冲突
        if (resolved_header.text_color == resolved_body.text_color and
            resolved_header.background_color == resolved_body.background_color):
            self.logger.warning(
                f"样式冲突：表头和表体使用了相同的颜色组合 "
                f"(文本色: {resolved_header.text_color}, 背景色: {resolved_header.background_color})"
            )

        # 检测字体冲突
        if (resolved_header.font.default_family == resolved_body.font.default_family and
            resolved_header.font.default_size == resolved_body.font.default_size):
            self.logger.warning(
                f"样式冲突：表头和表体使用了相同的字体配置 "
                f"(字体: {resolved_header.font.default_family}, 大小: {resolved_header.font.default_size}px)"
            )

        # 检测对齐方式冲突
        if (resolved_header.horizontal_align == resolved_body.horizontal_align and
            resolved_header.vertical_align == resolved_body.vertical_align):
            self.logger.warning(
                f"样式冲突：表头和表体使用了相同的对齐方式 "
                f"(水平: {resolved_header.horizontal_align}, 垂直: {resolved_header.vertical_align})"
            )

        # 检测分层样式覆盖冲突
        if style_config.hierarchical:
            hierarchical = style_config.hierarchical

            # 检测列样式覆盖
            if hierarchical.column_styles:
                for col_idx, col_style in hierarchical.column_styles.items():
                    if col_style.text_color or col_style.background_color:
                        self.logger.warning(
                            f"样式冲突：列 {col_idx} 的分层样式覆盖了全局颜色配置"
                        )

            # 检测行样式覆盖
            if hierarchical.row_styles:
                for row_idx, row_style in hierarchical.row_styles.items():
                    if row_style.text_color or row_style.background_color:
                        self.logger.warning(
                            f"样式冲突：行 {row_idx} 的分层样式覆盖了全局颜色配置"
                        )

            # 检测单元格样式覆盖
            if hierarchical.cell_styles:
                for cell_id, cell_style in hierarchical.cell_styles.items():
                    if cell_style.text_color or cell_style.background_color:
                        self.logger.warning(
                            f"样式冲突：单元格 {cell_id} 的分层样式覆盖了全局颜色配置"
                        )

    def _select_background_image(self, background_config, random_state):
        """
        选择背景图路径

        Args:
            background_config: 背景图配置
            random_state: 随机状态

        Returns:
            选中的背景图文件路径
        """
        import os
        import glob

        # 收集所有背景图文件
        all_background_files = []

        self.logger.debug(f"开始扫描背景图目录，共 {len(background_config.background_dirs)} 个目录")

        for i, bg_dir in enumerate(background_config.background_dirs):
            self.logger.debug(f"检查目录 {i+1}: {bg_dir}")
            if not os.path.exists(bg_dir):
                self.logger.warning(f"背景图目录不存在: {bg_dir}")
                continue

            self.logger.debug(f"目录存在: {bg_dir}")

            # 支持常见图片格式
            image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.tiff', '*.webp']
            dir_files = []

            for ext in image_extensions:
                pattern = os.path.join(bg_dir, ext)
                dir_files.extend(glob.glob(pattern))
                # 同时支持大写扩展名
                pattern_upper = os.path.join(bg_dir, ext.upper())
                dir_files.extend(glob.glob(pattern_upper))

            self.logger.debug(f"目录 {bg_dir} 中找到 {len(dir_files)} 个图片文件")
            if dir_files:
                self.logger.debug(f"找到的文件: {dir_files[:5]}...")  # 只显示前5个文件
            else:
                self.logger.warning(f"背景图目录中没有找到图片文件: {bg_dir}")
                continue

            # 根据目录概率权重添加文件
            if i < len(background_config.background_dir_probabilities):
                dir_probability = background_config.background_dir_probabilities[i]
                # 将目录中的每个文件都按目录概率加权
                weighted_files = [(f, dir_probability / len(dir_files)) for f in dir_files]
                all_background_files.extend(weighted_files)
            else:
                # 如果没有指定概率，使用等权重
                equal_weight = 1.0 / len(background_config.background_dirs) / len(dir_files)
                weighted_files = [(f, equal_weight) for f in dir_files]
                all_background_files.extend(weighted_files)

        self.logger.debug(f"总共收集到 {len(all_background_files)} 个背景图文件")
        if not all_background_files:
            raise ValueError("没有找到可用的背景图文件")

        # 根据权重随机选择
        files, weights = zip(*all_background_files)
        # 归一化权重
        total_weight = sum(weights)
        normalized_weights = [w / total_weight for w in weights]

        # 使用numpy的choice进行加权随机选择
        import numpy as np
        selected_index = random_state.choice(len(files), p=normalized_weights)
        selected_file = files[selected_index]

        self.logger.debug(f"选择背景图: {selected_file}")
        return selected_file



    def _calculate_css_background_params(self, background_path, background_config, random_state):
        """
        计算CSS背景图渲染参数

        Args:
            background_path: 背景图路径
            background_config: 背景图配置
            random_state: 随机状态

        Returns:
            CSS渲染参数字典
        """
        try:
            from PIL import Image

            # 获取背景图原始尺寸
            with Image.open(background_path) as img:
                bg_width, bg_height = img.size

            # 计算背景图缩放（如果需要）
            scale_factor = random_state.uniform(1.0, background_config.max_scale_factor)
            final_bg_width = int(bg_width * scale_factor)
            final_bg_height = int(bg_height * scale_factor)

            # 估算表格尺寸（基于配置参数）
            # 这是一个粗略估算，用于确保有足够空间
            estimated_table_width = self._estimate_table_width(self.current_config)
            estimated_table_height = self._estimate_table_height(self.current_config)

            self.logger.debug(f"估算表格尺寸: {estimated_table_width}x{estimated_table_height}")

            # V4.2简化：移除复杂的边距控制逻辑，改为后期裁剪
            # 现在只需要确保表格能完整显示在背景图上
            min_crop_width = max(estimated_table_width + 100, int(final_bg_width * 0.6))
            max_crop_width = min(1200, final_bg_width)
            crop_width = random_state.randint(min_crop_width, max_crop_width + 1)

            min_crop_height = max(estimated_table_height + 100, int(final_bg_height * 0.6))
            max_crop_height = min(800, final_bg_height)
            crop_height = random_state.randint(min_crop_height, max_crop_height + 1)

            # 计算表格在背景中的位置（随机），确保不会截断
            max_table_left = max(50, crop_width - estimated_table_width - 50)
            max_table_top = max(50, crop_height - estimated_table_height - 50)

            # 确保有有效的位置范围
            if max_table_left < 50:
                max_table_left = 50
                self.logger.warning(f"裁剪宽度 {crop_width} 可能不足以容纳表格宽度 {estimated_table_width}")

            if max_table_top < 50:
                max_table_top = 50
                self.logger.warning(f"裁剪高度 {crop_height} 可能不足以容纳表格高度 {estimated_table_height}")

            table_left = random_state.randint(50, max_table_left + 1)
            table_top = random_state.randint(50, max_table_top + 1)

            self.logger.debug(f"表格完整渲染: 裁剪尺寸={crop_width}x{crop_height}, 表格位置=({table_left}, {table_top})")

            # 计算背景图的随机裁剪偏移
            # 确保背景图的可见部分仍然覆盖整个容器
            bg_offset_x = 0
            bg_offset_y = 0

            if final_bg_width > crop_width:
                # 最大偏移应该确保背景图右边缘不超出容器右边缘
                max_bg_offset_x = final_bg_width - crop_width
                # 使用更保守的偏移范围，确保背景图可见
                safe_max_offset_x = min(max_bg_offset_x, final_bg_width // 4)  # 最多偏移背景图宽度的1/4
                bg_offset_x = random_state.randint(0, safe_max_offset_x)
                self.logger.debug(f"背景图X偏移: {bg_offset_x} (最大安全偏移: {safe_max_offset_x})")

            if final_bg_height > crop_height:
                max_bg_offset_y = final_bg_height - crop_height
                safe_max_offset_y = min(max_bg_offset_y, final_bg_height // 4)  # 最多偏移背景图高度的1/4
                bg_offset_y = random_state.randint(0, safe_max_offset_y)
                self.logger.debug(f"背景图Y偏移: {bg_offset_y} (最大安全偏移: {safe_max_offset_y})")

            params = {
                'background_width': final_bg_width,
                'background_height': final_bg_height,
                'table_left': table_left,
                'table_top': table_top,
                'crop_width': crop_width,
                'crop_height': crop_height,
                'bg_offset_x': bg_offset_x,
                'bg_offset_y': bg_offset_y
            }

            self.logger.debug(f"CSS背景参数: {params}")
            return params

        except Exception as e:
            self.logger.error(f"计算CSS背景参数失败: {e}")
            # 返回默认参数（使用更保守的偏移）
            return {
                'background_width': 1200,
                'background_height': 800,
                'table_left': 200,
                'table_top': 150,
                'crop_width': 1000,
                'crop_height': 600,
                'bg_offset_x': 50,   # 更保守的偏移
                'bg_offset_y': 50    # 更保守的偏移
            }

    def _estimate_table_width(self, config) -> int:
        """
        估算表格宽度（V4.2改进：更准确的尺寸估算）

        基于配置参数更准确地估算表格的渲染宽度
        """
        # 获取表格结构配置
        structure_config = config.structure
        cols = structure_config.cols

        # 获取样式配置以更准确估算
        style_config = config.style

        # 估算单列宽度
        sizing_config = style_config.sizing
        default_col_width = sizing_config.default_col_width

        if default_col_width != "auto":
            try:
                if isinstance(default_col_width, int):
                    col_width = default_col_width
                else:
                    col_width = int(default_col_width)
            except:
                col_width = 150  # 提高默认列宽估算
        else:
            # 基于字体大小和内容类型估算列宽
            font_size = getattr(style_config.common.font, 'default_size', 14)
            if isinstance(font_size, dict):
                # 如果是概率化配置，取平均值
                font_size = 14  # 回退到默认值

            # 根据内容类型调整列宽估算
            content_config = config.content
            if hasattr(content_config, 'programmatic_types'):
                content_types = content_config.programmatic_types
                if 'currency' in content_types or 'percentage' in content_types:
                    col_width = int(font_size * 10)  # 数值类型需要更多空间
                elif 'date' in content_types:
                    col_width = int(font_size * 8)   # 日期类型中等空间
                else:
                    col_width = int(font_size * 9)   # 文本类型标准空间
            else:
                col_width = int(font_size * 8)

        # 考虑内边距的影响
        padding = getattr(style_config.common, 'padding', 8)
        if isinstance(padding, dict):
            padding = 8  # 回退到默认值

        # 估算总宽度：列数 * (列宽 + 内边距*2) + 边框
        estimated_width = cols * (col_width + padding * 2) + (cols + 1) * 2  # 2px边框

        # 考虑透视变换可能增加的尺寸（约15%，更保守）
        estimated_width = int(estimated_width * 1.15)

        # 增加安全边距（20%）
        estimated_width = int(estimated_width * 1.2)

        return max(estimated_width, 500)  # 提高最小宽度

    def _estimate_table_height(self, config) -> int:
        """
        估算表格高度（V4.2改进：更准确的尺寸估算）

        基于配置参数更准确地估算表格的渲染高度
        """
        # 获取表格结构配置
        structure_config = config.structure
        header_rows = structure_config.header_rows
        body_rows = structure_config.body_rows
        total_rows = header_rows + body_rows

        # 获取样式配置以更准确估算
        style_config = config.style

        # 从样式配置中获取行高设置
        sizing_config = style_config.sizing
        default_row_height = sizing_config.default_row_height

        if default_row_height != "auto":
            try:
                if isinstance(default_row_height, int):
                    row_height = default_row_height
                else:
                    row_height = int(default_row_height)
            except:
                # 基于字体大小估算行高
                font_size = getattr(style_config.common.font, 'default_size', 14)
                if isinstance(font_size, dict):
                    font_size = 14  # 回退到默认值
                row_height = int(font_size * 2.5)  # 2.5倍字体大小
        else:
            # 基于字体大小和内边距估算行高
            font_size = getattr(style_config.common.font, 'default_size', 14)
            if isinstance(font_size, dict):
                font_size = 14  # 回退到默认值

            padding = getattr(style_config.common, 'padding', 8)
            if isinstance(padding, dict):
                padding = 8  # 回退到默认值

            # 更准确的行高估算：字体大小 + 内边距*2 + 行间距
            row_height = int(font_size * 1.4 + padding * 2 + 4)

        # 头部行可能更高（通常字体更大或加粗）
        header_height = int(row_height * 1.3) if header_rows > 0 else row_height

        # 估算总高度：头部高度 + 主体高度 + 边框
        estimated_height = header_height * header_rows + row_height * body_rows + (total_rows + 1) * 2

        # 考虑透视变换可能增加的尺寸（约15%，更保守）
        estimated_height = int(estimated_height * 1.15)

        # 增加安全边距（20%）
        estimated_height = int(estimated_height * 1.2)

        return max(estimated_height, 400)  # 提高最小高度
