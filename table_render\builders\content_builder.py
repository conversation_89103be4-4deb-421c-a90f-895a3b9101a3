"""
内容生成器

负责为表格结构填充内容。
"""

import csv
import logging
import numpy as np
from pathlib import Path
from faker import Faker

from ..models import TableModel
from ..config import ContentConfig, ResolvedContentParams


class ContentBuilder:
    """
    内容生成器类
    
    为已有的表格结构填充简单的占位内容。
    """
    
    def __init__(self, seed: int):
        """
        初始化内容生成器

        Args:
            seed: 随机种子，用于确保可复现性
        """
        self.random_state = np.random.RandomState(seed)
        self.faker = Faker()
        self.faker.seed_instance(seed)
        self.logger = logging.getLogger(__name__)
        
    def build(self, table_model: TableModel, config: ResolvedContentParams) -> TableModel:
        """
        为表格模型填充内容

        Args:
            table_model: 由StructureBuilder生成的、仅有结构的表格模型
            config: 解析后的内容参数

        Returns:
            填充了内容的表格模型
        """
        self.logger.debug(f"开始填充表格内容，使用源类型: {config.source_type}")

        # 根据配置的 source_type 分派到不同的处理函数
        if config.source_type == 'programmatic':
            self._fill_programmatically(table_model, config)
        elif config.source_type == 'csv':
            if config.csv_file_path is None:
                raise ValueError("CSV source is selected but 'csv_file_path' is missing.")
            self._fill_from_csv(table_model, config)
        else:
            raise NotImplementedError(f"Content source type '{config.source_type}' is not supported.")

        self.logger.debug("表格内容填充完成")
        return table_model

    def _fill_programmatically(self, table_model: TableModel, config: ResolvedContentParams):
        """
        使用程序化方法填充表格内容

        Args:
            table_model: 表格模型
            config: 解析后的内容参数
        """
        # 遍历所有单元格并填充内容
        for row in table_model.rows:
            for cell in row.cells:
                if cell.is_header:
                    # 为表头单元格生成列标题
                    cell.content = f"Column {cell.col_index + 1}"
                else:
                    # 为数据单元格生成格式化内容
                    cell.content = self._get_random_programmatic_content(config.programmatic_types)

    def _get_random_programmatic_content(self, types: list) -> str:
        """
        生成随机的程序化内容

        Args:
            types: 支持的内容类型列表

        Returns:
            生成的内容字符串
        """
        if not types:
            return f"Data {self.random_state.randint(1, 1000)}"

        content_type = self.random_state.choice(types)

        if content_type == 'date':
            return self.faker.date()
        elif content_type == 'currency':
            amount = self.random_state.uniform(10.0, 10000.0)
            return f"${amount:.2f}"
        elif content_type == 'percentage':
            percent = self.random_state.uniform(0.0, 100.0)
            return f"{percent:.1f}%"
        elif content_type == 'text':
            # V4.0新增：生成长文本用于测试内容溢出
            return self.faker.sentence(nb_words=self.random_state.randint(5, 15))
        else:
            # 默认生成简单的数字内容
            return f"Value {self.random_state.randint(1, 1000)}"

    def _fill_from_csv(self, table_model: TableModel, config: ResolvedContentParams):
        """
        从CSV文件填充表格内容

        Args:
            table_model: 表格模型
            config: 解析后的内容参数
        """
        # 加载CSV数据
        csv_data = self._load_csv_data(config)
        num_csv_rows = len(csv_data)
        num_csv_cols = len(csv_data[0]) if num_csv_rows > 0 else 0

        self.logger.debug(f"加载CSV数据: {num_csv_rows}x{num_csv_cols}")

        # 遍历 TableModel 填充内容
        for row in table_model.rows:
            for cell in row.cells:
                r_idx = cell.row_index
                c_idx = cell.col_index

                # 检查CSV数据源是否越界
                if r_idx < num_csv_rows and c_idx < num_csv_cols:
                    cell.content = csv_data[r_idx][c_idx]
                else:
                    # 处理行列数不匹配的情况
                    strategy = config.csv_mismatch_strategy
                    if strategy == 'fill_empty':
                        cell.content = ""
                    # 如果是 'truncate'，则不执行任何操作，内容将保持默认的空字符串

    def _load_csv_data(self, config: ResolvedContentParams) -> list:
        """
        加载CSV文件数据

        Args:
            config: 解析后的内容参数

        Returns:
            二维列表形式的CSV数据
        """
        csv_path = Path(config.csv_file_path)
        if not csv_path.exists():
            raise FileNotFoundError(f"CSV文件未找到: {config.csv_file_path}")

        csv_data = []
        with open(csv_path, 'r', encoding=config.csv_encoding) as f:
            reader = csv.reader(f)
            for row in reader:
                csv_data.append(row)

        self.logger.debug(f"成功加载CSV文件: {config.csv_file_path}")
        return csv_data
