import numpy as np
import cv2 as cv
from typing import List

# Labeler
''' labels to be generated includes the following
- Number of tables - This is counted by counting the number of connected components in the mask (table regions should be disjoint regions of white pixels)
- Bounding boxes (of tables) - Generated by detecting external contours and returning nounding box
- XML strings of structure - latex string representation as arg yo structure generator '''
class Labeler:

    ''' expects some structure generator '''
    def __init__(self,structure_generator):
        self.structure_generator = structure_generator
    
    ''' number of tables from table mask '''
    def number_of_tables(self,table_mask)->int:
        num,_ = cv.connectedComponents(table_mask)
        return num-1
    
    ''' sort the contours top to bottom '''
    def sort(self,contours):
        y_values = []
        for contour in contours:
            x,y,w,h = cv.boundingRect(contour)
            y_values.append(y)
        y_values = np.array(y_values)
        idx = np.argsort(y_values)
        sorted_contours = [contours[i] for i in idx]
        return sorted_contours
    
    ''' bounding boxes '''
    def bounding_boxes(self,table_mask):
        contours , _ = cv.findContours(table_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)
        sorted_contours = self.sort(contours)
        boxes = []
        for contour in sorted_contours:
            box = cv.boundingRect(contour)
            boxes.append(box)
        return boxes
    
    ''' generate label '''
    def label(self,table_mask,tables:List[str]):
        num_tables = len(tables)
        bboxes = self.bounding_boxes(table_mask)
        bboxes_data = [bbox for bbox in bboxes]
        structures = self.structure_generator.structures(tables)
        result = {'no':num_tables,'bounding_boxes':bboxes_data,'structures':structures}
        return result