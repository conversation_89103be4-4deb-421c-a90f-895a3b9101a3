## 调用链

### 节点(main)
**所在代码文件**: `table_render/main.py`

**用途**: 
作为TableRender工具的命令行入口。负责解析用户输入的参数（配置文件和样本数量），加载和验证配置，初始化核心生成器，并启动整个表格图像的生成流程。

**输入参数**:
- `config_file` / `--config`: 用户通过命令行指定的YAML配置文件路径。
- `--num-samples`: 用户通过命令行指定的希望生成的图像数量。

**输出说明**:
该函数没有直接的返回值。其主要作用是产生副作用：在配置指定的输出目录中生成图像文件和对应的标注文件。

**实现流程**:
```mermaid
sequenceDiagram
    participant User
    participant main as "main()"
    participant load_config as "load_config()"
    participant generator as MainGenerator

    User->>main: Command line execution
    main->>load_config: config_path
    load_config-->>main: render_config
    main->>generator: Create instance with config
    generator-->>main: generator instance
    main->>generator: generate(num_samples)
```

### 节点(MainGenerator.generate)
**所在代码文件**: `table_render/main_generator.py`

**用途**: 
作为核心协调器，负责按顺序执行完整的表格图像生成流水线。它为每个样本生成一个独立的随机种子，并调用各个子模块（Resolver, Builders, Renderer）来完成从配置解析到最终文件保存的全过程。

**输入参数**:
- `num_samples` (int): 需要生成的表格图像样本数量。

**输出说明**:
该方法没有直接的返回值。它将生成的图像、标注和元数据文件保存到由配置指定的输出目录中。

**实现流程**:
```mermaid
flowchart TD
    A["开始"] --> B{"循环 N 次 (num_samples)"};
    B -- "进行中" --> C["生成样本随机种子"];
    C --> D["调用 Resolver.resolve 解析具体参数"];
    D --> E["调用 StructureBuilder.build 构建表格结构"];
    E --> F["调用 ContentBuilder.build 填充内容"];
    F --> G["调用 StyleBuilder.build 生成CSS"];
    G --> H["调用 HtmlRenderer.render 渲染图片和标注"];
    H --> I["调用 AnnotationConverter.convert 转换标注格式"];
    I --> J["调用 FileUtils.save_sample 保存文件"];
    J --> B;
    B -- "循环完成" --> K["结束"];
```

### 节点(Resolver.resolve)
**所在代码文件**: `table_render/resolver.py`

**用途**: 
将用户提供的、包含概率和范围的抽象配置 (`RenderConfig`)，解析成一组在本次生成中完全确定的、可执行的具体参数 (`ResolvedParams`)。这是实现“一次配置，多样输出”的关键模块。

**输入参数**:
- `config` (RenderConfig): 原始的、经过验证的渲染配置对象。
- `seed` (int): 用于本次解析所有随机决策的种子，确保结果的可复现性。

**输出说明**:
- `ResolvedParams`: 一个包含所有具体参数的数据对象，例如确定的行数、列数、字体、颜色等。

**实现流程**:
```mermaid
sequenceDiagram
    participant MainGenerator
    participant Resolver
    participant StyleInheritanceManager
    participant ColorManager
    participant FontManager

    MainGenerator->>Resolver: resolve(config, seed)
    Resolver->>Resolver: _resolve_structure_params(...)
    Resolver->>Resolver: _resolve_content_params(...)
    Resolver->>StyleInheritanceManager: apply_inheritance(...)
    StyleInheritanceManager-->>Resolver: header_style_dict, body_style_dict
    Resolver->>ColorManager: get_color_pair(...)
    Resolver->>FontManager: get_weighted_random_directory(...)
    Resolver->>FontManager: select_safe_font(...)
    Resolver-->>MainGenerator: resolved_params
```

### 节点(StructureBuilder.build)
**所在代码文件**: `table_render/builders/structure_builder.py`

**用途**: 
根据确定的结构参数，生成一个包含完整行列布局、单元格合并信息和边框定义的表格逻辑模型 (`TableModel`)。这个模型是后续内容填充和样式渲染的基础。

**输入参数**:
- `config` (ResolvedStructureParams): 由Resolver输出的、包含确定行数、列数、合并概率等信息的结构参数。
- `border_mode` (str): 边框模式 ('full', 'none', 'semi')。
- `border_details` (dict): 当模式为 'semi' 时的详细边框配置。

**输出说明**:
- `TableModel`: 一个内容为空，但结构（行列、合并、边框）已经完全定义好的表格模型对象。

**实现流程**:
```mermaid
flowchart TD
    A["开始"] --> B["创建基础的行列网格"];
    B --> C{"是否需要合并? (merge_probability > 0)"};
    C -- "是" --> D["在表头和表体区域内分别执行单元格合并"];
    C -- "否" --> E1;
    D --> E1["为每个单元格分配边框样式"];
    E1 --> F["返回 TableModel"];
    F --> G["结束"];
```

### 节点(ContentBuilder.build)
**所在代码文件**: `table_render/builders/content_builder.py`

**用途**: 
根据配置的数据源类型（如CSV文件或程序化生成），为已经构建好结构的 `TableModel` 填充实际内容。

**输入参数**:
- `table_model` (TableModel): 由 `StructureBuilder` 生成的、仅有结构的表格模型。
- `config` (ResolvedContentParams): 包含数据源类型和具体路径等信息的已解析内容参数。

**输出说明**:
- `TableModel`: 一个结构和内容都已填充完毕的表格模型对象。

**实现流程**:
```mermaid
flowchart TD
    A["开始"] --> B{"数据源类型是?"};
    B -- "CSV" --> C["从CSV文件加载数据"];
    C --> D["将CSV数据填充到TableModel"];
    D --> Z["结束"];
    B -- "Programmatic" --> E["初始化单元格遍历"];
    E --> E_LOOP{"是否还有未填充单元格?"};
    E_LOOP -- "是" --> F{"是表头?"};
    F -- "是" --> G["生成'Column X'标题"];
    F -- "否" --> H["生成随机内容(日期/货币等)"];
    G --> E_LOOP;
    H --> E_LOOP;
    E_LOOP -- "否" --> I["返回TableModel"];
    I --> Z;
```

### 节点(StyleBuilder.build)
**所在代码文件**: `table_render/builders/style_builder.py`

**用途**: 
根据解析后的样式参数和表格模型中的边框决策，生成一份完整的、可直接用于渲染的CSS样式字符串。

**输入参数**:
- `config` (ResolvedStyleParams): 包含字体、颜色、对齐等所有已确定的样式参数。
- `table_model` (TableModel): 包含每个单元格精确边框信息的表格模型。

**输出说明**:
- `str`: 一个包含所有CSS规则的字符串。

**实现流程**:
```mermaid
flowchart TD
    A["开始"] --> B["生成 @font-face 规则"];
    B --> C["生成全局/表头/表体基础样式"];
    C --> D{"启用斑马条纹?"};
    D -- "是" --> E["生成奇偶行背景色样式"];
    D -- "否" --> F1;
    E --> F1["生成分层/覆盖样式(行/列/单元格)"];
    F1 --> G["生成尺寸样式(行高/列宽)"];
    G --> H["根据TableModel生成精确的单元格边框样式"];
    H --> I["组合所有CSS规则"];
    I --> J["返回CSS字符串"];
    J --> K["结束"];
```

### 节点(HtmlRenderer.render)
**所在代码文件**: `table_render/renderers/html_renderer.py`

**用途**: 
异步地将 `TableModel` 和 CSS 转换为 HTML 页面，在无头浏览器中渲染该页面，对表格进行截图，并提取单元格级别的标注信息（如边界框和内容）。

**输入参数**:
- `table_model` (TableModel): 包含结构和内容的完整表格模型。
- `css_string` (str): 由 `StyleBuilder` 生成的完整 CSS 样式字符串。

**输出说明**:
- `Tuple[bytes, Dict]`: 一个元组，包含渲染出的 PNG 图像字节数据和一份包含所有单元格标注信息的字典。

**实现流程**:
```mermaid
flowchart TD
    A["开始"] --> B["将TableModel和CSS转换为HTML字符串"];
    B --> C["在无头浏览器中打开新页面"];
    C --> D["将HTML内容设置到页面中"];
    D --> E["在页面中执行JS脚本以提取标注(BBox, content)"];
    E --> F["对HTML表格元素进行截图"];
    F --> G["关闭页面"];
    G --> H["返回(图像字节, 标注字典)"];
    H --> I["结束"];
```

### 节点(AnnotationConverter.convert_to_final_format)
**所在代码文件**: `table_render/utils/annotation_converter.py`

**用途**: 
将从浏览器获取的原始标注（物理位置、内容）与 `TableModel` 中的逻辑信息（行列合并、边框样式、表头状态）相结合，生成最终符合项目数据规范的结构化标注文件。

**输入参数**:
- `raw_annotations` (Dict): 从 `HtmlRenderer` 获取的原始标注数据。
- `table_model` (TableModel): 包含完整逻辑信息的表格模型。
- `image_filename` (str): 关联的图像文件名。

**输出说明**:
- `Dict`: 一个符合最终格式要求的、包含所有单元格详细标注的字典。

**实现流程**:
```mermaid
flowchart TD
    A[开始] --> B[创建 cell_id -> CellModel 的映射];
    B --> C[初始化最终标注字典结构];
    C --> D{遍历每个原始单元格标注};
    D -- 有 --> E[查找对应的CellModel];
    E --> F[转换BBox格式];
    F --> G[组合物理信息和逻辑信息];
    G --> H[将格式化后的单元格标注添加到列表];
    H --> D;
    D -- 结束 --> I[返回最终的标注字典];
    I --> J[结束];
```

### 节点(FileUtils.save_sample)
**所在代码文件**: `table_render/utils/file_utils.py`

**用途**: 
作为一个高级接口，负责将单个生成样本的所有相关文件（图像、标注、元数据）以统一的命名规范保存到各自指定的输出子目录中。

**输入参数**:
- `sample_index` (int): 当前样本的索引号。
- `image_bytes` (bytes): 渲染出的图像数据。
- `annotations` (Dict): 格式化后的标注数据。
- `metadata` (Dict): 本次生成的配置元数据。
- `output_dirs` (Dict): 包含 `images`、`annotations`、`metadata` 目录路径的字典。

**输出说明**:
- 无返回值。该方法在文件系统上产生副作用（创建文件）。

**实现流程**:
```mermaid
flowchart TD
    A[开始] --> B[根据样本索引生成基础文件名];
    B --> C[调用 save_image 保存图像文件];
    C --> D[调用 save_json 保存标注文件];
    D --> E[调用 save_json 保存元数据文件];
    E --> F[结束];
```

## 整体说明

### 用途
本调用链完整地描述了 `TableRender` v3.3.5 版本从接收用户输入（配置文件路径和样本数量）到最终在文件系统中生成符合规范的表格图像及其对应标注文件的全过程。它以 `MainGenerator` 为核心，通过一系列定义明确的模块化组件（解析器、构建器、渲染器、转换器），将一个高度可配置的、基于概率的表格定义，逐步实例化为一个具体的、可视化的、带有精确标注的数据样本。

### 涉及的目录/文件
- `table_render/main.py`: 命令行入口。
- `table_render/main_generator.py`: 核心协调器。
- `table_render/resolver.py`: 配置解析器。
- `table_render/builders/structure_builder.py`: 表格结构构建器。
- `table_render/builders/content_builder.py`: 表格内容构建器。
- `table_render/builders/style_builder.py`: CSS样式构建器。
- `table_render/renderers/html_renderer.py`: HTML渲染及标注提取器。
- `table_render/utils/annotation_converter.py`: 标注格式转换器。
- `table_render/utils/file_utils.py`: 文件保存工具。

### 整体调用时序图

```mermaid
sequenceDiagram
    participant main as "main()"
    participant generator as MainGenerator
    participant resolver as Resolver
    participant s_builder as StructureBuilder
    participant c_builder as ContentBuilder
    participant style_builder as StyleBuilder
    participant renderer as HtmlRenderer
    participant converter as AnnotationConverter
    participant file_utils as FileUtils

    main->>generator: generate(N)
    loop for N samples
        generator->>resolver: resolve(config, seed)
        resolver-->>generator: resolved_params

        generator->>s_builder: build(params.structure)
        s_builder-->>generator: table_model

        generator->>c_builder: build(table_model, params.content)
        c_builder-->>generator: table_model_with_content

        generator->>style_builder: build(params.style, table_model)
        style_builder-->>generator: css_string

        generator->>renderer: render(table_model, css_string)
        renderer-->>generator: image_bytes, raw_annotations

        generator->>converter: convert_to_final_format(raw_annotations, table_model)
        converter-->>generator: final_annotations

        generator->>file_utils: save_sample(image, annotations, metadata)
    end
```








