{"latex": {"sample_size": 10, "types": [0, 1, 2, 3, 4, 5, 6, 7], "types_map": {"0": "Borderless", "1": "Bordered Header", "2": "Bordered Header Bottom", "3": "Bordered Internal Columns", "4": "Bordered Columns", "5": "Partially Bordered", "6": "Bordered", "7": "Embedded"}, "img_path": "data/latex/imgs/", "mask_path": "data/latex/masks/", "annotation_path": "data/latex/annotations/"}, "word": {"sample_size": 10, "types": [0, 1, 2, 3, 4], "types_map": {"0": "Top Bottom", "1": "Header Bottom", "2": "Partially Bordered", "3": "Bordered", "4": "Embedded"}, "img_path": "data/word/imgs/", "mask_path": "data/word/masks/", "annotation_path": "data/word/annotations/"}, "html": {"sample_size": 10, "types": [0, 1, 2, 3, 4, 5, 6, 7], "types_map": {"0": "Borderless", "1": "Bordered Header", "2": "Bordered Header Bottom", "3": "Bordered Internal Columns", "4": "Striped Rows", "5": "Partially Bordered", "6": "Bordered", "7": "Embedded"}, "img_path": "data/html/imgs/", "mask_path": "data/html/masks/", "annotation_path": "data/html/annotations/"}, "parallel": true}