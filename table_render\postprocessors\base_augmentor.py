"""
基础图像增强器

V4.0新增：定义图像后处理的抽象基类。
"""

from abc import ABC, abstractmethod
from PIL import Image
from typing import Optional, Dict, Any, Tuple

from ..config import ResolvedPostprocessingParams


class BaseAugmentor(ABC):
    """
    图像增强器抽象基类

    定义了图像后处理的标准接口。所有具体的增强器都应该继承此类。
    """

    @abstractmethod
    def process(self,
               image_bytes: bytes,
               annotations: Optional[Dict[str, Any]] = None,
               params: Optional[ResolvedPostprocessingParams] = None) -> Tuple[bytes, Optional[Dict[str, Any]]]:
        """
        处理图像和标注

        Args:
            image_bytes: 原始图像的字节数据
            annotations: 最终格式的标注数据（包含p1,p2,p3,p4格式），如果为None则不处理标注
            params: 解析后的后处理参数，如果为None则不进行任何处理

        Returns:
            (处理后的图像字节数据, 处理后的标注数据)
        """
        pass

    @abstractmethod
    def augment(self,
               image: Image.Image,
               annotations: Optional[Dict[str, Any]] = None,
               params: ResolvedPostprocessingParams = None) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        对PIL图像对象和标注进行增强处理

        Args:
            image: PIL图像对象
            annotations: 最终格式的标注数据
            params: 解析后的后处理参数

        Returns:
            (增强后的PIL图像对象, 增强后的标注数据)
        """
        pass
