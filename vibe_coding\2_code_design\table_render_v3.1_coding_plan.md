# TableRender V3.1: 架构重构与概率化生成 - 开发计划

**目标:** 响应V3版本效果不如预期的反馈，对核心架构进行重构。实现表头/主体分离，并引入“泛化配置 -> 具体元数据”的工作流，以生成风格多样且结构真实可复现的表格。

---

## 1. 项目结构与受影响模块

本次开发是一次影响深远的重构，将引入新的核心组件并修改大部分现有模块。

```
TableRender/
└── table_render/           # 核心Python包
    ├── __init__.py
    ├── main.py             # (轻微修改) 适配新的日志和调用
    ├── config.py           # (重大修改) 引入概率化配置和全新的ResolvedParams模型
    ├── main_generator.py   # (重大修改) 实现新的核心工作流，调用Resolver
    ├── resolver.py         # (新增) 新增的核心“决策者”模块
    ├── models/             # (受影响)
    │   └── table_model.py  # (主要修改) 引入表头/主体的结构分离
    ├── builders/           # (受影响)
    │   ├── style_builder.py    # (重写) 适配ResolvedParams和头/体分离样式
    │   ├── structure_builder.py# (重写) 适配ResolvedParams和头/体分离结构
    │   └── content_builder.py# (受影响) 适配新的TableModel
    └── renderers/          # (受影响)
        └── html_renderer.py  # (主要修改) 渲染出 <thead> 和 <tbody> 标签
```

## 2. 渐进式开发与集成步骤

我们将本次重构分为5个关键的、可独立验证的步骤。

### 步骤 1: 引入 Resolver 和 ResolvedParams，重构工作流

**目标:** 搭建 `Config -> Resolver -> ResolvedParams -> Builder` 的新工作流骨架，但暂时不引入任何随机性。这是后续所有修改的基础。

**操作:**
1.  **新增 `table_render/resolver.py`**:
    -   创建一个 `Resolver` 类，包含一个 `resolve(config: RenderConfig, seed: int) -> ResolvedParams` 方法。
2.  **修改 `table_render/config.py`**:
    -   创建一套全新的Pydantic模型，名为 `ResolvedParams`，其内部包含 `ResolvedStructureParams`, `ResolvedStyleParams` 等。
    -   在V3.1的这个初始步骤中，`Resolved*Params` 的字段与原有的 `*Config` 模型几乎完全相同（都是确定性值）。
3.  **重构 `table_render/resolver.py`**:
    -   `resolve` 方法暂时只做简单的1对1映射，将 `RenderConfig` 的数据直接复制到 `ResolvedParams` 对象中并返回。
4.  **重构 `table_render/main_generator.py`**:
    -   在 `generate` 循环的开始，实例化 `Resolver` 并调用 `resolve` 方法，为每个样本生成一个 `resolved_params` 对象。
    -   将 `resolved_params` 而不是 `self.config` 传递给所有的 `Builder`。
5.  **修改所有 `Builder`**:
    -   更新 `build` 方法的签名，使其接收 `Resolved*Params` 对象。

**验证:**
-   使用一个V3版本的旧配置文件运行程序。
-   程序必须能成功运行，并且生成的表格与V3版本**完全相同**。
-   这证明了新的工作流管道已经通畅，但尚未改变任何行为。

### 步骤 2: 实现数据模型与渲染器的头/体分离

**目标:** 在数据和渲染层面实现 `<thead>` 和 `<tbody>` 的结构性分离。

**操作:**
1.  **修改 `table_render/models/table_model.py`**:
    -   修改 `TableModel`，将其中的 `rows: List[RowModel]` 替换为 `header_rows: List[RowModel]` 和 `body_rows: List[RowModel]`。
2.  **修改 `table_render/builders/structure_builder.py`**:
    -   重写 `build` 方法，使其根据配置中的 `header_rows` 数量，分别填充 `TableModel` 的 `header_rows` 和 `body_rows` 列表。
    -   **关键:** 确保单元格合并逻辑（`rowspan`/`colspan`）被严格限制在各自的区域内（即表头内的合并不能延伸到主体）。
3.  **修改 `table_render/renderers/html_renderer.py`**:
    -   修改 `render` 方法，使其在生成HTML时，用 `<thead>` 标签包裹 `header_rows`，用 `<tbody>` 标签包裹 `body_rows`。

**验证:**
-   运行程序。虽然生成的图像可能看起来没有变化，但这是验证的关键一步。
-   **（可选）** 在 `html_renderer.py` 中添加临时日志，打印出生成的HTML字符串。
-   检查HTML，必须能看到清晰的 `<thead>...</thead>` 和 `<tbody>...</tbody>` 结构。

### 步骤 3: 实现配置和样式的头/体分离

**目标:** 让用户可以在配置文件中为表头和表主体定义完全独立的样式。

**操作:**
1.  **修改 `table_render/config.py`**:
    -   创建一个 `BaseStyleConfig`，包含所有基础的、可被概率化的样式属性（如 `font_size: Union[int, RangeConfig]`）。
    -   重构顶层的 `StyleConfig`，使其包含 `header: BaseStyleConfig` 和 `body: BaseStyleConfig` 两个字段。
2.  **修改 `table_render/resolver.py`**:
    -   扩展 `resolve` 方法，使其能处理新的 `StyleConfig` 结构，并相应地填充 `ResolvedStyleParams`。
3.  **重写 `table_render/builders/style_builder.py`**:
    -   `build` 方法现在读取 `ResolvedStyleParams` 中的 `header` 和 `body` 样式。
    -   生成独立的CSS规则，例如 `thead th { ... }` 和 `tbody td { ... }`。

**验证:**
-   创建一个新的配置文件，为 `header` 设置加粗、深色背景，为 `body` 设置常规、浅色背景。
-   运行生成器，目视检查输出的表格，表头和主体的样式必须有清晰的区分。

### 步骤 4: 全面实现概率化配置

**目标:** 将“确定性配置”全面升级为“概率性配置”，让单个配置文件能生成多样化的表格。

**操作:**
1.  **修改 `table_render/config.py`**:
    -   系统性地检查所有 `*Config` 模型，将需要概率化的字段修改为 `Union[T, List[T]]` (用于选项) 或 `Union[int, RangeConfig]` (用于数值)。
2.  **重构 `table_render/resolver.py`**:
    -   这是本次升级的核心。大幅扩展 `resolve` 方法。
    -   当遇到 `List[T]` 时，使用 `random.choice()` 从列表中选择一个值。
    -   当遇到 `RangeConfig` 时，使用 `random.randint()` 从范围中选择一个值。
    -   将所有这些**具体**的决策结果存入 `ResolvedParams` 对象。

**验证:**
-   创建一个使用大量范围和列表的配置文件。
-   连续多次运行生成器（使用不同的随机种子）。
-   观察到每次生成的表格在尺寸、颜色、字体大小等方面都呈现出不同的、在预期范围内的变化。

### 步骤 5: 实现精确、可复现的元数据

**目标:** 确保 `metadata.json` 是对单次生成结果的精确快照，可用于100%复现。

**操作:**
1.  **修改 `table_render/main_generator.py`**:
    -   在 `generate` 循环中，将 `resolver` 生成的 `resolved_params` 对象传递给文件保存工具。
    -   确保 `FileUtils.save_metadata` 方法将这个 `resolved_params` 对象（而不是原始的 `config`）序列化为 `metadata.json`。

**验证:**
1.  运行生成器，生成一个随机表格，得到 `image_001.png` 和 `metadata_001.json`。
2.  将 `metadata_001.json` 的内容复制到一个新的配置文件 `reproduce_config.yaml` 中。
3.  使用 `reproduce_config.yaml` 再次运行生成器。
4.  新生成的图像必须与 `image_001.png` 在像素级别上**完全一致**。这是V3.1重构成功的最终标准。
