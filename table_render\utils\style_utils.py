"""
样式继承工具

提供样式继承、变异和合并功能，实现"公共样式 + 概率性差异化"机制。
"""

import logging
import random
from typing import Dict, Any, Optional, List
from ..config import CommonStyleConfig, StyleInheritanceConfig, ResolvedBaseStyleParams, ProbabilisticRange, ProbabilisticOptions
from .color_utils import ColorManager
from .prob_utils import choose_from_list, get_from_range


class StyleInheritanceManager:
    """
    样式继承管理器类
    
    负责实现样式继承逻辑，让表主体能够基于表头样式进行有控制的变异。
    """
    
    def __init__(self, random_state):
        """
        初始化样式继承管理器

        Args:
            random_state: 随机状态对象
        """
        self.random_state = random_state
        self.logger = logging.getLogger(__name__)
        self.color_manager = ColorManager()
    
    def apply_inheritance(self, common_style: CommonStyleConfig,
                         inheritance_config: StyleInheritanceConfig) -> tuple:
        """
        应用样式继承逻辑

        Args:
            common_style: 公共样式配置
            inheritance_config: 继承配置

        Returns:
            (表头样式, 表主体样式) 元组
        """
        # 表头使用公共样式（可能有轻微变化）
        header_style = self._create_base_style(common_style, is_header=True)

        # 表主体基于公共样式和表头样式进行概率性变异
        body_style = self._create_variant_style(common_style, inheritance_config, header_style)

        return header_style, body_style
    
    def _create_base_style(self, common_style: CommonStyleConfig, is_header: bool = False) -> Dict[str, Any]:
        """
        创建基础样式
        
        Args:
            common_style: 公共样式配置
            is_header: 是否为表头样式
            
        Returns:
            样式字典
        """
        style = {}
        
        # 字体配置 - V3.4：使用统一概率框架处理字体目录
        font_dirs = common_style.font.font_dirs
        font_dir_probabilities = getattr(common_style.font, 'font_dir_probabilities', None)

        # 处理字体目录选择
        if isinstance(font_dirs, str):
            selected_font_dir = font_dirs
        elif isinstance(font_dirs, list):
            if font_dir_probabilities and len(font_dir_probabilities) == len(font_dirs):
                # 使用概率选择
                selected_font_dir = choose_from_list(font_dirs, font_dir_probabilities, self.random_state)
            else:
                # 等概率选择
                selected_font_dir = self.random_state.choice(font_dirs)
        else:
            selected_font_dir = str(font_dirs)

        style['font'] = {
            'font_dirs': [selected_font_dir],  # 保持列表格式以兼容现有代码
            'default_family': self._resolve_choice_or_prob_options(common_style.font.default_family),
            'default_size': self._resolve_value_or_prob_range(common_style.font.default_size),
            'bold_probability': self._resolve_value(common_style.font.bold_probability),
            'italic_probability': self._resolve_value(common_style.font.italic_probability),
            'fallback_font': getattr(common_style.font, 'fallback_font', 'Microsoft YaHei')
        }
        
        # V3.3：概率化颜色生成
        style['randomize_color_probability'] = common_style.randomize_color_probability
        style['color_contrast'] = common_style.color_contrast

        # 对齐配置
        style['horizontal_align'] = self._resolve_choice_or_prob_options(common_style.horizontal_align)
        style['vertical_align'] = self._resolve_choice_or_prob_options(common_style.vertical_align)

        # 内边距 - V3.4：智能调整以避免与固定尺寸冲突
        resolved_padding = self._resolve_value_or_prob_range(common_style.padding)
        # 注意：这里暂时不进行智能调整，因为我们还不知道具体的行列尺寸
        # 智能调整将在StyleBuilder中进行
        style['padding'] = resolved_padding
        
        # 表头特殊处理：通常更突出
        if is_header:
            # 表头通常使用粗体
            style['font']['bold_probability'] = max(0.7, style['font']['bold_probability'])
            # 表头通常居中对齐
            if style['horizontal_align'] in ['left', 'right']:
                style['horizontal_align'] = 'center'
        
        return style
    
    def _create_variant_style(self, common_style: CommonStyleConfig,
                            inheritance_config: StyleInheritanceConfig,
                            header_style: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建变异样式（用于表主体）

        Args:
            common_style: 公共样式配置
            inheritance_config: 继承配置
            header_style: 表头样式（用于颜色继承逻辑）

        Returns:
            变异后的样式字典
        """
        # 先创建基础样式
        style = self._create_base_style(common_style, is_header=False)
        
        # 根据概率进行变异
        if self.random_state.random() < inheritance_config.font_family_change_probability:
            style['font']['default_family'] = self._generate_variant_font_family(common_style.font.default_family)

        if self.random_state.random() < inheritance_config.font_size_change_probability:
            style['font']['default_size'] = self._generate_variant_font_size(style['font']['default_size'])



        if self.random_state.random() < inheritance_config.alignment_change_probability:
            style['horizontal_align'] = self._generate_variant_alignment(common_style.horizontal_align)

        if self.random_state.random() < inheritance_config.padding_change_probability:
            style['padding'] = self._generate_variant_padding(style['padding'])

        # V3.3新增：颜色继承逻辑
        # 注意：这里我们需要在resolver中处理，因为需要实际的颜色值
        # 这里只是标记需要进行颜色继承处理
        style['_needs_color_inheritance'] = True
        style['_header_style'] = header_style
        style['_text_color_change_probability'] = inheritance_config.text_color_change_probability
        style['_background_color_change_probability'] = inheritance_config.background_color_change_probability

        return style
    
    def _generate_variant_font_family(self, original_families) -> str:
        """生成变异的字体族"""
        if isinstance(original_families, ProbabilisticOptions):
            return choose_from_list(original_families.option_list, original_families.probability_list, self.random_state)
        elif isinstance(original_families, list) and len(original_families) > 1:
            # 从原列表中选择不同的字体
            return self.random_state.choice(original_families)
        return self._resolve_choice_or_prob_options(original_families)
    
    def _generate_variant_font_size(self, original_size: int) -> int:
        """生成变异的字体大小"""
        # 在原大小基础上±2px变化
        variation = self.random_state.randint(-2, 3)
        new_size = original_size + variation
        return max(10, min(24, new_size))  # 限制在合理范围内
    

    def _generate_variant_alignment(self, original_alignments) -> str:
        """生成变异的对齐方式"""
        if isinstance(original_alignments, ProbabilisticOptions):
            return choose_from_list(original_alignments.option_list, original_alignments.probability_list, self.random_state)
        elif isinstance(original_alignments, list) and len(original_alignments) > 1:
            return self.random_state.choice(original_alignments)
        return self._resolve_choice_or_prob_options(original_alignments)
    
    def _generate_variant_padding(self, original_padding: int) -> int:
        """生成变异的内边距"""
        # 在原内边距基础上±2px变化
        variation = self.random_state.randint(-2, 3)
        new_padding = original_padding + variation
        return max(2, min(20, new_padding))  # 限制在合理范围内
    
    def merge_styles(self, base_style: Dict[str, Any], override_style: Dict[str, Any]) -> Dict[str, Any]:
        """
        合并样式
        
        Args:
            base_style: 基础样式
            override_style: 覆盖样式
            
        Returns:
            合并后的样式
        """
        merged = base_style.copy()
        
        for key, value in override_style.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                # 递归合并字典
                merged[key] = self.merge_styles(merged[key], value)
            else:
                merged[key] = value
        
        return merged
    
    def _resolve_value(self, value):
        """解析值：如果是范围则随机选择"""
        from ..config import RangeConfig, FloatRangeConfig

        if hasattr(value, 'min') and hasattr(value, 'max'):
            if isinstance(value, RangeConfig):
                return self.random_state.randint(value.min, value.max + 1)
            elif isinstance(value, FloatRangeConfig):
                return self.random_state.uniform(value.min, value.max)
        return value

    def _resolve_choice(self, value):
        """解析选择：如果是列表则随机选择"""
        if isinstance(value, list) and len(value) > 0:
            return self.random_state.choice(value)
        return value

    def _resolve_value_or_prob_range(self, value):
        """解析值或概率化范围：支持固定值、RangeConfig、FloatRangeConfig和ProbabilisticRange"""
        if isinstance(value, ProbabilisticRange):
            chosen_range = choose_from_list(value.range_list, value.probability_list, self.random_state)
            return get_from_range(chosen_range, self.random_state)
        else:
            return self._resolve_value(value)

    def _resolve_choice_or_prob_options(self, value):
        """解析选择值或概率化选项：支持单个值、列表和ProbabilisticOptions"""
        if isinstance(value, ProbabilisticOptions):
            return choose_from_list(value.option_list, value.probability_list, self.random_state)
        else:
            return self._resolve_choice(value)


