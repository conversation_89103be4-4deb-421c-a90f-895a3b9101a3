# TableRender v3.4 调用链分析

## 调用链

### 节点(main)

**所在代码文件**: `table_render/main.py`

**用途**:
作为TableRender工具的命令行入口。负责设置日志，解析用户输入的参数（配置文件和样本数量），调用 `load_config` 加载和验证配置，然后初始化核心的 `MainGenerator`，并最终调用其 `generate` 方法来启动整个表格图像的生成流程。

**输入参数**:
- `--config` (str): 用户通过命令行指定的YAML配置文件路径。
- `--num-samples` (int): 用户通过命令行指定的希望生成的图像数量。

**输出说明**:
该函数没有直接的返回值。其主要作用是在文件系统上生成图像和标注文件，或在发生错误时打印日志并退出程序。

**实现流程**:
```mermaid
sequenceDiagram
    participant User
    participant main as "main()"
    participant load_config as "load_config()"
    participant generator as MainGenerator

    User->>main: 执行命令行
    main->>load_config: 传入 config_path
    load_config-->>main: 返回 render_config 对象
    main->>generator: 使用 render_config 实例化
    generator-->>main: 返回 generator 实例
    main->>generator: 调用 generate(num_samples)
```

### 节点(MainGenerator.generate)

**所在代码文件**: `table_render/main_generator.py`

**用途**:
作为核心协调器，异步地执行完整的表格图像生成流水线。它为每个样本生成一个独立的随机种子，并按顺序调用各个子模块（Resolver, Builders, Renderer）来完成从配置解析到最终文件保存的全过程。v3.4版本中，其核心流程没有大变化，但调用的子模块（特别是Resolver）内部逻辑已增强。

**输入参数**:
- `num_samples` (int): 需要生成的表格图像样本数量。

**输出说明**:
该方法没有直接的返回值。它将生成的图像、标注和元数据文件保存到由配置指定的输出目录中。

**实现流程**:
```mermaid
flowchart TD
    A["开始 (generate)"] --> B["调用 asyncio.run(_async_generate)"];
    subgraph _async_generate
        C["初始化 HtmlRenderer 和 AnnotationConverter"];
        C --> D{"循环 N 次 (num_samples)"};
        D -- "进行中" --> E["生成样本随机种子"];
        E --> F["调用 Resolver.resolve 解析具体参数"];
        F --> G["实例化 StructureBuilder 并调用 .build()"];
        G --> H["实例化 ContentBuilder 并调用 .build()"];
        H --> I["实例化 StyleBuilder 并调用 .build()"];
        I --> J["调用 HtmlRenderer.render (await)"];
        J --> K["调用 AnnotationConverter.convert"];
        K --> L["准备元数据和文件名"];
        L --> M["调用 FileUtils.save_sample"];
        M --> D;
    end
    D -- "循环完成" --> N["关闭 HtmlRenderer (finally)"];
    N --> O["结束"];
    B --> C;
```

### 节点(Resolver.resolve)

**所在代码文件**: `table_render/resolver.py`

**用途**:
作为配置转换的核心，将用户提供的、包含概率和范围的抽象配置（`RenderConfig`），解析成一组用于单次生成的、完全确定性的具体参数（`ResolvedParams`）。它利用随机种子来确保每次解析的可复现性。在v3.4中，该模块被大幅增强，引入了 `prob_utils` 来处理概率化配置，并重构了所有子解析方法（如 `_resolve_structure_params`, `_resolve_style_params` 等）以支持新的概率模型。

**输入参数**:
- `config` (RenderConfig): 原始的、未解析的渲染配置对象。
- `seed` (int): 用于本次解析的随机种子，确保结果的确定性。

**输出说明**:
- `ResolvedParams`: 一个包含所有具体参数（结构、内容、样式、输出）的数据对象，可直接被后续的构建器使用。

**实现流程**:
```mermaid
flowchart TD
    A["开始 (resolve)"] --> B["创建随机状态 (RandomState)"];
    B --> C["初始化 StyleInheritanceManager"];
    C --> D["调用 _resolve_structure_params"];
    D --> E["调用 _resolve_content_params"];
    E --> F["调用 _resolve_style_params (v3.4 增强版)"];
    subgraph _resolve_style_params
        F1["应用样式继承"];
        F1 --> F2["解析概率化边框模式"];
        F2 --> F3["解析概率化字体/颜色/对齐等"];
        F3 --> F4["解析可变行高列宽 (Sizing)"];
    end
    F --> G["调用 _resolve_output_params"];
    G --> H["创建并返回 ResolvedParams 对象"];
    H --> I["结束"];
```

### 节点(StyleBuilder.build)

**所在代码文件**: `table_render/builders/style_builder.py`

**用途**:
负责将解析后的样式参数（`ResolvedStyleParams`）转换成最终的CSS字符串。它会生成包括字体、颜色、对齐、边框在内的所有样式规则。在v3.4中，该模块新增了 `_generate_sizing_rules` 方法，用于为具有不同尺寸的行和列生成特定的CSS类（如 `.row-0`, `.col-1`），以实现可变行高列宽的布局。

**输入参数**:
- `config` (ResolvedStyleParams): 经过 `Resolver` 解析后的、完全确定性的样式参数。
- `table_model` (TableModel): 包含了单元格级别边框决策的表格模型，用于生成精确的边框CSS。

**输出说明**:
- `str`: 一个包含了所有样式规则的、完整的CSS字符串。

**实现流程**:
```mermaid
flowchart TD
    A["开始 (build)"] --> B["生成 @font-face 规则"];
    B --> C["生成全局和表格基础样式"];
    C --> D["生成表头和表体样式"];
    D --> E["生成斑马纹或分层样式 (如果配置)"];
    E --> F["调用 _generate_sizing_rules 生成行高列宽CSS"];
    F --> G["根据 TableModel 生成单元格级边框CSS"];
    G --> H["合并所有CSS片段并返回"];
    H --> I["结束"];
```

### 节点(StructureBuilder.build)

**所在代码文件**: `table_render/builders/structure_builder.py`

**用途**:
负责根据解析后的结构参数（`ResolvedStructureParams`）创建表格的逻辑骨架（`TableModel`）。它会生成指定数量的行和列，并初始化每个单元格（`CellModel`），但不填充内容。该模块还负责处理复杂的表头结构和单元格合并逻辑，并根据边框模式计算出每个单元格的边框显示状态。

**输入参数**:
- `config` (ResolvedStructureParams): 经过 `Resolver` 解析后的、确定性的结构参数。
- `border_mode` (str): 边框模式，如 'full', 'none'。
- `border_details` (dict): 更详细的边框配置。

**输出说明**:
- `TableModel`: 一个包含了完整结构（行、列、单元格、合并信息、边框决策）但内容为空的表格模型对象。

**实现流程**:
```mermaid
flowchart TD
    A["开始 (build)"] --> B{"是否使用复杂表头?"};
    B -- "是" --> C["调用 _build_complex_header_table"];
    B -- "否" --> D["获取行列数"];
    D --> E["创建 TableModel"];
    E --> F["循环创建表头行和单元格"];
    F --> G["循环创建主体行和单元格"];
    G --> H["处理单元格合并"];
    C --> H;
    H --> I["计算并设置边框决策"];
    I --> J["返回 TableModel"];
    J --> K["结束"];
```

### 节点(ContentBuilder.build)

**所在代码文件**: `table_render/builders/content_builder.py`

**用途**:
负责为已经创建好结构（但内容为空）的 `TableModel` 填充实际内容。它支持两种内容来源：程序化生成（使用Faker库创建日期、货币、姓名等）或从指定的CSV文件中读取。该模块会遍历表格中的所有单元格，并根据配置为其赋予文本内容。

**输入参数**:
- `table_model` (TableModel): 由 `StructureBuilder` 生成的、仅有结构的表格模型。
- `config` (ResolvedContentParams): 经过 `Resolver` 解析后的、确定性的内容参数。

**输出说明**:
- `TableModel`: 一个在原有结构基础上填充了完整内容的表格模型对象。

**实现流程**:
```mermaid
flowchart TD
    A["开始 (build)"] --> B{"内容来源是?"};
    B -- "programmatic" --> C["调用 _fill_programmatically"];
    B -- "csv" --> D["调用 _fill_from_csv"];
    C --> E["遍历所有单元格"];
    E --> F["调用 _get_random_programmatic_content 生成内容"];
    F --> G["设置 cell.content"];
    G --> E;
    D --> H["读取CSV文件"];
    H --> I["遍历所有单元格并从CSV数据填充"];
    I --> J;
    E -- "遍历完成" --> J;
    J["返回填充后的 TableModel"] --> K["结束"];
```

### 节点(HtmlRenderer.render)

**所在代码文件**: `table_render/renderers/html_renderer.py`

**用途**:
负责将最终的 `TableModel` 和 CSS 样式字符串转换为图像和原始标注数据。它首先调用 `_table_model_to_html` 将表格模型和样式转换成一个完整的HTML文档。然后，它启动一个无头浏览器（Playwright），加载该HTML，对表格元素进行截图，并执行一段JavaScript脚本来提取每个单元格和内容块的位置和尺寸信息作为原始标注。

**输入参数**:
- `table_model` (TableModel): 包含了完整结构和内容的表格模型。
- `css_string` (str): 由 `StyleBuilder` 生成的完整CSS样式字符串。

**输出说明**:
- `Tuple[bytes, Dict]`: 一个元组，第一个元素是PNG格式的图像字节流，第二个元素是包含原始标注信息的字典。

**实现流程**:
```mermaid
flowchart TD
    A["开始 (render)"] --> B["调用 _table_model_to_html 生成HTML内容"];
    B --> C["创建新的浏览器页面 (Page)"];
    C --> D["设置页面内容为生成的HTML"];
    D --> E["执行JS脚本获取原始标注"];
    E --> F["对表格元素进行截图"];
    F --> G["关闭页面"];
    G --> H["返回 (图像字节流, 原始标注)"];
    H --> I["结束"];
```

### 节点(AnnotationConverter.convert_to_final_format)

**所在代码文件**: `table_render/utils/annotation_converter.py`

**用途**:
负责将从渲染器获取的原始标注数据（包含每个单元格和内容的像素级边界框）转换为最终项目所需的结构化JSON格式。它通过 `TableModel` 将几何信息与逻辑信息（如行列索引、是否为表头）相结合，并转换坐标格式，最终生成符合规范的标注文件内容。

**输入参数**:
- `raw_annotations` (Dict): 从 `HtmlRenderer` 获取的原始标注数据。
- `table_model` (TableModel): 对应的表格逻辑模型，用于信息补充。
- `image_filename` (str): 关联的图像文件名。

**输出说明**:
- `Dict`: 一个字典，其结构完全符合最终标注文件的格式要求。

**实现流程**:
```mermaid
flowchart TD
    A["开始 (convert)"] --> B["创建单元格ID到模型的映射"];
    B --> C["初始化最终标注字典结构"];
    C --> D{"循环遍历原始标注中的每个单元格"};
    D -- "进行中" --> E["根据ID查找对应的CellModel"];
    E --> F["转换BBox坐标格式"];
    F --> G["构建包含逻辑位置、边框、内容的单元格标注"];
    G --> H["将单元格标注添加到最终列表"];
    H --> D;
    D -- "循环完成" --> I["返回最终标注字典"];
    I --> J["结束"];
```

## 整体用途、目录结构和时序图

### 整体用途

TableRender v3.4 是一个高度可配置的表格图像生成工具。其核心设计思想是将**抽象的、带概率的配置**转换为**具体的、可渲染的表格**。整个流程始于用户提供的一个YAML配置文件，该文件定义了表格结构、内容和样式的各种可能性（如行数范围、边框模式选项、字体大小范围等）及其对应的概率。工具通过一个多阶段的流水线处理这个配置：

1.  **配置加载与验证**: `main.py` 和 `config.py` 负责加载YAML文件，并使用Pydantic模型进行严格的结构和类型验证。
2.  **概率性解析**: `Resolver` 模块是v3.4的核心。它接收经过验证的配置和随机种子，根据概率分布解析出本次生成所需的**一组完全确定的参数**（例如，本次生成5行8列，边框使用'semi'模式，字体大小为14px）。
3.  **模块化构建**: 三个 `Builder`（`StructureBuilder`, `ContentBuilder`, `StyleBuilder`）根据解析出的确定性参数，并行地构建出表格的逻辑模型（`TableModel`）和视觉样式（CSS）。`StructureBuilder` 负责行、列、合并单元格等结构；`ContentBuilder` 负责填充文本内容；`StyleBuilder` 负责生成所有CSS规则，特别是v3.4新增的可变行高列宽样式。
4.  **渲染与标注**: `HtmlRenderer` 利用无头浏览器（Playwright）将 `TableModel` 和CSS渲染成PNG图像，并提取出每个元素的精确位置作为原始标注。
5.  **格式转换与保存**: `AnnotationConverter` 将原始标注转换为最终的JSON格式，最后由 `FileUtils` 将图像和标注文件保存到磁盘。

通过这种方式，v3.4版本能够基于单个配置文件高效地生成大量多样化、风格各异的表格数据，极大地增强了生成样本的丰富性和泛化能力。

### 目录结构

```
table_render/
├── __init__.py
├── main.py                   # 命令行入口
├── config.py                 # (v3.4新增/重构) Pydantic模型，定义概率化配置结构
├── resolver.py               # (v3.4重构) 核心解析器，处理概率化配置
├── main_generator.py         # 核心流程协调器
├── builders/                 # 构建器模块
│   ├── __init__.py
│   ├── structure_builder.py    # 构建表格结构
│   ├── content_builder.py      # 填充表格内容
│   └── style_builder.py        # (v3.4修改) 生成CSS，支持可变尺寸
├── renderers/                # 渲染器模块
│   ├── __init__.py
│   └── html_renderer.py        # 使用Playwright渲染HTML为图像
├── models.py                 # 定义核心数据结构，如TableModel, CellModel
└── utils/                    # 工具函数模块
    ├── __init__.py
    ├── prob_utils.py           # (v3.4新增) 概率计算工具函数
    ├── style_utils.py          # (v3.4修改) 样式相关帮助函数
    ├── font_utils.py           # 字体管理
    ├── color_utils.py          # 颜色管理
    ├── file_utils.py           # (v3.4修改) 文件操作，支持label_suffix
    └── annotation_converter.py # 标注格式转换
```

### 整体调用时序图

```mermaid
sequenceDiagram
    participant User
    participant main as main()
    participant MainGen as MainGenerator
    participant Resolver as Resolver
    participant Builders as Builders (Structure, Content, Style)
    participant Renderer as HtmlRenderer
    participant Converter as AnnotationConverter

    User->>main: 执行命令 (含config, num_samples)
    main->>MainGen: 实例化 (传入config)
    main->>MainGen: generate(num_samples)

    loop 每个样本
        MainGen->>Resolver: resolve(config, seed)
        Resolver-->>MainGen: 返回 ResolvedParams

        MainGen->>Builders: build(ResolvedParams)
        Note over Builders: Structure, Content, Style Builders 被依次调用
        Builders-->>MainGen: 返回 TableModel 和 CSS String

        MainGen->>Renderer: render(TableModel, CSS)
        Renderer-->>MainGen: 返回 (image_bytes, raw_annotations)

        MainGen->>Converter: convert(raw_annotations, TableModel)
        Converter-->>MainGen: 返回 final_annotations

        MainGen->>main: (隐式) FileUtils.save_sample
    end
```





