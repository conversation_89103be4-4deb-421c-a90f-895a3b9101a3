# 透视变换效果对比测试

这个脚本用于对比不同库和方法实现的透视变换效果，帮助选择最适合的透视变换实现。

## 支持的变换方法

1. **OpenCV** - 使用 `cv2.warpPerspective()`
   - 优点：性能好，质量高，广泛使用
   - 缺点：需要安装OpenCV

2. **Pillow** - 使用 `Image.transform()` 
   - 优点：Python标准库，兼容性好
   - 缺点：质量可能不如专业库

3. **scikit-image** - 使用 `transform.ProjectiveTransform`
   - 优点：科学计算库，算法丰富
   - 缺点：依赖较重

4. **Wand (ImageMagick)** - 使用 `distort('perspective')`
   - 优点：ImageMagick的强大功能
   - 缺点：需要安装ImageMagick

5. **CSS浏览器渲染** - 使用Playwright + CSS transform
   - 优点：与网页渲染一致，抗锯齿好
   - 缺点：需要浏览器环境，速度较慢

6. **自定义数学实现** - 纯Python数学计算
   - 优点：无外部依赖，可完全控制
   - 缺点：性能较差，实现复杂

## 安装依赖

```bash
# 基础依赖
pip install pillow numpy

# 可选依赖（根据需要安装）
pip install opencv-python          # OpenCV
pip install scikit-image          # scikit-image  
pip install Wand                  # ImageMagick Python绑定
pip install playwright            # 浏览器自动化
playwright install chromium       # 安装Chromium浏览器
```

## 使用方法

### 基本用法

```bash
# 使用默认输出目录
python tests/perspective_transform_comparison.py --input your_image.png

# 指定输出目录
python tests/perspective_transform_comparison.py --input your_image.png --output_dir ./results
```

### 参数说明

- `--input, -i`: 输入图像路径（必需）
- `--output_dir, -o`: 输出目录（可选，默认为 `./perspective_comparison_output`）

## 输出结果

脚本会在输出目录中生成：

1. **原始图像**: `original.png`
2. **各方法的变换结果**: 
   - `opencv_light.png`, `opencv_medium.png`, `opencv_strong.png`
   - `pillow_light.png`, `pillow_medium.png`, `pillow_strong.png`
   - `skimage_light.png`, `skimage_medium.png`, `skimage_strong.png`
   - `wand_light.png`, `wand_medium.png`, `wand_strong.png`
   - `css_light.png`, `css_medium.png`, `css_strong.png`
   - `custom_light.png`, `custom_medium.png`, `custom_strong.png`
3. **对比HTML页面**: `comparison.html`

## 变换强度说明

- **light** (轻微): offset_ratio = 0.01，适合文档扫描效果
- **medium** (中等): offset_ratio = 0.03，平衡效果和质量  
- **strong** (强烈): offset_ratio = 0.05，明显的3D效果

## 评估标准

查看生成的 `comparison.html` 时，重点关注：

1. **边缘平滑度** - 是否有明显锯齿
2. **线条连续性** - 表格边框是否连续
3. **整体质量** - 图像是否清晰
4. **变形准确性** - 透视效果是否自然

## 示例

```bash
# 测试表格图像的透视变换效果
python tests/perspective_transform_comparison.py --input table_sample.png --output_dir ./table_perspective_test

# 测试完成后，打开 ./table_perspective_test/comparison.html 查看结果
```

## 故障排除

1. **某个方法失败**: 检查对应的依赖是否正确安装
2. **CSS方法失败**: 确保安装了Playwright和Chromium
3. **Wand方法失败**: 确保系统安装了ImageMagick
4. **内存不足**: 尝试使用较小的图像或减少测试方法

## 注意事项

- 脚本使用固定的随机种子(42)确保结果可重现
- 大图像可能需要较长处理时间
- CSS方法需要网络连接来下载浏览器（首次使用）
- 自定义数学实现较慢，适合小图像测试
