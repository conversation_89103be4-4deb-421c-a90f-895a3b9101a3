{"cells": [{"cell_type": "code", "execution_count": 1, "id": "0e284882", "metadata": {}, "outputs": [], "source": ["from typing import List\n", "import json\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from subprocess import run, PIPE\n", "from pdf2image import convert_from_bytes\n", "import docx\n", "from docx.enum.dml import MSO_THEME_COLOR\n", "from docx.oxml.ns import nsdecls\n", "from docx.oxml import parse_xml\n", "import cv2 as cv\n", "import glob\n", "import re\n", "import itertools\n", "from docx.shared import Inches, Cm\n", "import copy"]}, {"cell_type": "markdown", "id": "88e90527", "metadata": {}, "source": ["### Word Table Writer\n", "- Top & bottom rows bordered: Medium List\n", "- Header and bottom bordered: Light Shading\n", "- Partially bordered: Colorful Grid\n", "- Fully bordered: Light Grid \n"]}, {"cell_type": "code", "execution_count": 11, "id": "3f5bdc52", "metadata": {}, "outputs": [], "source": ["class TableWriter:    \n", "    def __init__(self):    \n", "        with open('templates/common/config.json','r') as f:\n", "            paragraphs = json.load(f)\n", "        self.paragraphs = []\n", "        for key in paragraphs:\n", "            text = \"\".join(paragraphs[key])\n", "            self.paragraphs.append(text)\n", "       \n", "    ''' write table '''\n", "    def write_table(self,table,df):\n", "        for j in range(df.shape[-1]):\n", "            table.cell(0,j).text = df.columns[j]\n", "        # add the rest of the data frame\n", "        for i in range(df.shape[0]):\n", "            for j in range(df.shape[-1]):\n", "                table.cell(i+1,j).text = str(df.values[i,j])\n", "                \n", "    'make row of cells background colored, defaults to column header row'\n", "    def color_table(self,table,color='1F5C8B'):\n", "        color = \"\".join([r'<w:shd {} ','w:fill=',f'\"{color}\"','/>'])\n", "        for row in table.rows:\n", "            for cell in row.cells:\n", "                shading_elm_2 = parse_xml(color.format(nsdecls('w')))\n", "                cell._tc.get_or_add_tcPr().append(shading_elm_2)\n", "                \n", "    ''' structure margins'''\n", "    def structure_margins(self,doc,vertical=0.5,horizontal=1):\n", "        sections = doc.sections\n", "        for section in sections:\n", "            section.top_margin = Cm(vertical)\n", "            section.bottom_margin = Cm(vertical)\n", "            section.left_margin = Cm(horizontal)\n", "            section.right_margin = Cm(horizontal)\n", "\n", "    ''' just write a single table '''\n", "    def add_table(self,doc,table_type:int,df,df_inner=None):\n", "        p = np.random.uniform(0,1)\n", "        if p>0.5:\n", "            accent = ' Accent '+str(int(np.random.uniform(1,7)))\n", "        elif p<0.5:\n", "            accent = ''\n", "        if table_type==0:\n", "            table = doc.add_table(df.shape[0]+1, df.shape[1],style='Medium List 1'+accent)\n", "            self.write_table(table,df)\n", "            return doc\n", "            \n", "        elif table_type==1:\n", "            table = doc.add_table(df.shape[0]+1, df.shape[1],'Light Shading'+accent)\n", "            self.write_table(table,df)\n", "            return doc\n", "        \n", "        elif table_type==2:\n", "            table = doc.add_table(df.shape[0]+1, df.shape[1],'Colorful Grid'+accent)\n", "            self.write_table(table,df)\n", "            return doc\n", "        \n", "        elif table_type==3:\n", "            table = doc.add_table(df.shape[0]+1, df.shape[1],'Light Grid'+accent)\n", "            self.write_table(table,df)\n", "            return doc\n", "            \n", "        elif  table_type==4:\n", "            styles = ['Medium List 1','Light Shading','Colorful Grid','Light Grid']\n", "            style = styles[int(np.random.uniform(0,len(styles)))]\n", "            style = style+accent\n", "            x = int(np.random.uniform(0,df.shape[-1]))\n", "            y = int(np.random.uniform(1,df.shape[-1]))\n", "            table = doc.add_table(df.shape[0]+1, df.shape[1],style='Light Grid'+accent)\n", "            for j in range(df.shape[-1]):\n", "                table.cell(0,j).text = df.columns[j]\n", "            # add the rest of the data frame\n", "            for i in range(df.shape[0]):\n", "                for j in range(df.shape[-1]):\n", "                    if i==x and j==y:\n", "                        inner_table = table.cell(i+1,j).add_table(df_inner.shape[0]+1,df_inner.shape[1])\n", "                        self.write_table(inner_table,df_inner)\n", "                        inner_table.style = style\n", "                    else: \n", "                        table.cell(i+1,j).text = str(df.values[i,j])\n", "            return doc\n", "             \n", "    ''' write multiple tables '''\n", "    def write(self,table_types:List[int],dfs,inner_dfs=None)->str:\n", "        if inner_dfs is None:\n", "            inner_dfs = [None for i in range(len(table_types))]\n", "        doc = docx.Document()\n", "        self.structure_margins(doc)\n", "        for i in range(len(table_types)):\n", "            text = self.paragraphs[i]\n", "            doc.add_paragraph(\"\".join([\"\\n\",text]))\n", "            header = doc.add_heading('Table '+str(i+1))\n", "            header.alignment = 1\n", "            df = dfs[i]\n", "            if inner_dfs[i] is None:\n", "                doc = self.add_table(doc,table_types[i],dfs[i])\n", "            else:\n", "                doc = self.add_table(doc,table_types[i],dfs[i],inner_dfs[i])\n", "        doc.add_paragraph(\"\".join([\"\\n\\n\",self.paragraphs[-1]]))\n", "        doc.save('tmp/x.docx')\n", "        out_doc = docx.Document('tmp/x.docx')\n", "        tables = out_doc.tables\n", "        for table in tables:\n", "            self.color_table(table)\n", "        return doc,out_doc"]}, {"cell_type": "code", "execution_count": 12, "id": "8ed91f5f", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv('sources/FullData.csv')\n", "df = df.iloc[:5,:4]"]}, {"cell_type": "markdown", "id": "71aa659b", "metadata": {}, "source": ["### Word To Pdf\n", "Convert word document to relevant pdf"]}, {"cell_type": "code", "execution_count": 13, "id": "747c9cea", "metadata": {}, "outputs": [], "source": ["class WordToPdf:\n", "    def doc_to_pdf(self,doc):\n", "        doc.save('tmp/tmp.docx')\n", "        p = run(['libreoffice','--headless','--convert-to','pdf','tmp/tmp.docx','--outdir','tmp'],stdout=PIPE)\n", "        with open('tmp/tmp.pdf','rb') as f:\n", "            pdf = f.read()\n", "        return pdf\n", "    \n", "    def docs_to_pdfs(self,docs):\n", "        pdfs = []\n", "        for doc in docs:\n", "            pdf = self.doc_to_pdf(doc)\n", "            pdfs.append(pdf)\n", "        return pdfs"]}, {"cell_type": "markdown", "id": "83313799", "metadata": {}, "source": ["### Pdf To Img"]}, {"cell_type": "code", "execution_count": 14, "id": "6ccaa2c8", "metadata": {}, "outputs": [], "source": ["class PdfToImg:\n", "    ''' pdf to img '''\n", "    def pdf_to_img(self,pdf_bytes:bytes):\n", "        img = convert_from_bytes(pdf_bytes,dpi=200)[0]\n", "        return np.asarray(img,dtype=np.uint8)\n", "    \n", "    ''' pdfs to imgs '''\n", "    def pdfs_to_imgs(self,pdfs:List[bytes]):\n", "        pdf_imgs = []\n", "        for pdf in pdfs:\n", "            img_pdf = self.pdf_to_img(pdf)\n", "            pdf_imgs.append(img_pdf)\n", "        return pdf_imgs"]}, {"cell_type": "markdown", "id": "08c29f30", "metadata": {}, "source": ["### Transformer\n", "Transform a given image , to try and mimic real world data of scanned images. The following transforms applicable\n", "- Gaussian Blur $k$ (kernel size), $(k,k)$ \n", "- Scale $(sx,sy)$ \n", "- Rotate $\\theta$"]}, {"cell_type": "code", "execution_count": 15, "id": "28827192", "metadata": {}, "outputs": [], "source": ["class Transformer:\n", "    ''' blur (! later must investigate scan effect)'''\n", "    def blur(self,img,kernel):\n", "        sigma_x,sigma_y = 2,2\n", "        blurred_img = cv.<PERSON><PERSON><PERSON>(img,kernel,sigma_x,sigma_y)\n", "        return blurred_img\n", "    \n", "    ''' rotate '''\n", "    def rotate(self,img,theta:float,border=(255,255,255)):\n", "        height, width = img.shape[:2]\n", "        center = (width/2, height/2)\n", "        rotate_matrix = cv.getRotationMatrix2D(center=center, angle=theta, scale=1)\n", "        rotated_img = cv.warpAffine(src=img, M=rotate_matrix, dsize=(width, height),borderValue=border)\n", "        return rotated_img\n", "    \n", "    ''' dirtify data by applying sequence of transformations'''\n", "    def dirtify(self,img,k:int,s_x:int,s_y:int,theta:float,mask:bool):\n", "        m,n = img.shape[:2]\n", "        m,n = int(s_y*m),int(s_x*n)\n", "        dim = (n,m)\n", "        if not mask:\n", "            x = self.blur(img,kernel=(k,k))\n", "            y = cv.resize(x, dim, interpolation = cv.INTER_AREA)\n", "            z = self.rotate(y,theta=theta)\n", "        else:\n", "            x = img\n", "            y = cv.resize(x, dim, interpolation = cv.INTER_AREA)\n", "            z = self.rotate(y,theta=theta,border=(0,0,0))\n", "        return z"]}, {"cell_type": "markdown", "id": "45028fc9", "metadata": {}, "source": ["### Mask Generator\n", "From the given pdf image generate table mask (label). This is done as follows.\n", "- Compute absolute difference between raw pdf img and outlined img\n", "- Apply adaptive thresholding to resulting image to obtain binary image\n", "- Detect external contours in binary image and fill bounding box regions of contours"]}, {"cell_type": "code", "execution_count": 16, "id": "43acbc51", "metadata": {}, "outputs": [], "source": ["class PreProcessor:\n", "    ''' grayscale the image '''\n", "    def grayscale(self,img):\n", "        grayscaled = cv.cvtColor(img, cv.COLOR_RGB2GRAY)\n", "        return grayscaled\n", "    \n", "    ''' thresholding the image to a binary image '''\n", "    def threshold(self,img,mode='adaptive'):\n", "        if mode == 'adaptive':\n", "            thresh = cv.adaptiveThreshold(img, 255, 1, 1, 11, 2)\n", "            return thresh\n", "        elif mode=='otsu':\n", "            _,thresh = cv.threshold(img,128,255,cv.THRESH_BINARY |cv.THRESH_OTSU)\n", "            return thresh\n", "\n", "    ''' apply preprocessing steps ''' \n", "    def preprocess(self,img):\n", "        grayscaled = self.grayscale(img)\n", "        thresholded = self.threshold(grayscaled)\n", "        return thresholded\n", "    \n", "class MaskGenerator:\n", "    def __init__(self):\n", "        self.preprocessor = PreProcessor()\n", "       \n", "    ''' fill region with specified contours '''\n", "    def fill(self,shape,contours):\n", "        filled_binary_mask = np.zeros(shape,dtype=np.uint8)\n", "        bounding_rectangles = []\n", "        for contour in contours:\n", "            rect = cv.boundingRect(contour)\n", "            x,y,w,h = rect\n", "            filled_binary_mask[y:y+h,x:x+w] = 255\n", "        return filled_binary_mask\n", "            \n", "    ''' fill the mask '''\n", "    def fill_mask(self,mask):\n", "        binary_mask = self.preprocessor.preprocess(mask)\n", "        contours, _ = cv.findContours(binary_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)\n", "        binary_mask = self.fill(binary_mask.shape,contours)\n", "        return binary_mask\n", "    \n", "    ''' generate table mask taking difference of 2 imgs '''\n", "    def mask(self,raw_img,outlined_img):\n", "        x = raw_img\n", "        y = outlined_img\n", "        mask = abs(x-y)\n", "        filled_mask = self.fill_mask(mask)\n", "        return filled_mask\n", "    \n", "    def masks(self,raw_imgs,outlined_imgs):\n", "        masks = []\n", "        for i in range(len(raw_imgs)):\n", "            x = raw_imgs[i]\n", "            y = outlined_imgs[i]\n", "            mask = self.mask(x,y)\n", "            masks.append(mask)\n", "        return masks"]}, {"cell_type": "markdown", "id": "dd93a6e9", "metadata": {}, "source": ["### Table Structure Generator\n", "Generate xml like string representing table structure. This is done by making system call to latexml."]}, {"cell_type": "code", "execution_count": 17, "id": "19ab9334", "metadata": {}, "outputs": [], "source": ["class StructureGenerator:\n", "    def __init__(self):\n", "        self.expression = '<table|<row|<cell|</cell>|</row>|</table>'\n", "        self.regex = re.compile(self.expression)\n", "    \n", "    ''' clean and format str '''\n", "    def clean(self,table:str)->str:\n", "        line = table\n", "        to_remove = ['w:tblPr','w:tblStyle','w:tblW','w:tblLook','w:tblGrid','w:tcPr','w:tblW','w:type','w:tcW']\n", "        for s in to_remove:\n", "            line = line.replace(s,'*')\n", "        to_replace = ['w:tbl','w:tr','w:tc']\n", "        replacement = ['table','row','cell']\n", "        for i in range(len(to_replace)):\n", "            line = line.replace(to_replace[i],replacement[i])\n", "        line = line.replace('cellr','*')\n", "        return line\n", "    \n", "    ''' markup representing table '''\n", "    def structure(self,table:str)->str:\n", "        table = self.clean(table)\n", "        lines = re.findall(self.regex, table)\n", "        lines = [lines[i]+'>' if lines[i].find(\">\")==-1 else lines[i] for i in range(len(lines))]\n", "        structure = \"\\n\".join(lines)\n", "        return structure\n", "    \n", "    ''' generate table structures '''\n", "    def structures(self,tables:List[str])->List[str]:\n", "        structures = []\n", "        for table in tables:\n", "            structure = self.structure(table._element.xml)\n", "            structures.append(structure)\n", "        return structures"]}, {"cell_type": "markdown", "id": "03b73590", "metadata": {}, "source": ["### Metadata Generator\n", "Metadata to be generated includes the following\n", "- Number of tables - This is counted by counting the number of connected components in the mask (table regions should be disjoint regions of white pixels)\n", "- Bounding boxes (of tables) - Generated by detecting external contours and returning nounding box\n", "- XML strings of structure - latex string representation as arg yo structure generator"]}, {"cell_type": "code", "execution_count": 18, "id": "8a3a63bf", "metadata": {}, "outputs": [], "source": ["class MetadataGenerator(StructureGenerator):    \n", "    ''' number of tables from table mask '''\n", "    def number_of_tables(self,table_mask)->int:\n", "        num,_ = cv.connectedComponents(table_mask)\n", "        return num-1\n", "    \n", "    ''' sort the contours top to bottom '''\n", "    def sort(self,contours):\n", "        y_values = []\n", "        for contour in contours:\n", "            x,y,w,h = cv.boundingRect(contour)\n", "            y_values.append(y)\n", "        y_values = np.array(y_values)\n", "        idx = np.argsort(y_values)\n", "        sorted_contours = [contours[i] for i in idx]\n", "        return sorted_contours\n", "    \n", "    ''' bounding boxes '''\n", "    def bounding_boxes(self,table_mask):\n", "        contours , _ = cv.findContours(table_mask, cv.RETR_EXTERNAL, cv.CHAIN_APPROX_SIMPLE)\n", "        sorted_contours = self.sort(contours)\n", "        boxes = []\n", "        for contour in sorted_contours:\n", "            box = cv.boundingRect(contour)\n", "            boxes.append(box)\n", "        return boxes\n", "    \n", "    ''' generate metadata '''\n", "    def metadata(self,table_mask,tables:List[str]):\n", "        num_tables = self.number_of_tables(table_mask)\n", "        bboxes = self.bounding_boxes(table_mask)\n", "        bboxes_data = [bbox for bbox in bboxes]\n", "        structures = self.structures(tables)\n", "        result = {'no':num_tables,'bounding_boxes':bboxes_data,'structures':structures}\n", "        return result"]}, {"cell_type": "markdown", "id": "64bf5673", "metadata": {}, "source": ["### Pipeline\n", "Combines all of the above to generate dataset the steps to generate a dataset are.\n", "- Given list of types generate templates (templates func)\n", "- From templates generate resulting pdf,img,mask (datum func)\n", "- Distort datum (applies transforms to img and mask)\n", "- Annotate distort datum (uses metadata generator)"]}, {"cell_type": "code", "execution_count": 19, "id": "4f2b31cf", "metadata": {}, "outputs": [], "source": ["class DataSource:\n", "    def __init__(self,path):\n", "        self.data = self.load(path)\n", "        self.N = len(self.data)\n", "        self.MIN_ROWS = 2\n", "        self.MIN_OUTER_ROWS = 4\n", "        self.MAX_ROWS = 30\n", "        self.MIN_COLS = 2\n", "        self.MAX_COLS = 7\n", "        self.MAX_COLS_INNER = 3\n", "        self.MAX_COLS_OUTER = 3\n", "        self.STATE = 42\n", "        \n", "    ''' prepare dataframe '''\n", "    def prepare(self,df):\n", "        df.columns = self.format_columns(df.columns)\n", "        nan_value = float(\"NaN\")\n", "        df.replace(\"\", \"-\", inplace=True)\n", "        df.replace(\"NaN\",\"-\", inplace=True)\n", "        df.dropna(inplace=True)\n", "        df = df.iloc[:300,:]\n", "        df = df.astype(str)\n", "        return df\n", "    \n", "    ''' load dataframes '''\n", "    def load(self,path):\n", "        fnames = glob.glob(path)\n", "        data = []\n", "        for fname in fnames:\n", "            df = pd.read_csv(fname,encoding=\"utf-8\")\n", "            df = self.prepare(df)\n", "            for c in df.columns:\n", "                df[c] = df[c].apply(self.reduce)\n", "            data.append(df)\n", "        return data\n", "    \n", "    ''' reduce rows '''\n", "    def reduce(self,x:str)->str:\n", "        if type(x) is not str:\n", "            return x\n", "        if len(x)>12:\n", "            return x[:12]\n", "        return x\n", "    \n", "    ''' format column headers '''\n", "    def format_columns(self,columns):\n", "        new_columns = []\n", "        for c in columns:\n", "            c = c.replace(\"_\",\" \")\n", "            c = c.title()\n", "            if len(c)>12:\n", "                c = c.split(' ')[0][:12]\n", "            new_columns.append(c)\n", "        return new_columns\n", "    \n", "    ''' shuffle '''\n", "    def shuffle(self):\n", "        p = np.random.permutation(self.N)\n", "        self.data = [self.data[p[i]] for i in range(len(p))]\n", "    \n", "    ''' select a dataframe '''\n", "    def select(self,i,rows,cols):\n", "        df = self.data[i].copy()\n", "        df = df.sample(frac=1,random_state=self.STATE)\n", "        df = df.iloc[:rows,np.random.permutation(cols)]\n", "        df = df.iloc[:,:cols]\n", "        return df\n", "        \n", "    ''' sample for simple tables'''\n", "    def sample(self,n,mode=0):\n", "        sample = []\n", "        p = np.random.permutation(n)\n", "        max_rows = self.MAX_ROWS//n\n", "        if mode == 0:\n", "            max_cols = 7\n", "        else:\n", "            max_rows = max(self.MIN_OUTER_ROWS,max_rows//2)\n", "            max_cols = 3\n", "        for i in range(n):\n", "            rows = int(np.random.uniform(self.MIN_ROWS,max_rows+1))\n", "            cols = int(np.random.uniform(self.MIN_COLS,max_cols+1))\n", "            df = self.select(p[i],rows,cols)\n", "            sample.append(df)\n", "        self.shuffle()\n", "        return sample"]}, {"cell_type": "markdown", "id": "e863cdb0", "metadata": {}, "source": ["### Pipeline\n", "Combines all of the above to generate dataset the steps to generate a dataset are.\n", "- Given list of types generate templates (templates func)\n", "- From templates generate resulting pdf,img,mask (datum func)\n", "- Distort datum (applies transforms to img and mask)\n", "- Annotate distort datum (uses metadata generator)"]}, {"cell_type": "code", "execution_count": 20, "id": "d97a0105", "metadata": {}, "outputs": [], "source": ["class WordGeneratorPipeline:\n", "    def __init__(self,path):\n", "        self.table_writer = TableWriter()\n", "        self.word_to_pdf = WordToPdf()\n", "        self.pdf_to_img = PdfToImg()\n", "        self.mask_generator = MaskGenerator()\n", "        self.transformer = Transformer()\n", "        self.metadata_generator = MetadataGenerator()\n", "        self.data_source = DataSource(path)\n", "        \n", "    ''' generate simple templates from given dfs and types'''\n", "    def samples(self,types:List[int])->List[str]:\n", "        n = len(types)\n", "        templates = []\n", "        if n<3:\n", "            sample = self.data_source.sample(n,0)\n", "        else:\n", "            sample = self.data_source.sample(n+1,0)\n", "        outer_samples = []\n", "        inner_samples = []\n", "        for i in range(n):\n", "            index = types[i]\n", "            if index!=4:\n", "                outer_samples.append(sample[i])\n", "                inner_samples.append(None)\n", "            else:\n", "                if n == 1:\n", "                    df_outer,df_inner = self.data_source.sample(2,1)[:2]\n", "                elif n==2:\n", "                    df_outer,df_inner = self.data_source.sample(n,1)[:2]\n", "                else:\n", "                    df_outer,df_inner = self.data_source.sample(n+1,1)[:2]\n", "                outer_samples.append(df_outer)\n", "                inner_samples.append(df_inner)\n", "        \n", "        return outer_samples,inner_samples\n", "    \n", "    ''' generate a single datapoint {mask,pdf,tables,img} '''\n", "    def datum(self,types:List[int])->dict:\n", "        outer_samples,inner_samples = self.samples(types)\n", "        # step 1 pdf and outlined pdf\n", "        doc,outlined_doc = self.table_writer.write(types,outer_samples,inner_samples)\n", "        #outlined_doc = self.table_writer.write_outlined(types,outer_samples,inner_samples)\n", "        pdfs = self.word_to_pdf.docs_to_pdfs([doc])\n", "        outlined_pdfs = self.word_to_pdf.docs_to_pdfs([outlined_doc])\n", "          \n", "        \n", "        # step 2 images and masks \n", "        imgs = self.pdf_to_img.pdfs_to_imgs(pdfs)\n", "        outlined_imgs = self.pdf_to_img.pdfs_to_imgs(outlined_pdfs)\n", "        masks = self.mask_generator.masks(imgs,outlined_imgs)\n", "        \n", "        # step 3 make results\n", "        results = {\"mask\":masks[0],\"img\":imgs[0],\"pdf\":pdfs[0],'tables':doc.tables}\n", "        \n", "        return results\n", "    \n", "    ''' apply transformation using params to dirty data (img and mask) '''\n", "    def distort_datum(self,datum:dict,k:int=7,s_x:int=1,s_y:int=1,theta:float=0)->dict:\n", "        img = datum['img']\n", "        mask = datum['mask']\n", "        img = self.transformer.dirtify(img,k,s_x,s_y,theta,False)\n", "        mask = self.transformer.dirtify(mask,k,s_x,s_y,theta,True)\n", "        return img,mask\n", "    \n", "    ''' label a datum '''\n", "    def label(self,datum:dict):\n", "        mask = datum['mask']\n", "        tables = datum['tables']\n", "        metadata = self.metadata_generator.metadata(mask,tables)\n", "        return metadata\n", "    \n", "    ''' combinations of tables '''\n", "    def generate_combinations(self,types:List[str]):\n", "        combinations = []\n", "        counts = {i:0 for i in types}\n", "        for i in range(1,4):\n", "            c = itertools.combinations(types, i)\n", "            for j in c:\n", "                combinations.append(list(j))\n", "                for k in j:\n", "                    counts[k] = counts[k]+1\n", "        return counts,combinations\n", "    \n", "    ''' save datum along with its annotation '''\n", "    def save(self,datum:dict,annotation:dict,config):\n", "        _id = annotation['id']\n", "        img_path = config['img_path']+_id+'.png'\n", "        mask_path = config['mask_path']+_id+'.png'\n", "        annotation_path = config['annotation_path']+_id+'.json'\n", "        \n", "        # save img and mask\n", "        img = datum['img']\n", "        mask = datum['mask']\n", "        \n", "        cv.imwrite(img_path,img)\n", "        cv.imwrite(mask_path,mask)\n", "        \n", "        # save annotation\n", "        with open(annotation_path,'w') as f:\n", "            json.dump(annotation,f)\n", "        \n", "    ''' generate dataset '''    \n", "    def generate_data(self,config):\n", "        sample_size = config[\"sample_size\"]\n", "        types = config[\"types\"]\n", "        N = sample_size\n", "        counts,combinations = self.generate_combinations(types)\n", "        n = len(combinations)\n", "        stats = {i:0 for i in types}\n", "        _id = 0\n", "        for i in range(N):\n", "            idx = int(np.random.uniform(0,n))\n", "            sub_types = combinations[idx]\n", "            for c in sub_types:\n", "                stats[c] = stats[c]+1\n", "            datum = self.datum(sub_types)\n", "            theta = np.random.uniform(-2,2)\n", "            img,mask = self.distort_datum(datum,theta=theta)\n", "            datum['img'] = img\n", "            datum['mask'] = mask\n", "            label = self.label(datum)\n", "            label[\"id\"] = str(_id)\n", "            self.save(datum,label,config)\n", "            _id =_id+1\n", "        return _id,stats"]}, {"cell_type": "code", "execution_count": 27, "id": "e4aa34ae", "metadata": {}, "outputs": [], "source": ["path = 'sources/*.csv'\n", "config = {\n", "\"sample_size\":100,\n", "\"types\":[0,1,2,3,4],\n", "\"types_map\":{\n", "    \"0\":\"Top Bottom\",\n", "    \"1\":\"Header Bottom\",\n", "    \"2\":\"Partially Bordered\",\n", "    \"3\":\"Bordered\",\n", "    \"4\":\"Embedded\",\n", "    },\n", "\"img_path\":\"data/word/imgs/\",\n", "\"mask_path\":\"data/word/masks/\",\n", "\"annotation_path\":\"data/word/annotations/\"\n", "}"]}, {"cell_type": "code", "execution_count": 28, "id": "7a702af4", "metadata": {}, "outputs": [], "source": ["p = WordGeneratorPipeline(path)"]}, {"cell_type": "code", "execution_count": 29, "id": "52579ea3", "metadata": {}, "outputs": [{"data": {"text/plain": ["(100, {0: 41, 1: 43, 2: 40, 3: 48, 4: 45})"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["p.generate_data(config)"]}, {"cell_type": "code", "execution_count": 548, "id": "47c8c31e", "metadata": {}, "outputs": [], "source": ["# # i am not sure how you are getting your data, but you said it is a\n", "# # pandas data frame\n", "# df = pd.read_csv('sources/FullData.csv')\n", "# df = df.iloc[:5,:4]\n", "\n", "# # open an existing document\n", "# doc = docx.Document()\n", "\n", "# # add a table to the end and create a reference variable\n", "# # extra row is so we can add the header row\n", "# t = doc.add_table(df.shape[0]+1, df.shape[1],style=t_style)\n", "# # t.style = 'ColorfulShading'\n", "# # t.style._element.xml = p\n", "# # add the header rows.\n", "# for j in range(df.shape[-1]):\n", "#     t.cell(0,j).text = df.columns[j]\n", "\n", "# # add the rest of the data frame\n", "# for i in range(df.shape[0]):\n", "#     for j in range(df.shape[-1]):\n", "#         t.cell(i+1,j).text = str(df.values[i,j])\n", "\n", "# # save the doc\n", "# f = doc.save(path)"]}, {"cell_type": "code", "execution_count": null, "id": "2b74d72c", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 549, "id": "8a0c4cd0", "metadata": {}, "outputs": [], "source": ["# t.cell(0,1).add_table(1,2)"]}, {"cell_type": "code", "execution_count": 550, "id": "5b1b0659", "metadata": {}, "outputs": [], "source": ["# collect structure\n", "# jj = t._element.xml\n", "# print(jj)"]}, {"cell_type": "code", "execution_count": 551, "id": "b2c95328", "metadata": {}, "outputs": [], "source": ["# def color_row(row=1):\n", "#     'make row of cells background colored, defaults to column header row'\n", "#     for i in range(1,4):\n", "#         row = t.rows[i]\n", "        \n", "#         for cell in row.cells:\n", "#             shading_elm_2 = parse_xml(r'<w:shd {} w:fill=\"1F5C8B\"/>'.format(nsdecls('w')))\n", "#             cell._tc.get_or_add_tcPr().append(shading_elm_2)\n", "#     f = doc.save('dev.docx')"]}, {"cell_type": "code", "execution_count": 552, "id": "398b26d5", "metadata": {}, "outputs": [], "source": ["# k = t.table._element.\n", "# print(k)"]}, {"cell_type": "code", "execution_count": 553, "id": "4c1256ea", "metadata": {}, "outputs": [], "source": ["# t.table._element.xml"]}, {"cell_type": "code", "execution_count": null, "id": "a0f38349", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "e114cab4", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cf77f926", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "377561ea", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "95f0bc76", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.8"}}, "nbformat": 4, "nbformat_minor": 5}