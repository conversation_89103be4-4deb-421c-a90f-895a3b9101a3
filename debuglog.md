(table\_render) root@af094f302c81:/aipdf-mlp/renzhu/code/tablerender# python -m table\_render.main --config configs/v4\_postprocess\_background\_test.yaml --num-samples 1

python -m table\_render.main --config configs/v4\_postprocess\_background\_test.yaml --num-samples 1

2025-07-18 10:40:10,027 - \_\_main\_\_ - INFO - 正在加载配置文件: configs/v4\_postprocess\_background\_test.yaml

2025-07-18 10:40:10,036 - \_\_main\_\_ - INFO - 配置加载并验证成功

2025-07-18 10:40:10,037 - \_\_main\_\_ - INFO - 开始生成 1 个样本...

2025-07-18 10:40:10,037 - table\_render.main\_generator - INFO - 主生成器已初始化，配置种子: 42

2025-07-18 10:40:10,399 - table\_render.main\_generator - INFO - 正在生成第 1/1 个样本...

2025-07-18 10:40:10,400 - table\_render.resolver - WARNING - 样式冲突：表头和表体使用了相同的颜色组合 (文本色: #000000, 背景色: #FFFFFF)

2025-07-18 10:40:10,400 - table\_render.resolver - INFO - 开始选择背景图，目录: \['./assets/backgrounds/']

2025-07-18 10:40:10,400 - table\_render.resolver - WARNING - 背景图目录不存在: ./assets/backgrounds/

2025-07-18 10:40:10,400 - table\_render.resolver - ERROR - 选择背景图失败: 没有找到可用的背景图文件

2025-07-18 10:40:10,400 - table\_render.resolver - INFO - \[CSS\_DEBUG] 开始计算CSS参数，背景图路径: None

2025-07-18 10:40:10,401 - table\_render.resolver - ERROR - 计算CSS背景参数失败: 'NoneType' object has no attribute 'read'

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[CSS\_DEBUG] CSS参数计算成功: {'background\_width': 1200, 'background\_height': 800, 'table\_left': 200, 'table\_top': 150, 'crop\_width': 1000, 'crop\_height': 600, 'bg\_offset\_x': 50, 'bg\_offset\_y': 50}

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[DEGRADATION] 降质模糊效果已启用

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[DEGRADATION] 降质噪声效果已启用

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[DEGRADATION] 全局褪色效果已启用

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[DEGRADATION] 局部褪色效果已启用

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[DEGRADATION] 不均匀光照效果已启用

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[DEGRADATION] JPEG压缩效果已启用

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[DEGRADATION] 亮度/对比度调整效果已启用

2025-07-18 10:40:10,401 - table\_render.resolver - INFO - \[DEGRADATION] 伽马校正效果已启用

2025-07-18 10:40:10,425 - table\_render.builders.style\_builder - WARNING - 字体目录不存在: assets/fonts

2025-07-18 10:40:10,425 - table\_render.builders.style\_builder - WARNING - 字体目录不存在: assets/fonts

2025-07-18 10:40:10,425 - table\_render.builders.style\_builder - INFO - 生成sizing规则，行高配置: {}

2025-07-18 10:40:10,425 - table\_render.builders.style\_builder - INFO - 生成sizing规则，列宽配置: {}

2025-07-18 10:40:10,425 - table\_render.builders.style\_builder - WARNING - 没有特定的行高或列宽配置，只生成默认规则

2025-07-18 10:40:10,451 - table\_render.renderers.html\_renderer - INFO - \[HTML\_DEBUG] 没有接收到背景参数 (background\_params is None)

2025-07-18 10:40:10,453 - table\_render.renderers.html\_renderer - INFO - \[HTML\_DEBUG] 无背景模式视口尺寸: 1920x1080

2025-07-18 10:40:10,562 - table\_render.postprocessors.image\_augmentor - INFO - 开始创建 DegradationProcessor

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - DegradationProcessor.\_\_init\_\_ 开始执行

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - 开始初始化降质管道

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - 开始创建 StrategyScheduler

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - StrategyScheduler 创建成功: <doc\_degradation.core.scheduler.StrategyScheduler object at 0x7ff1faaee0b0>

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - strategies 初始化成功: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - 开始创建 DegradationPipe

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - DegradationPipe 创建成功: <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - processors 初始化成功: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,562 - table\_render.postprocessors.degradation\_processor - INFO - 降质管道初始化完成

2025-07-18 10:40:10,562 - table\_render.postprocessors.image\_augmentor - INFO - DegradationProcessor 创建成功

2025-07-18 10:40:10,571 - table\_render.postprocessors.degradation\_processor - INFO - 开始应用降质效果: \['blur', 'noise', 'fade\_global', 'fade\_local', 'uneven\_lighting', 'jpeg', 'darker\_brighter', 'gamma\_correction']

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - effect\_name: blur

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type: DegradationType.BLUR

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type type: <enum 'DegradationType'>

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - processors keys: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - processors keys types: \[<enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>]

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - processor result: <bound method DegradationPipe.\_apply\_blur of <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>>

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - strategy: DegradationStrategy(degradation\_type=<DegradationType.BLUR: 'blur'>, enabled=True, config={'gaussian\_blur\_kernel\_range': (7, 7), 'gaussian\_blur\_sigma\_range': (2, 3), 'motion\_blur\_kernel\_range': (7, 9), 'average\_blur\_kernel\_range': (7, 9), 'blur\_prob': 0.8})

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config: {'gaussian\_blur\_kernel\_range': (7, 7), 'gaussian\_blur\_sigma\_range': (2, 3), 'motion\_blur\_kernel\_range': (7, 9), 'average\_blur\_kernel\_range': (7, 9), 'blur\_prob': 0.8}

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config type: <class 'dict'>

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - ERROR - 降质效果 blur 应用失败: 'NoneType' object has no attribute 'get'

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - effect\_name: noise

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type: DegradationType.NOISE

2025-07-18 10:40:10,572 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type type: <enum 'DegradationType'>

2025-07-18 10:40:10,573 - table\_render.postprocessors.degradation\_processor - INFO - processors keys: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,573 - table\_render.postprocessors.degradation\_processor - INFO - processors keys types: \[<enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>]

2025-07-18 10:40:10,573 - table\_render.postprocessors.degradation\_processor - INFO - processor result: <bound method DegradationPipe.\_apply\_noise of <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>>

2025-07-18 10:40:10,573 - table\_render.postprocessors.degradation\_processor - INFO - strategy: DegradationStrategy(degradation\_type=<DegradationType.NOISE: 'noise'>, enabled=True, config={'gaussian\_noise\_std\_range': (0.5, 1), 'poisson\_noise\_scale\_range': (0.05, 0.1), 'salt\_pepper\_noise\_prob\_range': (0.1, 0.1), 'noise\_prob': 0.7})

2025-07-18 10:40:10,573 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config: {'gaussian\_noise\_std\_range': (0.5, 1), 'poisson\_noise\_scale\_range': (0.05, 0.1), 'salt\_pepper\_noise\_prob\_range': (0.1, 0.1), 'noise\_prob': 0.7}

2025-07-18 10:40:10,573 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config type: <class 'dict'>

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - effect\_name: fade\_global

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type: DegradationType.FADE\_GLOBAL

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type type: <enum 'DegradationType'>

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - processors keys: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - processors keys types: \[<enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>]

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - processor result: <bound method DegradationPipe.\_apply\_global\_fade of <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>>

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - strategy: DegradationStrategy(degradation\_type=<DegradationType.FADE\_GLOBAL: 'fade\_global'>, enabled=True, config={'fade\_global\_kernel\_range': (1, 2), 'fade\_global\_prob': 0.8})

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config: {'fade\_global\_kernel\_range': (1, 2), 'fade\_global\_prob': 0.8}

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config type: <class 'dict'>

2025-07-18 10:40:10,581 - table\_render.postprocessors.degradation\_processor - INFO - effect\_name: fade\_local

2025-07-18 10:40:10,582 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type: DegradationType.FADE\_LOCAL

2025-07-18 10:40:10,582 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type type: <enum 'DegradationType'>

2025-07-18 10:40:10,582 - table\_render.postprocessors.degradation\_processor - INFO - processors keys: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,582 - table\_render.postprocessors.degradation\_processor - INFO - processors keys types: \[<enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>]

2025-07-18 10:40:10,582 - table\_render.postprocessors.degradation\_processor - INFO - processor result: <bound method DegradationPipe.\_apply\_local\_fade of <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>>

2025-07-18 10:40:10,582 - table\_render.postprocessors.degradation\_processor - INFO - strategy: DegradationStrategy(degradation\_type=<DegradationType.FADE\_LOCAL: 'fade\_local'>, enabled=True, config={'fade\_local\_brightness\_range': (0.1, 0.3), 'fade\_local\_saturation\_range': (0.4, 0.7), 'fade\_local\_num\_blocks\_range': (3, 5), 'fade\_local\_prob': 0.2})

2025-07-18 10:40:10,582 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config: {'fade\_local\_brightness\_range': (0.1, 0.3), 'fade\_local\_saturation\_range': (0.4, 0.7), 'fade\_local\_num\_blocks\_range': (3, 5), 'fade\_local\_prob': 0.2}

2025-07-18 10:40:10,582 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config type: <class 'dict'>

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - effect\_name: uneven\_lighting

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type: DegradationType.UNEVEN\_LIGHTING

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type type: <enum 'DegradationType'>

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - processors keys: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - processors keys types: \[<enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>]

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - processor result: <bound method DegradationPipe.\_apply\_uneven\_lighting of <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>>

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - strategy: DegradationStrategy(degradation\_type=<DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, enabled=True, config={'uneven\_lighting\_range': (0.2, 0.4), 'uneven\_lighting\_prob': 0.2})

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config: {'uneven\_lighting\_range': (0.2, 0.4), 'uneven\_lighting\_prob': 0.2}

2025-07-18 10:40:10,594 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config type: <class 'dict'>

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - effect\_name: jpeg

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type: DegradationType.JPEG

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type type: <enum 'DegradationType'>

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - processors keys: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - processors keys types: \[<enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>]

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - processor result: <bound method DegradationPipe.\_apply\_jpeg\_compression of <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>>

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - strategy: DegradationStrategy(degradation\_type=<DegradationType.JPEG: 'jpeg'>, enabled=True, config={'jpeg\_quality\_range': (5, 7), 'jpeg\_prob': 0.8})

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config: {'jpeg\_quality\_range': (5, 7), 'jpeg\_prob': 0.8}

2025-07-18 10:40:10,600 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config type: <class 'dict'>

2025-07-18 10:40:10,601 - table\_render.postprocessors.degradation\_processor - ERROR - 降质效果 jpeg 应用失败: 'NoneType' object has no attribute 'get'

2025-07-18 10:40:10,601 - table\_render.postprocessors.degradation\_processor - INFO - effect\_name: darker\_brighter

2025-07-18 10:40:10,601 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type: DegradationType.DARKER\_BRIGHTER

2025-07-18 10:40:10,601 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type type: <enum 'DegradationType'>

2025-07-18 10:40:10,601 - table\_render.postprocessors.degradation\_processor - INFO - processors keys: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,601 - table\_render.postprocessors.degradation\_processor - INFO - processors keys types: \[<enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>]

2025-07-18 10:40:10,602 - table\_render.postprocessors.degradation\_processor - INFO - processor result: <bound method DegradationPipe.\_apply\_darker\_brighter of <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>>

2025-07-18 10:40:10,602 - table\_render.postprocessors.degradation\_processor - INFO - strategy: DegradationStrategy(degradation\_type=<DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, enabled=True, config={'darker\_bright\_brightness\_range': (1.1, 1.3), 'darker\_bright\_contrast\_range': (1.5, 2.0), 'darker\_brighter\_prob': 0.5})

2025-07-18 10:40:10,602 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config: {'darker\_bright\_brightness\_range': (1.1, 1.3), 'darker\_bright\_contrast\_range': (1.5, 2.0), 'darker\_brighter\_prob': 0.5}

2025-07-18 10:40:10,602 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config type: <class 'dict'>

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - effect\_name: gamma\_correction

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type: DegradationType.GAMMA\_CORRECTION

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - degradation\_type type: <enum 'DegradationType'>

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - processors keys: \[<DegradationType.BLUR: 'blur'>, <DegradationType.NOISE: 'noise'>, <DegradationType.JPEG: 'jpeg'>, <DegradationType.FADE\_GLOBAL: 'fade\_global'>, <DegradationType.FADE\_LOCAL: 'fade\_local'>, <DegradationType.LINE\_BROKEN: 'line\_broken'>, <DegradationType.UNEVEN\_LIGHTING: 'uneven\_lighting'>, <DegradationType.ARTISTIC\_INK: 'artistic\_ink'>, <DegradationType.SINC: 'sinc'>, <DegradationType.USM\_SHARPEN: 'usm\_sharpen'>, <DegradationType.RANDOM\_RESIZE: 'random\_resize'>, <DegradationType.ESRGAN\_BLUR: 'esrgan\_blur'>, <DegradationType.DARKER\_BRIGHTER: 'darker\_brighter'>, <DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, <DegradationType.IRIS\_BLUR\_LARGE: 'iris\_blur\_large'>]

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - processors keys types: \[<enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>, <enum 'DegradationType'>]

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - processor result: <bound method DegradationPipe.\_apply\_gamma\_correction of <doc\_degradation.core.degradation\_pipe.DegradationPipe object at 0x7ff1faaedb40>>

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - strategy: DegradationStrategy(degradation\_type=<DegradationType.GAMMA\_CORRECTION: 'gamma\_correction'>, enabled=True, config={'gamma\_correction\_gamma\_range': (2.0, 2.5), 'gamma\_correction\_prob': 0.5})

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config: {'gamma\_correction\_gamma\_range': (2.0, 2.5), 'gamma\_correction\_prob': 0.5}

2025-07-18 10:40:10,605 - table\_render.postprocessors.degradation\_processor - INFO - strategy.config type: <class 'dict'>

2025-07-18 10:40:10,609 - table\_render.postprocessors.degradation\_processor - INFO - 降质效果应用完成

2025-07-18 10:40:10,640 - table\_render.main\_generator - INFO - 样本 1 生成完成

2025-07-18 10:40:10,668 - table\_render.main\_generator - INFO - 所有样本生成完毕

2025-07-18 10:40:10,668 - \_\_main\_\_ - INFO - 所有样本生成完毕



