#!/usr/bin/env python3
"""
TableRender V4.5 降质功能快速验证脚本

快速验证降质功能的基本工作状态，用于开发过程中的快速检查。
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from table_render.main_generator import MainGenerator
from table_render.config import TableRenderConfig


def quick_test_basic_functionality():
    """快速测试基本功能"""
    print("🚀 TableRender V4.5 降质功能快速验证")
    print("=" * 50)
    
    config_path = "configs/v4_postprocess_background_test.yaml"
    
    try:
        print("📁 加载配置文件...")
        config = TableRenderConfig.from_yaml(config_path)
        print("✅ 配置文件加载成功")
        
        print("🔧 初始化主生成器...")
        generator = MainGenerator(config)
        print("✅ 主生成器初始化成功")
        
        print("🎨 生成测试样本...")
        start_time = time.time()
        
        generator.generate_samples(num_samples=1)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 样本生成完成 (耗时: {duration:.2f}s)")
        
        # 检查输出文件
        output_dir = Path(config.output.output_dir)
        if output_dir.exists():
            output_files = list(output_dir.glob("**/*"))
            print(f"📁 生成文件数量: {len(output_files)}")
            
            # 显示部分输出文件
            for i, file_path in enumerate(output_files[:5]):
                if file_path.is_file():
                    print(f"   - {file_path.name}")
            
            if len(output_files) > 5:
                print(f"   ... 还有 {len(output_files) - 5} 个文件")
        
        print("\n🎉 快速验证通过！降质功能基本工作正常。")
        return True
        
    except Exception as e:
        print(f"❌ 快速验证失败: {e}")
        import traceback
        print("详细错误信息:")
        traceback.print_exc()
        return False


def test_config_loading():
    """测试配置加载"""
    print("\n🔍 测试配置加载...")
    
    config_path = "configs/v4_postprocess_background_test.yaml"
    
    try:
        config = TableRenderConfig.from_yaml(config_path)
        
        # 检查降质配置是否存在
        if hasattr(config, 'postprocessing') and config.postprocessing:
            postprocessing = config.postprocessing
            
            degradation_effects = [
                'degradation_blur',
                'degradation_noise',
                'degradation_fade_global', 
                'degradation_fade_local',
                'degradation_uneven_lighting',
                'degradation_jpeg',
                'degradation_darker_brighter',
                'degradation_gamma_correction'
            ]
            
            found_effects = []
            for effect in degradation_effects:
                if hasattr(postprocessing, effect):
                    effect_config = getattr(postprocessing, effect)
                    if effect_config is not None:
                        found_effects.append(effect)
            
            print(f"✅ 找到 {len(found_effects)}/8 个降质效果配置")
            
            if len(found_effects) == 8:
                print("✅ 所有降质效果配置都存在")
                return True
            else:
                print(f"⚠️  缺少降质效果配置: {set(degradation_effects) - set(found_effects)}")
                return False
        else:
            print("❌ 未找到postprocessing配置")
            return False
            
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        return False


def test_degradation_processor():
    """测试降质处理器"""
    print("\n🔧 测试降质处理器...")
    
    try:
        from table_render.postprocessors.degradation_processor import DegradationProcessor
        
        # 创建降质处理器
        processor = DegradationProcessor(seed=42)
        print("✅ 降质处理器创建成功")
        
        # 检查效果映射
        if hasattr(processor, 'effect_mapping'):
            mapping_count = len(processor.effect_mapping)
            print(f"✅ 效果映射包含 {mapping_count} 种效果")
            
            if mapping_count == 8:
                print("✅ 效果映射完整")
                return True
            else:
                print(f"⚠️  效果映射不完整，期望8种，实际{mapping_count}种")
                return False
        else:
            print("❌ 降质处理器缺少效果映射")
            return False
            
    except ImportError as e:
        print(f"❌ 无法导入降质处理器: {e}")
        return False
    except Exception as e:
        print(f"❌ 降质处理器测试失败: {e}")
        return False


def main():
    """主函数"""
    print("开始快速验证...")
    
    # 运行各项快速测试
    tests = [
        ("配置加载", test_config_loading),
        ("降质处理器", test_degradation_processor),
        ("基本功能", quick_test_basic_functionality)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 总结结果
    print(f"\n{'='*50}")
    print("📊 快速验证总结")
    print(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有快速验证通过！系统基本功能正常。")
        return True
    else:
        print("⚠️  部分验证失败，需要检查相关功能。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
