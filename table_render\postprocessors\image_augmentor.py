"""
图像增强器实现

V4.0新增：实现具体的图像后处理效果，包括模糊、噪声、透视变换等。
"""

import io
import logging
import numpy as np
import cv2
import os
import json
from pathlib import Path
from PIL import Image, ImageFilter, ImageDraw
from typing import Optional, Dict, Any, Tuple

from .base_augmentor import BaseAugmentor
from .background_composer import BackgroundComposer
from .degradation_processor import DegradationProcessor
from ..config import ResolvedPostprocessingParams


class ImageAugmentor(BaseAugmentor):
    """
    图像增强器实现类
    
    提供模糊、噪声、透视变换等图像后处理效果。
    """
    
    def __init__(self, seed: int, debug_mode: bool = False, debug_output_dir: Optional[str] = None):
        """
        初始化图像增强器

        Args:
            seed: 随机种子，用于确保可复现性
            debug_mode: 是否启用调试模式
            debug_output_dir: 调试输出目录
        """
        self.random_state = np.random.RandomState(seed)
        self.logger = logging.getLogger(__name__)
        self.debug_mode = debug_mode
        self.debug_output_dir = debug_output_dir
        self.debug_stage_counter = 0

        # V4.5新增：初始化降质处理器
        try:
            self.degradation_processor = DegradationProcessor(seed)
        except Exception as e:
            self.logger.error(f"DegradationProcessor 创建失败: {e}")
            self.degradation_processor = None

        if self.debug_mode and self.debug_output_dir:
            # 确保调试输出目录存在
            Path(self.debug_output_dir).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"ImageAugmentor调试模式已启用，输出目录: {self.debug_output_dir}")

    def _save_debug_stage(self, stage_name: str, image: Image.Image, annotations: Optional[Dict[str, Any]],
                         additional_info: Optional[Dict[str, Any]] = None):
        """
        保存调试阶段的图像和标注

        Args:
            stage_name: 阶段名称
            image: 图像
            annotations: 标注数据
            additional_info: 额外信息
        """
        if not self.debug_mode or not self.debug_output_dir:
            return

        self.debug_stage_counter += 1
        stage_prefix = f"stage{self.debug_stage_counter:02d}_{stage_name}"

        # 保存图像
        image_path = os.path.join(self.debug_output_dir, f"{stage_prefix}.png")
        image.save(image_path)
        self.logger.info(f"调试: 保存图像 {image_path}")

        # 保存标注
        if annotations is not None:
            annotation_path = os.path.join(self.debug_output_dir, f"{stage_prefix}.json")
            debug_data = {
                "stage": stage_name,
                "image_size": image.size,
                "annotations": annotations
            }
            if additional_info:
                debug_data.update(additional_info)

            with open(annotation_path, 'w', encoding='utf-8') as f:
                json.dump(debug_data, f, indent=2, ensure_ascii=False)
            self.logger.info(f"调试: 保存标注 {annotation_path}")

        # 生成可视化图像（带标注框）
        if annotations and annotations.get('cells'):
            vis_image = self._create_visualization_image(image, annotations)
            vis_path = os.path.join(self.debug_output_dir, f"{stage_prefix}_with_boxes.png")
            vis_image.save(vis_path)
            self.logger.info(f"调试: 保存可视化图像 {vis_path}")

    def _create_visualization_image(self, image: Image.Image, annotations: Dict[str, Any]) -> Image.Image:
        """
        创建带标注框的可视化图像

        Args:
            image: 原始图像
            annotations: 标注数据

        Returns:
            带标注框的图像
        """
        vis_image = image.copy()
        draw = ImageDraw.Draw(vis_image)

        # 定义颜色
        colors = ['red', 'blue', 'green', 'orange', 'purple', 'brown', 'pink', 'gray']

        for i, cell in enumerate(annotations.get('cells', [])):
            if 'bbox' not in cell:
                continue

            bbox = cell['bbox']
            color = colors[i % len(colors)]

            if isinstance(bbox, dict) and all(key in bbox for key in ['p1', 'p2', 'p3', 'p4']):
                # 四角点格式
                points = [tuple(bbox['p1']), tuple(bbox['p2']), tuple(bbox['p3']), tuple(bbox['p4'])]
                # 绘制多边形
                draw.polygon(points, outline=color, width=2)
                # 标记单元格编号
                draw.text(bbox['p1'], str(i), fill=color)
            elif isinstance(bbox, list) and len(bbox) >= 4:
                # 矩形格式 [x_min, y_min, x_max, y_max]
                x_min, y_min, x_max, y_max = bbox[:4]
                draw.rectangle([x_min, y_min, x_max, y_max], outline=color, width=2)
                # 标记单元格编号
                draw.text((x_min, y_min), str(i), fill=color)

        return vis_image
    
    def process(self,
               image_bytes: bytes,
               annotations: Optional[Dict[str, Any]] = None,
               params: Optional[ResolvedPostprocessingParams] = None) -> Tuple[bytes, Optional[Dict[str, Any]]]:
        """
        处理图像字节数据和标注

        Args:
            image_bytes: 原始图像的字节数据
            annotations: 最终格式的标注数据（包含p1,p2,p3,p4格式），如果为None则不处理标注
            params: 解析后的后处理参数，如果为None则不进行任何处理

        Returns:
            (处理后的图像字节数据, 处理后的标注数据)
        """
        # 将字节数据转换为PIL图像
        image = Image.open(io.BytesIO(image_bytes))

        # 调试模式：即使没有后处理参数也要保存原始状态
        if self.debug_mode and self.debug_output_dir:
            self._save_debug_stage("original_input", image, annotations, {
                "stage_description": "后处理模块接收到的原始输入"
            })

        if params is None:
            # 调试模式：保存无处理的输出状态
            if self.debug_mode and self.debug_output_dir:
                self._save_debug_stage("no_processing", image, annotations, {
                    "stage_description": "无后处理参数，直接输出"
                })
            return image_bytes, annotations

        # 应用增强效果
        enhanced_image, enhanced_annotations = self.augment(image, annotations, params)

        # 将处理后的图像转换回字节数据
        output_buffer = io.BytesIO()
        enhanced_image.save(output_buffer, format='PNG')
        return output_buffer.getvalue(), enhanced_annotations
    
    def augment(self,
               image: Image.Image,
               annotations: Optional[Dict[str, Any]] = None,
               params: ResolvedPostprocessingParams = None) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        对PIL图像对象和标注进行增强处理

        Args:
            image: PIL图像对象
            annotations: 最终格式的标注数据
            params: 解析后的后处理参数

        Returns:
            (增强后的PIL图像对象, 增强后的标注数据)
        """
        import copy
        enhanced_image = image.copy()
        enhanced_annotations = copy.deepcopy(annotations) if annotations is not None else None

        # 调试模式：保存原始状态
        if self.debug_mode and self.debug_output_dir:
            self._save_debug_stage("postprocess_input", enhanced_image, enhanced_annotations, {
                "stage_description": "图像后处理输入状态"
            })

        # 按顺序应用各种效果
        if params.apply_blur and params.blur_radius is not None:
            enhanced_image = self._apply_blur(enhanced_image, params.blur_radius)
            self.logger.debug(f"应用模糊效果，半径: {params.blur_radius}")

        if params.apply_noise and params.noise_intensity is not None:
            enhanced_image = self._apply_noise(enhanced_image, params.noise_intensity)
            self.logger.debug(f"应用噪声效果，强度: {params.noise_intensity}")

        if params.apply_perspective and params.perspective_offset_ratio is not None:
            enhanced_image, enhanced_annotations = self._apply_perspective_transform_with_annotations(
                enhanced_image, enhanced_annotations, params.perspective_offset_ratio
            )
            self.logger.debug(f"应用透视变换，偏移比例: {params.perspective_offset_ratio}")

        # V4.0新增：背景图合成
        if params.apply_background and params.background_image_path is not None:
            self.logger.debug(f"开始背景图合成，输入标注: {enhanced_annotations}")
            enhanced_image, enhanced_annotations = self._apply_background_composition(
                enhanced_image, enhanced_annotations, params.background_image_path, params.max_scale_factor, params
            )
            self.logger.debug(f"背景图合成完成，输出标注: {enhanced_annotations}")
            self.logger.debug(f"应用背景图合成: {params.background_image_path}")

        # V4.5新增：应用降质效果（在所有其他后处理之后）
        if (params.apply_degradation_blur or params.apply_degradation_noise or
            params.apply_degradation_fade_global or params.apply_degradation_fade_local or
            params.apply_degradation_uneven_lighting or params.apply_degradation_jpeg or
            params.apply_degradation_darker_brighter or params.apply_degradation_gamma_correction):

            self.logger.debug("开始应用降质效果")

            # 检查降质处理器是否可用
            if self.degradation_processor is None:
                self.logger.error("降质处理器未初始化，跳过降质处理")
            else:
                # 调试模式：保存降质前状态
                if self.debug_mode and self.debug_output_dir:
                    self._save_debug_stage("before_degradation", enhanced_image, enhanced_annotations, {
                        "stage_description": "降质处理前状态"
                    })

                enhanced_image, enhanced_annotations = self.degradation_processor.apply_degradations(
                    enhanced_image, enhanced_annotations, params
                )

            # 调试模式：保存降质后状态
            if self.debug_mode and self.debug_output_dir:
                self._save_debug_stage("after_degradation", enhanced_image, enhanced_annotations, {
                    "stage_description": "降质处理后状态"
                })

            self.logger.debug("降质效果应用完成")

        # 调试模式：保存最终结果
        if self.debug_mode and self.debug_output_dir:
            self._save_debug_stage("postprocess_output", enhanced_image, enhanced_annotations, {
                "stage_description": "图像后处理最终输出"
            })

        return enhanced_image, enhanced_annotations
    
    def _apply_blur(self, image: Image.Image, radius: float) -> Image.Image:
        """
        应用高斯模糊效果
        
        Args:
            image: PIL图像对象
            radius: 模糊半径
            
        Returns:
            模糊后的图像
        """
        return image.filter(ImageFilter.GaussianBlur(radius=radius))
    
    def _apply_noise(self, image: Image.Image, intensity: int) -> Image.Image:
        """
        应用高斯噪声效果
        
        Args:
            image: PIL图像对象
            intensity: 噪声强度
            
        Returns:
            添加噪声后的图像
        """
        # 将PIL图像转换为numpy数组
        img_array = np.array(image)
        
        # 生成高斯噪声
        noise = self.random_state.normal(0, intensity, img_array.shape)
        
        # 将噪声添加到图像上，并确保像素值在有效范围内
        noisy_img_array = np.clip(img_array + noise, 0, 255).astype(np.uint8)
        
        # 转换回PIL图像
        return Image.fromarray(noisy_img_array)

    def _apply_perspective_transform_with_annotations(self,
                                                    image: Image.Image,
                                                    annotations: Optional[Dict[str, Any]],
                                                    max_offset_ratio: float) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        应用透视变换效果并同时处理标注

        Args:
            image: PIL图像对象
            annotations: 最终格式的标注数据
            max_offset_ratio: 最大偏移比例（相对于图像短边）

        Returns:
            (透视变换后的图像, 变换后的标注数据)
        """
        # 将PIL图像转换为OpenCV格式
        img_cv = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
        h, w = img_cv.shape[:2]

        # 计算最大偏移量
        max_offset = min(h, w) * max_offset_ratio

        # 定义原始图像的四个角点
        src_points = np.float32([
            [0, 0],           # 左上
            [w - 1, 0],       # 右上
            [0, h - 1],       # 左下
            [w - 1, h - 1]    # 右下
        ])

        # 为每个角点添加随机偏移，创建目标角点
        dst_points = np.float32([
            [
                self.random_state.uniform(-max_offset, max_offset),
                self.random_state.uniform(-max_offset, max_offset)
            ],  # 左上
            [
                w - 1 + self.random_state.uniform(-max_offset, max_offset),
                self.random_state.uniform(-max_offset, max_offset)
            ],  # 右上
            [
                self.random_state.uniform(-max_offset, max_offset),
                h - 1 + self.random_state.uniform(-max_offset, max_offset)
            ],  # 左下
            [
                w - 1 + self.random_state.uniform(-max_offset, max_offset),
                h - 1 + self.random_state.uniform(-max_offset, max_offset)
            ]   # 右下
        ])

        # 计算透视变换矩阵
        matrix = cv2.getPerspectiveTransform(src_points, dst_points)

        # 应用透视变换
        warped_img = cv2.warpPerspective(img_cv, matrix, (w, h))

        # 转换回PIL图像格式
        warped_img_rgb = cv2.cvtColor(warped_img, cv2.COLOR_BGR2RGB)
        warped_image = Image.fromarray(warped_img_rgb)

        # 处理标注坐标变换
        transformed_annotations = self._transform_annotations(annotations, matrix)

        return warped_image, transformed_annotations

    def _transform_annotations(self, annotations: Optional[Dict[str, Any]], perspective_matrix: np.ndarray) -> Optional[Dict[str, Any]]:
        """
        将透视变换应用到标注坐标上

        Args:
            annotations: 最终格式的标注数据
            perspective_matrix: 透视变换矩阵

        Returns:
            变换后的标注数据
        """
        if annotations is None:
            return None

        # 深拷贝标注数据以避免修改原始数据
        import copy
        transformed_annotations = copy.deepcopy(annotations)

        # 对每个单元格的四个角点应用透视变换
        for i, cell in enumerate(transformed_annotations.get('cells', [])):
            bbox = cell.get('bbox', {})

            # 验证bbox格式
            required_points = ['p1', 'p2', 'p3', 'p4']
            if not all(point in bbox for point in required_points):
                self.logger.warning(f"单元格{i}的bbox缺少必要的角点，跳过透视变换: {bbox}")
                continue

            # 提取四个角点坐标
            corners = np.array([
                [bbox['p1']],
                [bbox['p2']],
                [bbox['p3']],
                [bbox['p4']]
            ], dtype=np.float32)

            self.logger.debug(f"透视变换前单元格{i}: {bbox}")

            # 应用透视变换
            transformed_corners = cv2.perspectiveTransform(corners, perspective_matrix)

            # 更新标注中的坐标
            new_bbox = {
                'p1': [float(transformed_corners[0][0][0]), float(transformed_corners[0][0][1])],
                'p2': [float(transformed_corners[1][0][0]), float(transformed_corners[1][0][1])],
                'p3': [float(transformed_corners[2][0][0]), float(transformed_corners[2][0][1])],
                'p4': [float(transformed_corners[3][0][0]), float(transformed_corners[3][0][1])]
            }
            cell['bbox'] = new_bbox
            self.logger.debug(f"透视变换后单元格{i}: {new_bbox}")

        self.logger.debug(f"已变换 {len(transformed_annotations.get('cells', []))} 个单元格的标注坐标")
        return transformed_annotations

    def _apply_background_composition(self,
                                    image: Image.Image,
                                    annotations: Optional[Dict[str, Any]],
                                    background_path: str,
                                    max_scale_factor: float,
                                    transparency_config=None) -> Tuple[Image.Image, Optional[Dict[str, Any]]]:
        """
        应用背景图合成

        Args:
            image: PIL图像对象
            annotations: 标注数据
            background_path: 背景图路径
            max_scale_factor: 最大缩放倍数
            transparency_config: V4.3新增：透明度配置参数

        Returns:
            (合成后的图像, 更新后的标注)
        """
        try:
            # 创建背景图合成器，传递调试参数
            composer = BackgroundComposer(
                self.random_state.randint(0, 2**31),
                debug_mode=self.debug_mode,
                debug_output_dir=self.debug_output_dir
            )

            # 执行背景图合成
            composed_image, composed_annotations = composer.compose(
                image, annotations, background_path, max_scale_factor, transparency_config
            )

            return composed_image, composed_annotations

        except Exception as e:
            self.logger.error(f"背景图合成失败: {e}")
            # 返回原始图像和标注
            return image, annotations
