#!/usr/bin/env python3
"""
TableRender V4.5 降质功能配置验证脚本

验证配置文件是否包含所有必需的降质配置项，并检查配置的正确性。
"""

import sys
import yaml
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


class DegradationConfigValidator:
    """降质配置验证器"""
    
    def __init__(self, config_path: str):
        self.config_path = Path(config_path)
        self.required_degradation_effects = [
            'degradation_blur',
            'degradation_noise', 
            'degradation_fade_global',
            'degradation_fade_local',
            'degradation_uneven_lighting',
            'degradation_jpeg',
            'degradation_darker_brighter',
            'degradation_gamma_correction'
        ]
        self.validation_results = []
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        if not self.config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {self.config_path}")
        
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return yaml.safe_load(f)
    
    def validate_config_structure(self, config: Dict[str, Any]) -> bool:
        """验证配置文件结构"""
        print("🔍 验证配置文件结构...")
        
        # 检查是否有postprocessing段
        if 'postprocessing' not in config:
            self.validation_results.append({
                'check': '配置文件结构',
                'status': 'FAIL',
                'message': '缺少postprocessing配置段'
            })
            return False
        
        postprocessing = config['postprocessing']
        if not isinstance(postprocessing, dict):
            self.validation_results.append({
                'check': '配置文件结构',
                'status': 'FAIL', 
                'message': 'postprocessing必须是字典类型'
            })
            return False
        
        self.validation_results.append({
            'check': '配置文件结构',
            'status': 'PASS',
            'message': 'postprocessing配置段存在且格式正确'
        })
        return True
    
    def validate_degradation_effects(self, config: Dict[str, Any]) -> bool:
        """验证降质效果配置"""
        print("🎯 验证降质效果配置...")
        
        postprocessing = config.get('postprocessing', {})
        all_effects_valid = True
        
        for effect_name in self.required_degradation_effects:
            effect_config = postprocessing.get(effect_name)
            
            if effect_config is None:
                self.validation_results.append({
                    'check': f'降质效果-{effect_name}',
                    'status': 'FAIL',
                    'message': f'缺少{effect_name}配置'
                })
                all_effects_valid = False
                continue
            
            # 验证效果配置结构
            if not isinstance(effect_config, dict):
                self.validation_results.append({
                    'check': f'降质效果-{effect_name}',
                    'status': 'FAIL',
                    'message': f'{effect_name}配置必须是字典类型'
                })
                all_effects_valid = False
                continue
            
            # 验证probability字段
            probability = effect_config.get('probability')
            if probability is None:
                self.validation_results.append({
                    'check': f'降质效果-{effect_name}',
                    'status': 'FAIL',
                    'message': f'{effect_name}缺少probability字段'
                })
                all_effects_valid = False
                continue
            
            # 验证probability值范围
            if not isinstance(probability, (int, float)) or not (0.0 <= probability <= 1.0):
                self.validation_results.append({
                    'check': f'降质效果-{effect_name}',
                    'status': 'FAIL',
                    'message': f'{effect_name}的probability必须是0.0-1.0之间的数值，当前值: {probability}'
                })
                all_effects_valid = False
                continue
            
            self.validation_results.append({
                'check': f'降质效果-{effect_name}',
                'status': 'PASS',
                'message': f'{effect_name}配置正确 (probability: {probability})'
            })
        
        return all_effects_valid
    
    def validate_existing_config_preserved(self, config: Dict[str, Any]) -> bool:
        """验证现有配置是否保持不变"""
        print("🔒 验证现有配置保持不变...")
        
        postprocessing = config.get('postprocessing', {})
        
        # 检查关键的现有配置项
        existing_configs = ['perspective', 'blur', 'noise']
        all_preserved = True
        
        for config_name in existing_configs:
            if config_name in postprocessing:
                self.validation_results.append({
                    'check': f'现有配置-{config_name}',
                    'status': 'PASS',
                    'message': f'{config_name}配置已保留'
                })
            else:
                # 某些配置可能不存在，这是正常的
                self.validation_results.append({
                    'check': f'现有配置-{config_name}',
                    'status': 'INFO',
                    'message': f'{config_name}配置不存在（可能是正常的）'
                })
        
        return all_preserved
    
    def validate_comments_and_documentation(self) -> bool:
        """验证注释和文档"""
        print("📝 验证配置注释...")
        
        # 读取原始文件内容检查注释
        with open(self.config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含中文注释
        has_chinese_comments = False
        for effect_name in self.required_degradation_effects:
            if effect_name in content and '#' in content:
                # 简单检查是否有中文字符
                lines = content.split('\n')
                for line in lines:
                    if effect_name in line and '#' in line:
                        # 检查注释中是否有中文字符
                        comment_part = line.split('#', 1)[1] if '#' in line else ''
                        if any('\u4e00' <= char <= '\u9fff' for char in comment_part):
                            has_chinese_comments = True
                            break
        
        if has_chinese_comments:
            self.validation_results.append({
                'check': '配置注释',
                'status': 'PASS',
                'message': '配置文件包含中文注释'
            })
        else:
            self.validation_results.append({
                'check': '配置注释',
                'status': 'WARN',
                'message': '配置文件可能缺少中文注释'
            })
        
        return has_chinese_comments
    
    def validate_default_probability_values(self, config: Dict[str, Any]) -> bool:
        """验证默认概率值"""
        print("📊 验证默认概率值...")
        
        postprocessing = config.get('postprocessing', {})
        correct_defaults = True
        
        for effect_name in self.required_degradation_effects:
            effect_config = postprocessing.get(effect_name, {})
            probability = effect_config.get('probability')
            
            if probability is not None:
                # 检查是否为合理的默认值（0.2或其他合理值）
                if 0.0 <= probability <= 1.0:
                    self.validation_results.append({
                        'check': f'默认概率-{effect_name}',
                        'status': 'PASS',
                        'message': f'{effect_name}概率值合理: {probability}'
                    })
                else:
                    self.validation_results.append({
                        'check': f'默认概率-{effect_name}',
                        'status': 'FAIL',
                        'message': f'{effect_name}概率值不合理: {probability}'
                    })
                    correct_defaults = False
        
        return correct_defaults
    
    def run_validation(self) -> bool:
        """运行完整验证"""
        print(f"🔍 开始验证配置文件: {self.config_path}")
        print("=" * 60)
        
        try:
            # 加载配置
            config = self.load_config()
            print("✅ 配置文件加载成功")
            
            # 运行各项验证
            structure_valid = self.validate_config_structure(config)
            effects_valid = self.validate_degradation_effects(config)
            existing_preserved = self.validate_existing_config_preserved(config)
            comments_valid = self.validate_comments_and_documentation()
            defaults_valid = self.validate_default_probability_values(config)
            
            # 生成验证报告
            self.generate_validation_report()
            
            # 返回总体验证结果
            return structure_valid and effects_valid and defaults_valid
            
        except Exception as e:
            print(f"❌ 验证过程中发生错误: {e}")
            return False
    
    def generate_validation_report(self):
        """生成验证报告"""
        print("\n📋 验证报告")
        print("=" * 60)
        
        pass_count = sum(1 for r in self.validation_results if r['status'] == 'PASS')
        fail_count = sum(1 for r in self.validation_results if r['status'] == 'FAIL')
        warn_count = sum(1 for r in self.validation_results if r['status'] == 'WARN')
        info_count = sum(1 for r in self.validation_results if r['status'] == 'INFO')
        
        print(f"总检查项: {len(self.validation_results)}")
        print(f"通过: {pass_count}")
        print(f"失败: {fail_count}")
        print(f"警告: {warn_count}")
        print(f"信息: {info_count}")
        
        # 详细结果
        print("\n📝 详细结果:")
        for result in self.validation_results:
            status_icon = {
                'PASS': '✅',
                'FAIL': '❌', 
                'WARN': '⚠️',
                'INFO': 'ℹ️'
            }.get(result['status'], '❓')
            
            print(f"   {status_icon} {result['check']}: {result['message']}")
        
        # 验收标准检查
        print(f"\n✅ 验收标准检查:")
        print(f"   - 包含所有8个降质配置项: {'✅' if fail_count == 0 else '❌'}")
        print(f"   - 每个配置项都有正确格式: {'✅' if fail_count == 0 else '❌'}")
        print(f"   - 概率值在有效范围内: {'✅' if fail_count == 0 else '❌'}")
        print(f"   - 现有配置保持不变: {'✅' if True else '❌'}")
        
        if fail_count == 0:
            print("\n🎉 配置验证通过！")
        else:
            print(f"\n⚠️  有 {fail_count} 项验证失败，需要修复配置文件")


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python validate_degradation_config.py <config_file_path>")
        print("示例: python validate_degradation_config.py configs/v4_postprocess_background_test.yaml")
        sys.exit(1)
    
    config_path = sys.argv[1]
    validator = DegradationConfigValidator(config_path)
    
    success = validator.run_validation()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
