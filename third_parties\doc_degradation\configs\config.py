import yaml
from typing import <PERSON>ple, List
from dataclasses import dataclass


@dataclass
class DegradationConfig:
    # 基础
    target_size_ratio: float = 0.25  # 目标尺寸比例

    # 模糊
    blur_prob: float = 0.8  # 高斯0.5，运动0.3，均值模糊0.2
    gaussian_blur_kernel_range: Tuple[int, int] = (5, 5)
    gaussian_blur_sigma_range: Tuple[float, float] = (0.5, 1)
    motion_blur_kernel_range: Tuple[int, int] = (3, 5)
    average_blur_kernel_range: Tuple[int, int] = (3, 5)

    # 噪声
    noise_prob: float = 0.7
    gaussian_noise_std_range: Tuple[float, float] = (0.5, 1)
    poisson_noise_scale_range: Tuple[float, float] = (0.05, 0.1)
    salt_pepper_noise_prob_range: Tuple[float, float] = (0.1, 0.1)

    # 全局变淡
    fade_global_prob: float = 0.8
    fade_global_kernel_range: Tuple[int, int] = (1, 2)

    # 局部变淡
    fade_local_prob: float = 0.2
    fade_local_brightness_range: Tuple[float, float] = (1.2, 1.4)
    fade_local_saturation_range: Tuple[float, float] = (0.4, 0.7)
    fade_local_num_blocks_range: Tuple[int, int] = (3, 5)

    # 部分文字断线
    line_broken_prob: float = 0.1
    line_broken_alpha_range: Tuple[float, float] = (2.0, 2.6)
    line_broken_beta_range: Tuple[float, float] = (1.1, 1.5)

    # 不均匀光照模拟
    uneven_lighting_prob: float = 0.2
    uneven_lighting_range: Tuple[float, float] = (0.2, 0.4)

    # 艺术点墨喷洒效果
    artistic_ink_drops_prob: float = 0.1
    artistic_ink_num_clusters_range: Tuple[int, int] = (2, 4)
    artistic_ink_drop_density_range: Tuple[float, float] = (0.002, 0.003)
    artistic_ink_cluster_size_range: Tuple[int, int] = (10, 40)
    artistic_ink_intensity_range: Tuple[float, float] = (0.9, 1.0)
    artistic_ink_noise_scale: float = 0.1
    artistic_ink_blur_sigma_range: Tuple[float, float] = (0.3, 1.0)

    # 几何变形
    geometric_prob: float = 0.3
    perspective_scale_range: Tuple[float, float] = (0.01, 0.05)

    # JPEG压缩
    jpeg_prob: float = 0.8
    jpeg_quality_range: Tuple[int, int] = (5, 7)  # 色彩图，5-10重， 文字图，1-5重，5-10中，10-20轻

    # Sinc filter
    sinc_prob: float = 0.5
    sinc_kernel_size_range: Tuple[int, int] = (9, 11)   #正常
    sinc_cutoff_range: Tuple[float, float] = (0.1, 0.1)

    # usm_sharpen
    usm_sharpen_prob: float = 0.5
    usm_amount_range: Tuple[float, float] = (0.5, 1.0)

    # mixed resize up and down (esrgan resize)
    # 0.6-0.8  70%
    # 0.8-0.9   30%
    random_resize_prob: Tuple[float, float] = (1.0, 0.0)
    resize_scale_range: Tuple[Tuple[float, float], Tuple[float, float]] = ((0.8, 0.9), (1.0, 1.5))
    resize_scale_range_light_heavy: Tuple[Tuple[float, float], Tuple[float, float]] = ((0.8, 0.9), (0.8, 0.9))
    resize_light_heavy_prob: Tuple[float, float] = (0.3, 0.7)

    # esrgan blur
    esrgan_blur_prob: float = 0.5
    esrgan_blur_kernel_size_list: Tuple[int, ...] = (3, 5, 7)
    esrgan_blur_kernel_type_list: Tuple[str, ...] = (
        'iso', 'aniso', 'generalized_iso', 'generalized_aniso', 'plateau_iso', 'plateau_aniso'
    )

    esrgan_blur_kernel_prob: Tuple[float, ...] = (0.45, 0.25, 0.12, 0.03, 0.12, 0.03)[::-1]
    esrgan_blur_sigma_range: Tuple[float, float] = (0.2*3, 3)
    esrgan_blur_betap_range: Tuple[float, float] = (0.5*3, 4)

    # mix blur
    blur_mix_prob: float = 0.5

    # noise mix
    noise_mix_prob: float = 0.5

    # darker_bright
    darker_bright_prob: float = 0.5
    darker_bright_contrast_range: Tuple[float, float] = (1.5, 2.0)
    darker_bright_brightness_range: Tuple[float, float] = (1.1, 1.3)

    # gamma
    gamma_prob: float = 0.5
    gamma_range: Tuple[float, float] = (2.0, 2.5)

    # iris_blur_large
    iris_blur_max_size: int = 4000
    clear_ratio_range: Tuple[float, float] = (0.1, 0.3)      # 清晰区域比例
    blur_ratio_range: Tuple[float, float] = (0.001, 0.004)    # 模糊强度比例
    gradient_ratio_range: Tuple[float, float] = (0.1, 0.3)   # 渐变区域比例

    # light_heavy_prob
    random_resize_light_heavy_prob: float = 0.5  # 轻中度的概率

    @classmethod
    def from_yaml(cls, yaml_path: str) -> 'DegradationConfig':
        with open(yaml_path, 'r') as f:
            config_dict = yaml.safe_load(f)

        # 校验配置字典是否与类属性一致，如果有遗漏或者格式问题，可以处理
        init_kwargs = {k: v for k, v in config_dict.items() if k in cls.__annotations__}
        return cls(**init_kwargs)